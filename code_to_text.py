import os


def extract_code_and_structure(file_list_arg):
    # Path for the output file
    output_path = 'frontend.txt'

    # Data structures to hold content
    structure_content = []
    code_content = []

    processed_directories = set()

    # New list to hold all individual file paths
    all_files_to_process = []
    for path_arg in file_list_arg:
        if os.path.isfile(path_arg):
            all_files_to_process.append(path_arg)
        elif os.path.isdir(path_arg):
            for root, dirs, files in os.walk(path_arg):
                # Skip __pycache__ directories
                dirs[:] = [d for d in dirs if d != '__pycache__']
                
                for file in files:
                    all_files_to_process.append(os.path.join(root, file))
        else:
            print(f"Warning: Path '{path_arg}' is neither a file nor a directory and will be skipped.")

    for file_path in all_files_to_process:
        # Normalize the file path
        normalized_path = os.path.normpath(file_path)
        directory = os.path.dirname(normalized_path)
        filename = os.path.basename(normalized_path)

        # Record the directory structure
        if directory not in processed_directories:
            processed_directories.add(directory)
            # Record each level of the directory
            parts = directory.split(os.sep)
            for i, part in enumerate(parts):
                structure_content.append('    ' * i + part + '/\n')
            # Append file name under its directory
            structure_content.append('    ' * len(parts) + filename + '\n')
        else:
            # Only append file name under its directory
            structure_content.append('    ' * len(directory.split(os.sep)) + filename + '\n')

        # Extract and write the code
        try:
            with open(normalized_path, 'r', encoding='utf-8', errors='replace') as file:
                code_content.append(f"\n# File: {normalized_path}\n")
                code_content.append(file.read() + "\n")
        except IOError as e:
            print(f"Failed to read file: {normalized_path}")
            print(f"Error: {str(e)}")

    # Write everything to a single file
    with open(output_path, 'w', encoding='utf-8') as output_file:
        output_file.writelines(structure_content)
        output_file.writelines(code_content)



# file_list = [
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\main.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\data_processing.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\focal_loss.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\imbalance_handlers.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\optimization.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\oversampling.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\probability_calibration.py',
#     r'C:\Code\200_SolBot_Windsurf\06_ml\ParameterOptimizationPipeline\xgboost_training.py'
# ]

# # Backend
# file_list = [
#     # r'C:\\Code\\200_SolBot_Windsurf\\backend\\config',  # Example of a folder
#     # r'C:\\Code\\200_SolBot_Windsurf\\backend\\app\\core\\security.py', # Example of a single file

#     r'C:\Code\200_SolBot_Windsurf\backend\app',
#     r'C:\Code\200_SolBot_Windsurf\backend\config',
#     r'C:\Code\200_SolBot_Windsurf\backend\listener',
#     r'C:\Code\200_SolBot_Windsurf\backend\solbot',

#     r'C:\Code\200_SolBot_Windsurf\backend\run.py',

# ]

# Frontend
file_list = [
    # r'C:\\Code\\200_SolBot_Windsurf\\frontend\\components', # Example of a folder
    # r'C:\\Code\\200_SolBot_Windsurf\\frontend\\.env.local', # Example of a single file

    r'C:\Code\200_SolBot_Windsurf\frontend\app',
    r'C:\Code\200_SolBot_Windsurf\frontend\components',
    r'C:\Code\200_SolBot_Windsurf\frontend\hooks',
    r'C:\Code\200_SolBot_Windsurf\frontend\lib',
    r'C:\Code\200_SolBot_Windsurf\frontend\types',
    r'C:\Code\200_SolBot_Windsurf\frontend\utils',

    # r'C:\\Code\\200_SolBot_Windsurf\\frontend\\.env.local',
    # r'C:\Code\200_SolBot_Windsurf\frontend\components.json',
    # r'C:\\Code\\200_SolBot_Windsurf\\frontend\\package.json',
    r'C:\Code\200_SolBot_Windsurf\frontend\tailwind.config.js',

]

extract_code_and_structure(file_list)
