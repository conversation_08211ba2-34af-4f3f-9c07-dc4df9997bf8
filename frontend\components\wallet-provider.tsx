"use client"

// Use dynamic import for wallet adapter styles instead of static import
import { ReactNode, useMemo, useState, useEffect } from "react"
import { ConnectionProvider, WalletProvider } from "@solana/wallet-adapter-react"
import { PhantomWalletAdapter } from "@solana/wallet-adapter-phantom"
import { WalletModalProvider } from "@solana/wallet-adapter-react-ui"

export function WalletContextProvider({ children }: { children: ReactNode }) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Removed dynamic import of wallet adapter styles

  const endpoint = useMemo(() => {
    const envEndpoint = process.env.NEXT_PUBLIC_SOLANA_RPC_ENDPOINT;
    if (!envEndpoint) {
      console.warn("NEXT_PUBLIC_SOLANA_RPC_ENDPOINT not set, falling back to public RPC. May encounter rate limits.");
      return "https://api.mainnet-beta.solana.com"; // Fallback
    }
    console.log("Using RPC Endpoint:", envEndpoint); // Log the endpoint being used
    return envEndpoint;
  }, []); // Empty dependency array
  
  // Wrap wallet initialization in try-catch to prevent critical failures
  const wallets = useMemo(() => {
    try {
      return [new PhantomWalletAdapter()];
    } catch (error) {
      console.error("Failed to initialize wallet adapter:", error);
      setLoadError(error instanceof Error ? error : new Error(String(error)));
      // Return empty array as fallback
      return [];
    }
  }, []);

  // Add effect to track component loading
  useEffect(() => {
    setIsLoaded(true);
    return () => {
      console.log("WalletContextProvider unmounted");
    };
  }, []);

  // If there was an error initializing wallets, render without the wallet providers
  if (loadError) {
    console.error("Wallet initialization error:", loadError);
    return (
      <div className="wallet-provider-fallback">
        {children}
      </div>
    );
  }

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect={isLoaded}>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  )
} 