# run.py 0.0859
import uvicorn
import os
import sys
from dotenv import load_dotenv

# Construct the path to the .env file relative to run.py
# run.py is in backend/, .env is also expected in backend/
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')

# Load the .env file
load_dotenv(dotenv_path=dotenv_path)
print(f"Attempting to load .env file from: {dotenv_path}") # Add print statement for debugging
# Verify if a key variable is loaded (optional debug check)
# print(f"SHYFT_API_BASE_URL from env after load_dotenv: {os.getenv('SHYFT_API_BASE_URL')}")

# Add the parent directory to sys.path to allow importing the backend package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app
from config.logging_config import (
    setup_logging,
    setup_swap_logger,
    setup_predictions_logger,
    setup_trade_logger,
    setup_price_logger
)
from datetime import datetime
import logging

# Initialize centralized logging
root_logger = setup_logging()
swap_logger = setup_swap_logger()
predictions_logger = setup_predictions_logger()
trade_logger = setup_trade_logger()
price_logger = setup_price_logger()

# Create a custom filter to prevent duplicate log messages
class DuplicateFilter(logging.Filter):
    def __init__(self):
        super().__init__()
        self._last_log = {}

    def filter(self, record):
        # Create a key based on module, line number, and message
        log_key = (record.module, record.lineno, record.getMessage())
        current_time = record.created
        
        # Check if we've seen this exact log message recently (within 0.1 seconds)
        if log_key in self._last_log:
            last_time = self._last_log[log_key]
            if current_time - last_time < 0.1:  # Within 100ms
                return False
        
        # Update last seen time
        self._last_log[log_key] = current_time
        
        # Clean up old entries (older than 1 second)
        for key in list(self._last_log.keys()):
            if current_time - self._last_log[key] > 1.0:
                del self._last_log[key]
        
        return True

# Apply the filter to the root logger to affect all loggers
root_logger.addFilter(DuplicateFilter())
predictions_logger.addFilter(DuplicateFilter())

# Initialize the FastAPI application,
app = create_app()

if __name__ == "__main__":
    # Start the Uvicorn server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=False,  # Disable-- Uvicorn's default access logs if desired
    )
