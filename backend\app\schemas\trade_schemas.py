from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List, Union, Annotated, Literal, Dict, Any
from datetime import datetime

from app.db_models import TradeTypeEnum


class TradeReportPayload(BaseModel):
    """
    Payload for reporting a trade result.
    
    The trade_type field can be a string ('BUY' or 'SELL') or a TradeTypeEnum instance.
    It will be validated and converted to a string in the standard format.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)

    user_wallet: str
    tx_signature: str  # Signature of the buy/sell tx
    status: Literal['success', 'failure']  # Use Literal for specific strings
    trade_type: str  # Accept string input, will validate to ensure it's 'BUY' or 'SELL'
    mint_address: str
    amount_token: float = Field(..., gt=0, description="Amount of token bought/sold. Must be greater than 0")
    price_sol: float = Field(..., ge=0, description="Actual executed average price for the trade. Must be non-negative")
    total_sol: float = Field(..., ge=0, description="Actual SOL cost or proceeds. Must be non-negative")
    error_message: Optional[str] = None
    sell_full_balance: Optional[bool] = False
    fee_amount_sol: Optional[float] = None
    fee_destination_wallet: Optional[str] = None

    @field_validator('trade_type')
    @classmethod
    def validate_trade_type(cls, v):
        # Ensure trade_type is either 'BUY' or 'SELL'
        if v not in ('BUY', 'SELL'):
            raise ValueError("trade_type must be either 'BUY' or 'SELL'")
        return v


class TradeReportResponse(BaseModel):
    """
    Response for trade report endpoint.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)

    message: str
    trade_id: Optional[int] = None
    holding_updated: bool = False


class TradeHistoryParams(BaseModel):
    """
    Parameters for trade history pagination.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)

    limit: int = Field(default=50, ge=1, le=100, description="Number of trades to return (max 100)")
    offset: int = Field(default=0, ge=0, description="Number of trades to skip")


class TradeItem(BaseModel):
    """
    Single trade item for history response.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True, from_attributes=True)

    id: int
    timestamp: datetime
    trade_type: str  # Changed from TradeTypeEnum to match database model
    token_mint: str
    amount_token: float
    price_sol: float
    total_sol: float
    pnl_sol: Optional[float] = None
    tx_signature: Optional[str] = None
    fee_amount_sol: Optional[float] = None
    fee_destination_wallet: Optional[str] = None


class TradeHistoryResponse(BaseModel):
    """
    Response for trade history endpoint.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)

    trades: List[TradeItem]
    total_count: int
    limit: int
    offset: int


class RiskLadderStats(BaseModel):
    """
    Risk assessment statistics for token proposals, showing probability distribution
    across different profit/loss categories.
    """
    rug_pull_pct: Optional[float] = Field(None, description="Percentage chance of Rug Pull / Total Loss")
    small_gains_pct: Optional[float] = Field(None, description="Percentage chance of Small Gains (0-100% Profit)")
    good_profit_pct: Optional[float] = Field(None, description="Percentage chance of Good Profit (100-200% Profit)")
    big_gains_pct: Optional[float] = Field(None, description="Percentage chance of Big Gains (200-500% Profit)")
    to_the_moon_pct: Optional[float] = Field(None, description="Percentage chance of To The Moon (500%+ Profit)")
    pool_sample_size: Optional[int] = Field(None, description="Number of resolved proposals in the current pool")
    last_updated_utc: Optional[str] = Field(None, description="ISO timestamp of when these stats were last calculated")
    status_message: Optional[str] = Field(None, description="Message if stats are unavailable") # For fallback


class TransactionProposalSchema(BaseModel):
    """
    Schema for transaction proposals sent over WebSockets or used in API routes.
    Represents a potential buy or sell signal to be presented to the user.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)

    type: str
    user_wallet: str
    mint_address: str
    sol_amount: Optional[float] = None
    token_amount: Optional[float] = None
    slippage_bps: Optional[int] = None
    reason: Optional[str] = None
    risk_ladder_stats: Optional[RiskLadderStats] = None
    # Additional fields that might be present in transaction proposals
    token_name: Optional[str] = None
    token_symbol: Optional[str] = None
    price_sol: Optional[float] = None
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None