import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from fastapi import FastAP<PERSON>

from app.database import async_session_factory
from app.db_models import SniperBotSimulation
from solbot.roi_calculator import calculate_actual_max_roi
from solbot.config import SNIPER_BOT_ROI_DELAY_HOURS, SNIPER_BOT_BATCH_SIZE

logger = logging.getLogger(__name__)

class SniperBotBatchResolver:
    """
    Cost-optimized batch resolver for sniper bot simulations.
    
    Instead of resolving all tokens continuously, this service resolves only
    the most recent N tokens that have passed the grace period, reducing API costs by ~95%.
    """
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.is_processing = False
    
    async def resolve_latest_batch(self) -> dict:
        """
        Resolve the latest batch of tokens that have passed the grace period.
        
        Returns:
            dict: Summary of the batch processing results
        """
        if self.is_processing:
            logger.warning("SniperBotBatchResolver: Batch processing already in progress, skipping.")
            return {"status": "skipped", "reason": "already_processing"}
        
        self.is_processing = True
        
        try:
            if not hasattr(self.app.state, 'http_session') or self.app.state.http_session is None:
                logger.error("SniperBotBatchResolver: aiohttp ClientSession not found in app state. Cannot run.")
                return {"status": "error", "reason": "no_http_session"}

            logger.info("SniperBotBatchResolver: Starting batch resolution process.")
            
            # Find tokens ready for resolution
            tokens_to_resolve = await self._get_tokens_for_resolution()
            
            if not tokens_to_resolve:
                logger.info("SniperBotBatchResolver: No tokens found for resolution.")
                return {
                    "status": "completed",
                    "tokens_processed": 0,
                    "tokens_successful": 0,
                    "tokens_failed": 0
                }
            
            logger.info(f"SniperBotBatchResolver: Found {len(tokens_to_resolve)} tokens for resolution.")
            
            # Process the batch
            results = await self._process_batch(tokens_to_resolve)
            
            logger.info(f"SniperBotBatchResolver: Batch processing completed. "
                       f"Processed: {results['tokens_processed']}, "
                       f"Successful: {results['tokens_successful']}, "
                       f"Failed: {results['tokens_failed']}")
            
            return results
            
        except Exception as e:
            logger.error(f"SniperBotBatchResolver: Error in batch resolution: {e}", exc_info=True)
            return {"status": "error", "reason": str(e)}
        finally:
            self.is_processing = False
    
    async def _get_tokens_for_resolution(self) -> List[SniperBotSimulation]:
        """Get the most recent unresolved tokens that have passed the grace period."""
        try:
            async with async_session_factory() as db:
                # Calculate cutoff time for grace period
                cutoff_time_utc = datetime.now(timezone.utc) - timedelta(hours=SNIPER_BOT_ROI_DELAY_HOURS)
                cutoff_time_naive = cutoff_time_utc.replace(tzinfo=None)
                
                # Get the most recent unresolved tokens that have passed grace period
                stmt = select(SniperBotSimulation).where(
                    SniperBotSimulation.is_resolved == False,
                    SniperBotSimulation.launch_timestamp <= cutoff_time_naive
                ).order_by(
                    SniperBotSimulation.launch_timestamp.desc()  # Most recent first
                ).limit(SNIPER_BOT_BATCH_SIZE)
                
                result = await db.execute(stmt)
                tokens = result.scalars().all()
                
                logger.debug(f"SniperBotBatchResolver: Found {len(tokens)} tokens ready for resolution.")
                return tokens
                
        except Exception as e:
            logger.error(f"SniperBotBatchResolver: Error getting tokens for resolution: {e}")
            return []
    
    async def _process_batch(self, tokens: List[SniperBotSimulation]) -> dict:
        """Process a batch of tokens and calculate their ROI."""
        tokens_processed = 0
        tokens_successful = 0
        tokens_failed = 0
        
        for token in tokens:
            try:
                logger.info(f"SniperBotBatchResolver: Processing token {token.mint_address} (ID: {token.id})")
                
                # Calculate ROI using 10th transaction as entry point
                actual_roi = await calculate_actual_max_roi(
                    self.app,
                    token.mint_address,
                    entry_price_index=9  # 10th transaction (0-based index)
                )
                
                # Update the token record
                await self._update_token_record(token.id, actual_roi)
                
                tokens_processed += 1
                if actual_roi is not None:
                    tokens_successful += 1
                    logger.info(f"SniperBotBatchResolver: Successfully resolved token {token.mint_address}. ROI: {actual_roi:.4f}")
                else:
                    tokens_failed += 1
                    logger.warning(f"SniperBotBatchResolver: Failed to calculate ROI for token {token.mint_address}")
                
                # Small delay between tokens to avoid overwhelming the API
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"SniperBotBatchResolver: Error processing token {token.mint_address}: {e}")
                tokens_processed += 1
                tokens_failed += 1
                continue
        
        return {
            "status": "completed",
            "tokens_processed": tokens_processed,
            "tokens_successful": tokens_successful,
            "tokens_failed": tokens_failed
        }
    
    async def _update_token_record(self, token_id: int, actual_roi: Optional[float]):
        """Update a token record with resolution results."""
        try:
            async with async_session_factory() as db:
                current_time_utc = datetime.now(timezone.utc)
                naive_resolution_time = current_time_utc.replace(tzinfo=None)
                
                update_payload = {
                    "is_resolved": True,
                    "resolution_timestamp": naive_resolution_time
                }
                
                if actual_roi is not None:
                    update_payload["actual_max_roi"] = actual_roi
                else:
                    # Mark as error with special ROI value
                    update_payload["actual_max_roi"] = -2.0
                
                update_stmt = update(SniperBotSimulation).where(
                    SniperBotSimulation.id == token_id
                ).values(**update_payload)
                
                await db.execute(update_stmt)
                await db.commit()
                
        except Exception as e:
            logger.error(f"SniperBotBatchResolver: Error updating token record {token_id}: {e}")


# Global instance for use by API endpoints and scheduler
_batch_resolver: Optional[SniperBotBatchResolver] = None

def get_batch_resolver(app: FastAPI) -> SniperBotBatchResolver:
    """Get or create the global batch resolver instance."""
    global _batch_resolver
    if _batch_resolver is None:
        _batch_resolver = SniperBotBatchResolver(app)
    return _batch_resolver

async def resolve_sniper_bot_batch(app: FastAPI) -> dict:
    """
    Public function to trigger batch resolution.
    Used by both manual API calls and scheduled tasks.
    """
    resolver = get_batch_resolver(app)
    return await resolver.resolve_latest_batch()
