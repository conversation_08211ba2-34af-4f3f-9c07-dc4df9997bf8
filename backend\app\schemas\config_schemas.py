from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional


class ConfigurationBase(BaseModel):
    """
    Base Pydantic model for configuration data.
    
    Contains all configuration fields except id, user_wallet, and updated_at.
    """
    max_buy_sol: Optional[float] = Field(default=None, gt=0, description="Max SOL per buy trade")
    tp_percent: Optional[float] = Field(default=None, ge=0, le=10000, description="Take profit percentage")
    sl_percent: Optional[float] = Field(default=None, ge=0, le=100, description="Stop loss percentage")
    buy_slippage_bps: Optional[int] = Field(default=150, ge=0, le=10000, description="Buy slippage in basis points")
    sell_slippage_bps: Optional[int] = Field(default=150, ge=0, le=10000, description="Sell slippage in basis points")
    priority_fee_microlamports: Optional[int] = Field(default=None, ge=0, description="Priority fee in micro-lamports per compute unit. e.g., 10000 for 0.00001 SOL/CU")
    min_unique_wallets: Optional[int] = Field(default=None, ge=1, description="Minimum unique wallets filter")
    min_total_volume: Optional[float] = Field(default=None, ge=0, description="Minimum total volume filter")
    max_holder1_percent: Optional[float] = Field(default=None, ge=0, le=100, description="Maximum top holder percentage filter")


class ConfigurationCreate(ConfigurationBase):
    """
    Pydantic model for creating a new configuration.
    
    Inherits all fields from ConfigurationBase.
    """
    pass


class ConfigurationResponse(ConfigurationBase):
    """
    Pydantic model for configuration responses.
    
    Includes all fields from ConfigurationBase plus user_wallet and updated_at.
    """
    user_wallet: str
    updated_at: datetime
    
    class Config:
        orm_mode = True  # For Pydantic v1
        # from_attributes = True  # For Pydantic v2+ 