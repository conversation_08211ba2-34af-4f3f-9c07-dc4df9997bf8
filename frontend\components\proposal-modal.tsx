import React, { useState, useEffect } from "react"
import { Tren<PERSON>Up, TrendingDown, Clock, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, Target } from "lucide-react"
import { TransactionProposalData } from "@/types/api"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import TokenChartWidget from '@/components/token-chart-widget'

export interface ProposalModalProps {
  isOpen: boolean
  onClose: () => void
  proposal: TransactionProposalData | null
  isLoading: boolean
  onApprove: () => void
  onReject: () => void
  startTime?: number | null
  // These would come from your ML models, using placeholders for now
  predictionData?: {
    roi50: number  // Probability (%) to reach 50% ROI (1.5X)
    roi500: number // Probability (%) to reach 500% ROI (5X)
    roi1000: number // Probability (%) to reach 1000%+ ROI (10X+)
  }
}

const ProposalModal: React.FC<ProposalModalProps> = ({
  isOpen,
  onClose,
  proposal,
  isLoading,
  onApprove,
  onReject,
  startTime = null,
  predictionData = { roi50: 75, roi500: 34, roi1000: 12 } // Default placeholder values
}) => {
  if (!isOpen || !proposal) return null;
  
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  
  useEffect(() => {
    if (isOpen && startTime) {
      // Initial calculation
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      
      // Set up interval for updates
      const interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
      
      // Clean up on unmount or when proposal changes
      return () => clearInterval(interval);
    } else if (!isOpen) {
      // Reset timer when modal closes
      setElapsedTime(0);
    }
  }, [isOpen, startTime, proposal?.mint_address]);
  
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'} transition-opacity duration-300`}>
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" onClick={onClose} />
      
      {/* Modal Content */}
      <div className={`bg-card border-2 ${proposal.type === 'buy' ? 'border-primary/20' : 'border-primary/20'} rounded-xl shadow-md w-full max-w-2xl h-[90vh] overflow-y-auto relative z-10 m-4`}>
        <div className="p-6 h-full flex flex-col">
          {/* Header */}
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <h3 className={`font-grotesk text-2xl font-medium leading-none tracking-tight pb-2 border-b border-border mb-2 ${proposal.type === 'buy' ? 'gradient-text-primary-to-white' : 'text-destructive'} font-bold`}>
                {proposal.type === 'buy' ? 'Buy Proposal' : 'Sell Proposal'}
              </h3>
              <div className="flex items-center gap-3">
                {startTime && (
                  <div className="flex items-center text-white/60 text-sm">
                    <Clock className="h-3.5 w-3.5 mr-1" />
                    <span>{formatTime(elapsedTime)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* ML Model Predictions */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            {/* 1.5X Prediction */}
            <div className="bg-black/20 border border-white/10 rounded-xl p-4 flex flex-col items-center">
              <div className="text-sm text-white/60 mb-1 flex items-center gap-1">
                {/* <Target className="w-4 h-4" /> */}
                <span>1.5X Probability</span>
              </div>
              <div className="text-4xl font-bold font-grotesk">{predictionData.roi50}%</div>
              {/* <div className="text-sm opacity-70 mt-1">50% ROI Probability</div> */}
            </div>
            
            {/* 5X Prediction */}
            <div className="bg-black/20 border border-white/10 rounded-xl p-4 flex flex-col items-center">
              <div className="text-sm text-white/60 mb-1 flex items-center gap-1">
                {/* <BarChart3 className="w-4 h-4" /> */}
                <span>5X Probability</span>
              </div>
              <div className="text-4xl font-bold font-grotesk">{predictionData.roi500}%</div>
              {/* <div className="text-sm opacity-70 mt-1">500% ROI Probability</div> */}
            </div>
            
            {/* 10X Prediction */}
            <div className="bg-black/20 border border-white/10 rounded-xl p-4 flex flex-col items-center">
              <div className="text-sm text-white/60 mb-1 flex items-center gap-1">
                {/* <ArrowUp className="w-4 h-4" /> */}
                <span>10X+ Probability</span>
              </div>
              <div className="text-4xl font-bold font-grotesk">{predictionData.roi1000}%</div>
              {/* <div className="text-sm opacity-70 mt-1">1000%+ ROI Probability</div> */}
            </div>
          </div>
          
          {/* Token Chart Widget */}
          <div className="flex-grow bg-card/50 rounded-xl overflow-hidden border border-white/5">
            <TokenChartWidget
              mintAddress={proposal.mint_address}
              widgetContainerId={`proposal-chart-${proposal.mint_address}`}
              className="w-full h-full min-h-[300px]"
            />
          </div>

          {/* Footer with Actions */}
          <div className="mt-6 flex items-center justify-end gap-3">
            <Button
              variant="outline"
              onClick={onReject}
              disabled={isLoading}
              size="lg"
              className="flex-1"
            >
              Reject
            </Button>
            <Button
              variant={proposal.type === 'buy' ? 'default' : 'destructive'}
              onClick={onApprove}
              disabled={isLoading}
              size="lg"
              className="flex-1"
            >
              {isLoading ? "Processing..." : proposal.type === 'buy' ? 'Buy' : 'Sell'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProposalModal 