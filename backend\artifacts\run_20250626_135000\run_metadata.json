{"run_info": {"timestamp": "20250626_135000", "mode": "test", "mode_description": "Fast validation (~5 min)", "execution_time_minutes": 0.0}, "models": {"trained_models": ["xgboost"], "best_model": "xgboost", "selection_metric": "precision"}, "data_info": {"total_samples": 10791, "training_samples": 7552, "validation_samples": 2159, "test_samples": 1080, "feature_count": 277, "split_ratios": {"train": 0.6998424613103512, "validation": 0.20007413585395237, "test": 0.10008340283569642}}, "production_files": {"required_for_inference": ["models/xgboost.joblib", "preprocessing/feature_scaler.joblib", "preprocessing/selected_features.json", "preprocessing/preprocessing_params.json"], "optional_files": ["results/test_performance.csv", "results/validation_performance.csv", "results/execution_summary.json"]}}