"use client";

import React, { useEffect, useRef } from 'react';

// Define window interface augmentation for the specific chart widget function
declare global {
    interface Window {
        createMyWidget?: (containerId: string, config: any) => void; // Use createMyWidget
    }
}

interface TokenChartWidgetProps {
    mintAddress: string | null;
    widgetContainerId: string; // Unique ID for the container per instance
    className?: string; // Allow passing custom classes for styling
}

const TokenChartWidget: React.FC<TokenChartWidgetProps> = ({
    mintAddress,
    widgetContainerId,
    className = "w-full h-full min-h-[200px] bg-slate-900/30 rounded flex items-center justify-center text-slate-500 border border-slate-700/50" // Default styling with min-height
}) => {
    const scriptId = 'moralis-chart-widget-script'; // Unique ID for the script tag
    const scriptLoadedRef = useRef(false);

    useEffect(() => {
        // Ensure running on client-side
        if (typeof window === 'undefined') {
            console.log(`[ChartWidget ${widgetContainerId}] SSR, skipping.`);
            return;
        }

        const container = document.getElementById(widgetContainerId);
        if (!container) {
            console.error(`[ChartWidget ${widgetContainerId}] Container element not found.`);
            // Attempt to find it again after a short delay, in case of race conditions
            setTimeout(() => {
                 if (!document.getElementById(widgetContainerId)) {
                      console.error(`[ChartWidget ${widgetContainerId}] Container still not found after delay.`);
                 }
            }, 100);
            return;
        }

        // Clear previous content or show loading state if no mint address
        if (!mintAddress) {
            console.log(`[ChartWidget ${widgetContainerId}] No mint address provided.`);
            container.innerHTML = '<p class="text-xs p-2">No token selected.</p>';
            return; // Don't proceed if no mint address
        }

        // Show loading state before attempting to load/render
        container.innerHTML = '<p class="text-xs p-2 animate-pulse">Loading chart...</p>';


        const loadWidget = () => {
             const currentContainer = document.getElementById(widgetContainerId); // Re-check container existence
             if (!currentContainer) {
                  console.error(`[ChartWidget ${widgetContainerId}] Container disappeared before loading widget.`);
                  return;
             }
             if (!mintAddress) { // Re-check mintAddress in case it changed while script was loading
                 console.log(`[ChartWidget ${widgetContainerId}] Mint address became null before widget load.`);
                 currentContainer.innerHTML = '<p class="text-xs p-2">No token selected.</p>';
                 return;
             }

            if (typeof window.createMyWidget === 'function') {
                console.log(`[ChartWidget ${widgetContainerId}] Calling createMyWidget for ${mintAddress}`);
                try {
                    // Destroy previous widget instance if necessary (widget might handle this internally)
                    // currentContainer.innerHTML = ''; // Clear container before creating new one

                    window.createMyWidget(widgetContainerId, {
                        autoSize: true, // Let widget handle sizing within container
                        chainId: 'solana', // Use 'solana'
                        tokenAddress: mintAddress, // Use dynamic mint address
                        // --- Include other useful config from template ---
                        showHoldersChart: false, // Or false, based on preference
                        defaultInterval: '1', // e.g., '1', '5', '15', '60', 'D'
                        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone ?? 'Etc/UTC',
                        theme: 'dark', // Use 'dark' or 'light' - 'moralis' might be specific
                        locale: 'en',
                        candleUpColor: '#00d4ff',
                        candleDownColor: '#f23c3c',
                        backgroundColor: '#0c0e12',  
                        gridColor: '#0d2035',
                        textColor: '#68738D',
                        hideLeftToolbar: true,
                        hideTopToolbar: false, // Show the top toolbar with token info
                        hideBottomToolbar: true
                        // -----------------------------------------------
                    });
                } catch (error) {
                     console.error(`[ChartWidget ${widgetContainerId}] Error calling createMyWidget:`, error);
                     if (currentContainer) currentContainer.innerHTML = '<p class="text-xs text-red-400 p-2">Error loading chart.</p>';
                }
            } else {
                console.error(`[ChartWidget ${widgetContainerId}] createMyWidget function is not defined on window.`);
                 if (currentContainer) currentContainer.innerHTML = '<p class="text-xs text-red-400 p-2">Widget function unavailable.</p>';
            }
        };

        // Function to poll for the createMyWidget function
        const pollForWidgetFunction = (maxAttempts = 20, interval = 300) => {
            let attempts = 0;
            
            const checkFunction = () => {
                if (typeof window.createMyWidget === 'function') {
                    console.log(`[ChartWidget ${widgetContainerId}] createMyWidget function found.`);
                    loadWidget();
                    return true;
                }
                
                attempts++;
                if (attempts >= maxAttempts) {
                    console.error(`[ChartWidget ${widgetContainerId}] createMyWidget not available after ${maxAttempts} attempts.`);
                    const currentContainer = document.getElementById(widgetContainerId);
                    if (currentContainer) {
                        currentContainer.innerHTML = '<p class="text-xs text-red-400 p-2">Failed to load chart widget.</p>';
                    }
                    return false;
                }
                
                console.log(`[ChartWidget ${widgetContainerId}] Waiting for createMyWidget... (${attempts}/${maxAttempts})`);
                setTimeout(checkFunction, interval);
                return false;
            };
            
            checkFunction();
        };

        if (!document.getElementById(scriptId)) {
            console.log(`[ChartWidget ${widgetContainerId}] Moralis chart script not found, loading...`);
            const script = document.createElement('script');
            script.id = scriptId;
            script.src = 'https://moralis.com/static/embed/chart.js'; // Correct script source
            script.type = 'text/javascript';
            script.async = true;
            script.onload = () => {
                 console.log(`[ChartWidget ${widgetContainerId}] Moralis chart script loaded successfully.`);
                 scriptLoadedRef.current = true;
                 // Start polling for the widget function
                 pollForWidgetFunction();
            };
            script.onerror = () => {
                console.error(`[ChartWidget ${widgetContainerId}] Failed to load the Moralis chart script.`);
                const currentContainer = document.getElementById(widgetContainerId);
                if (currentContainer) currentContainer.innerHTML = '<p class="text-xs text-red-400 p-2">Failed to load widget script.</p>';
            };
            document.body.appendChild(script);
        } else if (scriptLoadedRef.current) {
            console.log(`[ChartWidget ${widgetContainerId}] Moralis chart script already exists, polling for function.`);
            pollForWidgetFunction();
        } else {
            console.log(`[ChartWidget ${widgetContainerId}] Moralis chart script exists but may not be fully loaded.`);
            // Script exists but might not be fully initialized
            setTimeout(() => {
                 pollForWidgetFunction();
            }, 500);
        }

        // Basic cleanup: clear container when mint changes or component unmounts
        return () => {
            console.log(`[ChartWidget ${widgetContainerId}] Cleanup triggered. Mint was: ${mintAddress}`);
            const container = document.getElementById(widgetContainerId);
            if (container) {
                 container.innerHTML = ''; // Clear the container
            }
        };

    }, [mintAddress, widgetContainerId]); // Rerun effect if these change

    // Render the container div
    return (
        <div id={widgetContainerId} className={className}>
           {/* Initial placeholder - gets replaced by widget or error message */}
           <p className="text-xs p-2">{mintAddress ? `Loading chart for ${mintAddress.substring(0,6)}...` : "No token selected."}</p>
        </div>
    );
};

export default TokenChartWidget; 