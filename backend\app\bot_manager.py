import asyncio
import logging
from solbot.prediction import run_user_bot_session
from app.crud.config_crud import config_crud
from app.crud.bot_state_crud import set_bot_status
from app.database import async_session_factory
from app.schemas.bot_schemas import BotStatusEnum
from app.user_context import UserBotContext
from fastapi import FastAPI

logger = logging.getLogger(__name__)

class UserBotManager:
    def __init__(self):
        self.active_bots: dict[str, asyncio.Task] = {}
        self.user_contexts: dict[str, UserBotContext] = {}
        self._lock = asyncio.Lock()  # Add a lock for thread safety
        self.app = None  # Store app reference
        
    async def start_bot(self, user_wallet: str, app: FastAPI = None):
        async with self._lock:
            if user_wallet in self.active_bots:
                logger.info(f"Bot already running for {user_wallet}")
                return False  # Indicate already running
            
            # Store app reference if provided
            if app:
                self.app = app
                
            # Fetch the user's configuration from the DB
            async with async_session_factory() as db:
                user_config_db = await config_crud.get_configuration_by_user(db, user_wallet)
            
            if not user_config_db:
                logger.error(f"[{user_wallet}] No configuration found. Cannot start bot.")
                # Set DB state back to Error
                async with async_session_factory() as db:
                    await set_bot_status(db, user_wallet, BotStatusEnum.ERROR)
                return False
            
            # Convert SQLAlchemy model to dict for the bot function
            user_config = {c.name: getattr(user_config_db, c.name) for c in user_config_db.__table__.columns}
            logger.info(f"[{user_wallet}] Loaded configuration for bot session.")
            
            # Validate required configuration parameters
            if user_config.get('max_buy_sol') is None:
                logger.error(f"[{user_wallet}] Configuration is missing required max_buy_sol parameter. Cannot start bot.")
                async with async_session_factory() as db:
                    await set_bot_status(db, user_wallet, BotStatusEnum.ERROR)
                return False
                
            if user_config.get('tp_percent') is None:
                logger.error(f"[{user_wallet}] Configuration is missing required tp_percent parameter. Cannot start bot.")
                async with async_session_factory() as db:
                    await set_bot_status(db, user_wallet, BotStatusEnum.ERROR)
                return False
                
            if user_config.get('sl_percent') is None:
                logger.error(f"[{user_wallet}] Configuration is missing required sl_percent parameter. Cannot start bot.")
                async with async_session_factory() as db:
                    await set_bot_status(db, user_wallet, BotStatusEnum.ERROR)
                return False
            
            # Create user context for this bot session
            user_context = UserBotContext(user_wallet)
            self.user_contexts[user_wallet] = user_context
            
            # Reset session stats at the start of a new bot session
            user_context.reset_session_stats()
            
            # Create the actual bot session task
            logger.info(f"Creating bot task for {user_wallet}")
            bot_task = asyncio.create_task(self._run_bot_wrapper(user_wallet, user_config, user_context, self.app))
            self.active_bots[user_wallet] = bot_task
            logger.info(f"Actual bot task started for {user_wallet}")
            return True  # Indicate started
    
    async def stop_bot(self, user_wallet: str):
        async with self._lock:
            task = self.active_bots.pop(user_wallet, None)
            if task:
                logger.info(f"Attempting to stop bot task for {user_wallet}")
                task.cancel()
                try:
                    await asyncio.wait_for(task, timeout=5.0)  # Wait briefly for cleanup
                except asyncio.CancelledError:
                    logger.info(f"Bot task for {user_wallet} cancelled successfully.")
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout waiting for bot task {user_wallet} to cancel.")
                except Exception as e:
                    logger.error(f"Error during task cancellation for {user_wallet}: {e}")
                
                # Clean up the user context
                self.user_contexts.pop(user_wallet, None)
                logger.info(f"Cleaned up context for {user_wallet}")
                return True  # Indicate stopped
            else:
                logger.info(f"No active bot task found for {user_wallet} to stop.")
                return False  # Indicate not found
    
    async def stop_all_bots(self):
        """
        Stop all currently running bots and update their state in the database to STOPPED.
        Used during server shutdown or connection loss.
        
        Returns:
            int: Number of bots stopped
        """
        logger.info("Stopping all active bots...")
        count = 0
        
        # Get a copy of the keys to avoid modifying during iteration
        async with self._lock:
            active_wallets = list(self.active_bots.keys())
        
        # Stop each bot and update its database state
        for wallet in active_wallets:
            try:
                # Update database state to STOPPED
                async with async_session_factory() as db:
                    await set_bot_status(db, wallet, BotStatusEnum.STOPPED)
                
                # Stop the bot task
                stopped = await self.stop_bot(wallet)
                if stopped:
                    count += 1
                    logger.info(f"Successfully stopped bot for {wallet} during shutdown")
                else:
                    logger.warning(f"Bot for {wallet} was already stopped")
            except Exception as e:
                logger.error(f"Error stopping bot for {wallet} during shutdown: {e}")
        
        logger.info(f"Stopped {count} active bots during shutdown")
        return count
        
    async def _run_bot_wrapper(self, user_wallet: str, user_config: dict, user_context: UserBotContext, app: FastAPI = None):
        """
        Wrapper that executes the actual bot session and handles completion or errors.
        
        Args:
            user_wallet: str - The wallet address of the user
            user_config: dict - The user's configuration
            user_context: UserBotContext - Context for the bot session
            app: FastAPI - FastAPI app instance (optional)
        """
        # Keep track of the final status to set at the end
        final_status = BotStatusEnum.STOPPED
        
        try:
            # Run the actual bot prediction logic
            logger.info(f"Starting bot session for {user_wallet}")
            
            # Mark bot as RUNNING in database
            async with async_session_factory() as db:
                await set_bot_status(db, user_wallet, BotStatusEnum.RUNNING)
                
            await run_user_bot_session(user_wallet, user_config, user_context, app)
            
            # Bot completed normally, set final status to STOPPED
            logger.info(f"Bot session completed normally for {user_wallet}")
            final_status = BotStatusEnum.STOPPED
            
        except asyncio.CancelledError:
            # Bot was cancelled, log this but don't set final_status here
            # It will be set by whatever stopped the bot (usually an explicit stop request)
            logger.info(f"Bot session cancelled for {user_wallet}")
            raise
            
        except Exception as e:
            # Set to ERROR state on uncaught exceptions
            logger.error(f"ERROR in bot session for {user_wallet}: {str(e)}", exc_info=True)
            final_status = BotStatusEnum.ERROR
            
        finally:
            # Clean up task entry in the manager AFTER the task finishes/errors/is cancelled
            async with self._lock:
                # Check if task is still the one we expect before removing
                # This handles cases where stop_bot might have run concurrently
                current_task = self.active_bots.get(user_wallet)
                if current_task is asyncio.current_task():
                    self.active_bots.pop(user_wallet, None)
                    self.user_contexts.pop(user_wallet, None)
                    logger.info(f"[{user_wallet}] Removed task and context from dictionaries in finally block.")
                elif current_task:
                    logger.warning(f"[{user_wallet}] Task in active_bots dict was different. Not removing.")
                else:
                    logger.info(f"[{user_wallet}] Task already removed from active_bots dict.")

        # Update the DB state only if the task wasn't cancelled externally
        # Check if the task is marked as cancelled right before DB update
        if not asyncio.current_task().cancelled():
            try:
                logger.info(f"[{user_wallet}] Setting final DB state to {final_status.value}")
                async with async_session_factory() as db:
                    await set_bot_status(db, user_wallet, final_status)
            except Exception as db_err:
                logger.error(f"[{user_wallet}] Failed to set final DB state: {db_err}")
                
    async def broadcast_buy_signal(self, mint_address: str, prediction_details: dict):
        logger.info(f"Broadcasting BUY signal for {mint_address} to active users.")
        signal_payload = (mint_address, prediction_details)
        async with self._lock:
            active_wallets = list(self.active_bots.keys()) # Get wallets with active bot tasks
            
        logger.debug(f"Attempting to broadcast buy signal to: {active_wallets}")
        # Only add to queue for users with active bot sessions
        for user_wallet in active_wallets:
            # Get context again inside loop in case state changed
            context = self.user_contexts.get(user_wallet)
            if context:
                try:
                    # Use the add_buy_signal method we implemented
                    context.add_buy_signal(signal_payload)
                    logger.debug(f"Sent buy signal for {mint_address} to user {user_wallet}")
                except Exception as e:
                    logger.error(f"Error queueing buy signal for {user_wallet}: {e}")
            else:
                logger.debug(f"User context for {user_wallet} missing during buy broadcast (likely stopped).")
                
    async def broadcast_sell_signal(self, mint_address: str):
        logger.info(f"Broadcasting SELL signal for {mint_address} to active users.")
        async with self._lock:
            active_wallets = list(self.active_bots.keys()) # Get wallets with active bot tasks
            
        logger.debug(f"Attempting to broadcast sell signal to: {active_wallets}")
        # Only add to queue for users with active bot sessions
        for user_wallet in active_wallets:
            # Get context again inside loop in case state changed
            context = self.user_contexts.get(user_wallet)
            if context:
                try:
                    # Use the add_sell_signal method we implemented
                    context.add_sell_signal(mint_address)
                    logger.debug(f"Sent sell signal for {mint_address} to user {user_wallet}")
                except Exception as e:
                    logger.error(f"Error queueing sell signal for {user_wallet}: {e}")
            else:
                logger.debug(f"User context for {user_wallet} missing during sell broadcast (likely stopped).")

# Instantiate the manager globally
bot_manager = UserBotManager() 