import { PublicKey, TransactionInstruction } from '@solana/web3.js';

// Define constants for Jupiter API endpoints if using direct fetch
const JUPITER_QUOTE_API = 'https://quote-api.jup.ag/v6/quote';
const JUPITER_SWAP_API = 'https://quote-api.jup.ag/v6/swap';

// Define known mint addresses
export const SOL_MINT = 'So11111111111111111111111111111111111111112';

export async function getJupiterQuote(
    inputMintStr: string,
    outputMintStr: string,
    amountLamportsStr: string, // Use string to handle potential BigInts
    slippageBps: number
): Promise<QuoteResponse | null> {
    console.log(`[Jupiter] Getting quote: ${amountLamportsStr} lamports of ${inputMintStr} -> ${outputMintStr} (Slippage: ${slippageBps} BPS)`);
    const params = new URLSearchParams({
        inputMint: inputMintStr,
        outputMint: outputMintStr,
        amount: amountLamportsStr,
        slippageBps: slippageBps.toString(),
        onlyDirectRoutes: 'false', // Can adjust if needed
        asLegacyTransaction: 'false', // Important for swap-instructions endpoint
        // platformFeeBps: 'YOUR_PLATFORM_FEE_BPS' // Optional: For platform fees
    });

    try {
        const response = await fetch(`${JUPITER_QUOTE_API}?${params.toString()}`);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[Jupiter] Quote API error ${response.status}: ${errorText}`);
            return null;
        }
        const quoteResponse: QuoteResponse = await response.json();
        console.log(`[Jupiter] Quote received: Est. out amount ${quoteResponse.outAmount} lamports of ${outputMintStr}`);
        // console.log("[Jupiter] Full Quote Response:", JSON.stringify(quoteResponse, null, 2)); // Verbose logging
        return quoteResponse;
    } catch (error) {
        console.error('[Jupiter] Error fetching quote:', error);
        return null;
    }
}

export async function getJupiterSwapTransaction(
    quoteResponse: QuoteResponse,
    userPublicKeyStr: string,
    treasuryWalletAddress: string,
    priorityFeeMicroLamports?: number | null
): Promise<SwapTransactionResponse | null> {
    console.log(`[Jupiter /swap] Getting swap transaction for user ${userPublicKeyStr}`);
    console.log(`[Jupiter /swap] Using platformFeeBps: 100, feeAccount: ${treasuryWalletAddress}`);
    try {
        const body: any = {
            quoteResponse,
            userPublicKey: userPublicKeyStr,
            wrapAndUnwrapSol: true, // Auto-wrap/unwrap SOL
            platformFeeBps: 100, // 1% fee
            feeAccount: treasuryWalletAddress,
            dynamicComputeUnitLimit: true, // Recommended by Jupiter for better CU estimation
        };

        if (priorityFeeMicroLamports && priorityFeeMicroLamports > 0) {
            body.computeUnitPriceMicroLamports = priorityFeeMicroLamports;
            console.log(`[Jupiter /swap] Requesting priority fee: ${priorityFeeMicroLamports} microLamports/CU`);
        } else {
            // Let Jupiter decide by omitting it
            // body.computeUnitPriceMicroLamports = 'auto';
            // console.log(`[Jupiter /swap] Using auto priority fee.`);
        }

        const response = await fetch(JUPITER_SWAP_API, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[Jupiter] Swap Transaction API error ${response.status}: ${errorText}`);
            return null;
        }

        const swapResponse: SwapTransactionResponse = await response.json();
        console.log('[Jupiter /swap] Swap transaction response received:', swapResponse);
        return swapResponse;

    } catch (error) {
        console.error('[Jupiter] Error fetching swap transaction:', error);
        return null;
    }
}

// --- Types based on Jupiter V6 API response ---

export interface QuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  priceImpactPct: number;
  routePlan: RoutePlan[];
  platformFee?: {
    amount: string;
    feeBps: number;
  };
  fee?: {
    amount: string;
    mint: string;
    pct: number;
  };
  slippageBps: number;
  contextSlot?: number;
  timeTaken?: number;
}

interface RoutePlan {
  swapInfo: SwapInfo;
  percent: number;
}

interface SwapInfo {
  ammKey: string;
  label: string;
  inputMint: string;
  outputMint: string;
  inAmount: string;
  outAmount: string;
  feeAmount: string;
  feeMint: string;
}

export interface SwapTransactionResponse {
  swapTransaction: string; // base64 encoded versioned transaction
  lastValidBlockHeight: number;
}

// Type for AddressLookupTableAccount, may need to be fetched
export interface AddressLookupTableAccountArgs {
    key: PublicKey;
    state: {
        deactivationSlot: bigint;
        lastExtendedSlot: number;
        lastExtendedSlotDistance: number;
        authority?: PublicKey;
        addresses: PublicKey[];
    };
} 