import asyncio
import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from .config import api_keys
from .utilities import API<PERSON>eyManager, start_config_watcher
from .processing import process_signatures
from .websocket import web_socket_listener
from .monitoring import monitor_accumulators
from app.states import MintState
from config.logging_config import setup_logging

setup_logging()
logger = logging.getLogger(__name__)

app = FastAPI()
api_key_manager = APIKeyManager(api_keys)

async def starter(app, mint_address: str):
    logger.debug(f"Entered starter with {mint_address}")
    if not mint_address:
        logger.error("Mint address missing.")
        raise HTTPException(status_code=400, detail="Mint address is missing")
    async with app.state.mint_states_lock:
        current_state = app.state.mint_states.get(mint_address)
        if current_state == MintState.STOPPED:
            logger.info(f"{mint_address} stopped. Skipping.")
            return
        elif current_state is None:
            app.state.mint_states[mint_address] = MintState.PENDING
            app.state.mint_activation_times[mint_address] = asyncio.get_event_loop().time()
            logger.debug(f"Set {mint_address} to PENDING with timestamp {app.state.mint_activation_times[mint_address]}")
    logger.debug(f"Adding {mint_address} to subscription queue")
    await app.state.subscription_queue.put(mint_address)
    logger.debug(f"{mint_address} added to queue")

async def log_mint_states(app):
    while True:
        async with app.state.mint_states_lock:
            states_snapshot = app.state.mint_states.copy()
        logger.debug(f"Current mint states: {states_snapshot}")
        await asyncio.sleep(60)