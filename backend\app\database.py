import os
import logging
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from solbot.config import DATABASE_URL

# Get the logger
logger = logging.getLogger(__name__)

# Database URL from centralized config
if not DATABASE_URL:
    logger.error("DATABASE_URL is not set in the config.ini file")
    raise ValueError("DATABASE_URL is not set in the config.ini file")

# Log the database connection attempt (without exposing credentials)
masked_url = DATABASE_URL
if "://" in DATABASE_URL:
    # Create a safe version for logging by masking credentials
    parts = DATABASE_URL.split("://", 1)
    if "@" in parts[1]:
        auth_server = parts[1].split("@", 1)
        masked_url = f"{parts[0]}://***:***@{auth_server[1]}"
    
logger.info(f"Connecting to database using URL: {masked_url}")

# Create async engine
async_engine = create_async_engine(
    DATABASE_URL,
    echo=False,  # Set to True for debugging SQL statements
    future=True,
    # Add connect_args for better timeout handling if using PostgreSQL
    connect_args={"timeout": 10} if "postgresql" in DATABASE_URL else {},
)

# Create async session factory
async_session_factory = async_sessionmaker(
    async_engine,
    expire_on_commit=False,
    class_=AsyncSession,
)

# Define async dependency function for FastAPI
async def get_db_session():
    """
    Async dependency function that yields a database session.
    
    This can be used in FastAPI endpoints as a dependency.
    """
    logger.debug("Database session requested")
    async with async_session_factory() as session:
        try:
            yield session
            await session.commit()
            logger.debug("Database session committed")
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error - rolled back: {str(e)}")
            raise 