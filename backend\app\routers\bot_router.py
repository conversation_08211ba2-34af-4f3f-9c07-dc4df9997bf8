from fastapi import APIRouter, Depends, HTTPException, Request, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db_session
from app.crud.bot_state_crud import get_bot_state, set_bot_status, get_bot_state_by_user
from app.schemas.bot_schemas import BotStateResponse, BotStatusEnum, BotSettingsResponse, BotStartResponse, BotStopResponse
from datetime import datetime
from app.bot_manager import bot_manager
import logging
import asyncio
from app.db_models import User
from app.dependencies import get_current_user, CustomRateLimiter, get_current_active_user, admin_required
from app.websocket_utils import send_proposal_to_user

# Get the module-level logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/bot",
    tags=["bot"],
    dependencies=[Depends(CustomRateLimiter(times=30, seconds=60))])  # 30 requests per minute

@router.get("/status", response_model=BotStateResponse)
async def get_status(db: AsyncSession = Depends(get_db_session), current_user: User = Depends(get_current_user)):
    """
    Get the current status of the trading bot for a specific user.
    
    Args:
        db: AsyncSession - The database session (from dependency).
        current_user: User - The authenticated user.
        
    Returns:
        BotStateResponse - The current state of the bot.
    """
    logger.debug(f"Getting bot status for user: {current_user.wallet_address}")
    
    try:
        state = await get_bot_state(db, current_user.wallet_address)
        
        if state is None:
            # Return default response if no state exists
            logger.debug(f"No state found for user {current_user.wallet_address}, returning default STOPPED state")
            return BotStateResponse(
                user_wallet=current_user.wallet_address, 
                status=BotStatusEnum.STOPPED, 
                last_changed=datetime.utcnow()
            )
        
        logger.debug(f"Retrieved bot state for user {current_user.wallet_address}: {state.status}")
        return state
    
    except Exception as e:
        # Log the full exception and traceback
        logger.exception(f"Error retrieving bot status for user {current_user.wallet_address}: {str(e)}")
        
        # Create a more specific error message
        error_message = str(e)
        status_code = 500
        
        # Check for specific error types
        if "connection" in error_message.lower():
            error_message = f"Database connection error: {error_message}"
        
        raise HTTPException(
            status_code=status_code,
            detail=f"Error retrieving bot status: {error_message}"
        )

@router.post("/start", response_model=BotStateResponse,
            dependencies=[Depends(CustomRateLimiter(times=10, seconds=60))])  # 10 requests per minute
async def start_bot_request(request: Request, db: AsyncSession = Depends(get_db_session), current_user: User = Depends(get_current_user)):
    """
    Request to start the trading bot for a specific user.
    
    Args:
        request: Request - The request object to access the app instance.
        db: AsyncSession - The database session (from dependency).
        current_user: User - The authenticated user.
        
    Returns:
        BotStateResponse - The updated state of the bot.
    """
    logger.debug(f"Start bot request received for user: {current_user.wallet_address}")
    
    try:
        # Set the database state to running
        updated_state = await set_bot_status(db, current_user.wallet_address, BotStatusEnum.RUNNING)
        
        # Get user configuration (could be from database or other source in the future)
        # For now, use some default values
        user_config = {
            "buy_amount": 0.01,  # Amount in SOL
            "take_profit_multiplier": 1.5,  # 50% profit target
            "stop_loss_percentage": 0.1,  # 10% stop loss
            "use_trailing_take_profit": True,
            "trailing_take_profit_deviation": 0.05,  # 5% trailing deviation
        }
        
        # Pass the app instance to the bot manager's start_bot method
        success = await bot_manager.start_bot(current_user.wallet_address, app=request.app)
        if not success:
            # Handle case where bot might already be marked as running in manager
            logger.warning(f"Bot manager reported bot already running for {current_user.wallet_address}, but DB state was just set to Running.")
        
        logger.info(f"Started bot session for user {current_user.wallet_address}")
        return updated_state
    
    except Exception as e:
        # Log the full exception and traceback
        logger.exception(f"Error starting bot for user {current_user.wallet_address}: {str(e)}")
        
        # Create a more specific error message
        error_message = str(e)
        status_code = 500
        
        # Check for specific error types
        if "connection" in error_message.lower():
            error_message = f"Database connection error: {error_message}"
        elif "already running" in error_message.lower():
            error_message = "Bot is already running"
            status_code = 400
        
        raise HTTPException(
            status_code=status_code,
            detail=f"Error starting bot: {error_message}"
        )

@router.post("/stop", response_model=BotStateResponse,
            dependencies=[Depends(CustomRateLimiter(times=10, seconds=60))])  # 10 requests per minute
async def stop_bot_request(db: AsyncSession = Depends(get_db_session), current_user: User = Depends(get_current_user)):
    """
    Request to stop the trading bot for a specific user.
    
    Args:
        db: AsyncSession - The database session (from dependency).
        current_user: User - The authenticated user.
        
    Returns:
        BotStateResponse - The updated state of the bot.
    """
    logger.debug(f"Stop bot request received for user: {current_user.wallet_address}")
    
    try:
        # Update database first
        updated_state = await set_bot_status(db, current_user.wallet_address, BotStatusEnum.STOPPED)
        
        await bot_manager.stop_bot(current_user.wallet_address)
        
        logger.info(f"Bot stopped for user {current_user.wallet_address}")
        
        return updated_state
    
    except Exception as e:
        # Log the full exception and traceback
        logger.exception(f"Error stopping bot for user {current_user.wallet_address}: {str(e)}")
        
        # Create a more specific error message
        error_message = str(e)
        status_code = 500
        
        # Check for specific error types
        if "connection" in error_message.lower():
            error_message = f"Database connection error: {error_message}"
        elif "not running" in error_message.lower():
            error_message = "Bot is not currently running"
            status_code = 400
        
        raise HTTPException(
            status_code=status_code,
            detail=f"Error stopping bot: {error_message}"
        )

@router.post("/settings", response_model=BotSettingsResponse,
            dependencies=[Depends(CustomRateLimiter(times=10, seconds=60))])  # 10 requests per minute
async def update_bot_settings(
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Update bot settings for a specific user.
    """
    logger.debug(f"Update bot settings request for user: {current_user.wallet_address}")
    # Placeholder implementation
    return BotSettingsResponse(
        user_wallet=current_user.wallet_address,
        settings={
            "buy_amount": 0.01,
            "take_profit_multiplier": 1.5,
            "stop_loss_percentage": 0.1,
            "use_trailing_take_profit": True,
            "trailing_take_profit_deviation": 0.05,
        }
    )

@router.get("/settings", response_model=BotSettingsResponse,
           dependencies=[Depends(CustomRateLimiter(times=10, seconds=60))])  # 10 requests per minute
async def get_bot_settings(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Get bot settings for a specific user.
    """
    logger.debug(f"Get bot settings request for user: {current_user.wallet_address}")
    # Placeholder implementation
    return BotSettingsResponse(
        user_wallet=current_user.wallet_address,
        settings={
            "buy_amount": 0.01,
            "take_profit_multiplier": 1.5,
            "stop_loss_percentage": 0.1,
            "use_trailing_take_profit": True,
            "trailing_take_profit_deviation": 0.05,
        }
    )

@router.post("/test-proposal/{wallet_address}")
async def send_test_proposal(
    wallet_address: str,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    user: User = Depends(admin_required)
):
    """
    DEBUG ONLY: Send a test transaction proposal to a specific wallet
    """
    app = request.app
    
    # Create a test proposal
    test_proposal = {
        'type': 'buy',
        'user_wallet': wallet_address,
        'mint_address': 'So11111111111111111111111111111111111111112',  # SOL token mint
        'sol_amount': 0.01,
        'slippage_bps': 150,
        'reason': 'DEBUG_TEST'
    }
    
    # Attempt to send the proposal
    success = await send_proposal_to_user(app, wallet_address, test_proposal)
    
    return {
        "success": success,
        "message": "Test proposal sent successfully" if success else "Failed to send test proposal",
        "wallet": wallet_address
    } 