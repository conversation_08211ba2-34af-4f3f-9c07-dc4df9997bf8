#!/usr/bin/env python3
"""
Script to reset default values for max_buy_sol, tp_percent, and sl_percent to None for existing users.

This script should be run after updating the schema and model to ensure all users start with
NULL values for these fields instead of the previous defaults.

Usage:
    python -m backend.scripts.reset_config_defaults
"""

import asyncio
import sys
import logging
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

# Add the parent directory to sys.path to allow imports
sys.path.append(".")

# Import the necessary modules from the backend
from backend.app.database import async_session_factory
from backend.app.db_models import Configuration


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def reset_config_defaults():
    """Reset default values for specified fields to None."""
    logger.info("Starting to reset default configuration values...")
    
    async with async_session_factory() as session:
        session: AsyncSession
        
        # Find all configurations with default values (old defaults were 0.01, 100.0, and 50.0)
        # But we'll reset all non-null values to ensure clean state
        update_stmt = (
            update(Configuration)
            .where(Configuration.max_buy_sol.is_not(None))
            .values(max_buy_sol=None)
        )
        result = await session.execute(update_stmt)
        max_buy_sol_updated = result.rowcount
        
        update_stmt = (
            update(Configuration)
            .where(Configuration.tp_percent.is_not(None))
            .values(tp_percent=None)
        )
        result = await session.execute(update_stmt)
        tp_percent_updated = result.rowcount
        
        update_stmt = (
            update(Configuration)
            .where(Configuration.sl_percent.is_not(None))
            .values(sl_percent=None)
        )
        result = await session.execute(update_stmt)
        sl_percent_updated = result.rowcount
        
        await session.commit()
        
        logger.info(f"Reset {max_buy_sol_updated} configurations for max_buy_sol")
        logger.info(f"Reset {tp_percent_updated} configurations for tp_percent")
        logger.info(f"Reset {sl_percent_updated} configurations for sl_percent")
        
        logger.info("Default configuration values have been reset successfully!")


if __name__ == "__main__":
    asyncio.run(reset_config_defaults()) 