import asyncio
import logging
from typing import Dict
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class MintAddressLockManager:
    """Manages per-mint_address locks to prevent race conditions in accumulator access."""
    
    def __init__(self):
        self._locks: Dict[str, asyncio.Lock] = {}
        self._manager_lock = asyncio.Lock()
    
    async def get_lock(self, mint_address: str) -> asyncio.Lock:
        """Get or create a lock for the specified mint_address."""
        async with self._manager_lock:
            if mint_address not in self._locks:
                self._locks[mint_address] = asyncio.Lock()
            return self._locks[mint_address]
    
    @asynccontextmanager
    async def acquire(self, mint_address: str):
        """Context manager for safely acquiring and releasing a mint_address lock."""
        lock = await self.get_lock(mint_address)
        acquired = False
        try:
            await lock.acquire()
            acquired = True
            logger.debug(f"Acquired lock for mint_address: {mint_address}")
            yield
        except asyncio.CancelledError:
            # Re-raise the exception after cleanup
            raise
        finally:
            if acquired:
                lock.release()
                logger.debug(f"Released lock for mint_address: {mint_address}")
    
    async def cleanup(self, mint_address: str) -> bool:
        """
        Remove a mint_address lock if it's no longer needed.
        Returns True if the lock was successfully removed.
        """
        async with self._manager_lock:
            if mint_address in self._locks:
                lock = self._locks[mint_address]
                if not lock.locked():
                    del self._locks[mint_address]
                    logger.debug(f"Removed lock for mint_address: {mint_address}")
                    return True
            return False
