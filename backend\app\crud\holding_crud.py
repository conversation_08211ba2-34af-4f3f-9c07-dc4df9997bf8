from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete, text
from sqlalchemy.exc import IntegrityError
from typing import Optional, List
from datetime import datetime
import logging

from app.db_models import Holding
from app.crud.base_crud import BaseCRUD

logger = logging.getLogger(__name__)


class HoldingCRUD(BaseCRUD[Holding]):
    """
    CRUD operations for the Holding model.
    """
    
    def __init__(self):
        super().__init__(Holding)
    
    async def get_holdings_by_user(self, db: AsyncSession, user_wallet: str) -> List[Holding]:
        """
        Get all holdings for a specific user.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The wallet address of the user
            
        Returns:
            List of Holding instances
        """
        query = select(Holding).where(Holding.user_wallet == user_wallet)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_holding(self, db: AsyncSession, user_wallet: str, token_mint: str) -> Optional[Holding]:
        """
        Get a specific holding by user wallet and token mint.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The wallet address of the user
            token_mint: The token mint address
            
        Returns:
            Holding instance if found, None otherwise
        """
        query = select(Holding).where(
            Holding.user_wallet == user_wallet,
            Holding.token_mint == token_mint
        )
        result = await db.execute(query)
        return result.scalars().first()
    
    async def update_holding_on_buy(
        self, db: AsyncSession, user_wallet: str, token_mint: str, 
        amount_bought: float, cost_sol: float
    ) -> Optional[Holding]:
        """
        Update or create a holding when a user buys tokens.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The wallet address of the user
            token_mint: The token mint address
            amount_bought: Amount of tokens bought
            cost_sol: Total cost in SOL
            
        Returns:
            Updated or created Holding instance, or None if invalid input
        """
        if amount_bought <= 0:
            logger.warning(f"Attempted to buy non-positive amount ({amount_bought}) of {token_mint} for {user_wallet}")
            return None
        
        existing_holding = await self.get_holding(db, user_wallet, token_mint)
        
        if existing_holding:
            # Update existing holding
            new_total_cost = (existing_holding.average_buy_price_sol * existing_holding.amount_held) + cost_sol
            new_total_amount = existing_holding.amount_held + amount_bought
            new_avg_price = new_total_cost / new_total_amount if new_total_amount > 1e-12 else 0
            
            existing_holding.amount_held = new_total_amount
            existing_holding.average_buy_price_sol = new_avg_price
            existing_holding.last_acquired_at = datetime.utcnow()
            
            holding_to_return = existing_holding
        else:
            # Create new holding
            avg_price = cost_sol / amount_bought if amount_bought > 1e-12 else 0
            new_holding = Holding(
                user_wallet=user_wallet,
                token_mint=token_mint,
                amount_held=amount_bought,
                average_buy_price_sol=avg_price
            )
            db.add(new_holding)
            holding_to_return = new_holding
        
        try:
            await db.commit()
            await db.refresh(holding_to_return)
            return holding_to_return
        except IntegrityError as e:
            # Handle potential race condition
            logger.warning(f"IntegrityError when updating/creating holding: {e}")
            await db.rollback()
            # Try to fetch the record again in case it was created in parallel
            if not existing_holding:
                return await self.get_holding(db, user_wallet, token_mint)
            return None
    
    async def update_holding_on_sell(
        self, db: AsyncSession, user_wallet: str, token_mint: str, amount_sold: float
    ) -> Optional[float]:
        """
        Update a holding when a user sells tokens.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The wallet address of the user
            token_mint: The token mint address
            amount_sold: Amount of tokens sold
            
        Returns:
            Remaining amount after sell, or None if holding not found
        """
        if amount_sold <= 0:
            logger.warning(f"Attempted to sell non-positive amount ({amount_sold}) of {token_mint} for {user_wallet}")
            return None
        
        existing_holding = await self.get_holding(db, user_wallet, token_mint)
        
        if not existing_holding:
            logger.warning(f"No holding found for {token_mint} for user {user_wallet} while attempting to sell")
            return None
        
        if amount_sold >= existing_holding.amount_held - 1e-12:
            # Selling all or more than held (accounting for float precision)
            logger.info(f"Selling all/more than held of {token_mint} for {user_wallet}. Deleting record.")
            await db.delete(existing_holding)
            await db.commit()
            return 0.0  # Indicate zero remaining holding
        else:
            # Partial sell
            existing_holding.amount_held -= amount_sold
            existing_holding.updated_at = datetime.utcnow()
            remaining_amount = existing_holding.amount_held
            
            await db.commit()
            await db.refresh(existing_holding)
            return remaining_amount
    
    async def delete_holding(self, db: AsyncSession, user_wallet: str, token_mint: str) -> bool:
        """Deletes a specific holding record."""
        existing_holding = await self.get_holding(db, user_wallet, token_mint)
        if existing_holding:
            logger.info(f"Deleting holding record for {token_mint} for user {user_wallet}.")
            await db.delete(existing_holding)
            await db.commit()
            return True
        else:
            logger.warning(f"Attempted to delete non-existent holding for {token_mint} for user {user_wallet}.")
            return False
    
    async def update_holding_current_price(self, db: AsyncSession, holding_id: int, current_price: float):
        """
        Update the current price for a holding.
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to update
            current_price: New current price in SOL
        """
        stmt = update(Holding).where(Holding.id == holding_id).values(
            current_price_sol=current_price,
            updated_at=datetime.utcnow()
        )
        await db.execute(stmt)
        await db.commit()
    
    async def update_holding_with_price_change(self, db: AsyncSession, holding_id: int, current_price: float):
        """
        Update the current price for a holding and reset stagnant_check_count when price changes.
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to update
            current_price: New current price in SOL
        """
        stmt = update(Holding).where(Holding.id == holding_id).values(
            current_price_sol=current_price,
            stagnant_check_count=0,
            last_successful_price_fetch=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        await db.execute(stmt)
        await db.commit()
    
    async def record_successful_price_fetch(self, db: AsyncSession, holding_id: int, current_price: float):
        """
        Record a successful price fetch that doesn't change the price but updates the timestamp.
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to update
            current_price: Current price in SOL (same as before)
        """
        stmt = update(Holding).where(Holding.id == holding_id).values(
            last_successful_price_fetch=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        await db.execute(stmt)
        await db.commit()
    
    async def increment_stagnant_check_count(self, db: AsyncSession, holding_id: int):
        """
        Increment the stagnant_check_count for a holding when price hasn't changed.
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to update
        """
        stmt = update(Holding).where(Holding.id == holding_id).values(
            stagnant_check_count=Holding.stagnant_check_count + 1,
            updated_at=datetime.utcnow()
        )
        await db.execute(stmt)
        await db.commit()
    
    async def should_increment_stagnant_count(self, db: AsyncSession, holding_id: int) -> bool:
        """
        Determine if a holding's stagnant_check_count should be incremented.
        
        This checks both:
        1. If at least one successful price fetch has occurred
        2. If the holding is past its grace period (20 seconds from creation/acquisition)
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to check
            
        Returns:
            bool: True if stagnant count should be incremented, False otherwise
        """
        query = select(Holding).where(Holding.id == holding_id)
        result = await db.execute(query)
        holding = result.scalars().first()
        
        if not holding:
            return False
            
        # Check if holding is within grace period (20 seconds from last_acquired_at)
        now = datetime.utcnow()
        grace_period_seconds = 20
        
        # If holding was acquired within the grace period, don't increment
        if holding.last_acquired_at and (now - holding.last_acquired_at).total_seconds() < grace_period_seconds:
            return False
            
        # If holding has never had a successful price fetch, don't increment
        if not holding.last_successful_price_fetch:
            return False
            
        return True
    
    async def get_holdings_by_token_mint_with_active_monitoring(self, db: AsyncSession, token_mint: str) -> List[Holding]:
        """
        Get all holdings for a specific token mint where monitoring_active is True.
        
        Args:
            db: AsyncSession for database operations
            token_mint: The token mint address
            
        Returns:
            List of Holding instances
        """
        query = select(Holding).where(
            Holding.token_mint == token_mint,
            Holding.monitoring_active == True
        )
        result = await db.execute(query)
        return result.scalars().all()
    
    async def activate_monitoring_for_holding(self, db: AsyncSession, holding_id: int) -> None:
        """
        Set monitoring_active to True and reset stagnant_check_count for a holding.
        
        Args:
            db: AsyncSession for database operations
            holding_id: ID of the holding to update
        """
        stmt = update(Holding).where(Holding.id == holding_id).values(
            monitoring_active=True,
            stagnant_check_count=0,
            updated_at=datetime.utcnow()
        )
        await db.execute(stmt)
        await db.commit()
        
    async def activate_monitoring_for_holding_by_details(self, db: AsyncSession, user_wallet: str, token_mint: str) -> None:
        """
        Set monitoring_active to True and reset stagnant_check_count for a holding by user wallet and token mint.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The wallet address of the user
            token_mint: The token mint address
        """
        holding = await self.get_holding(db, user_wallet, token_mint)
        if holding:
            await self.activate_monitoring_for_holding(db, holding.id)


# Create a singleton instance for reuse
holding_crud = HoldingCRUD() 