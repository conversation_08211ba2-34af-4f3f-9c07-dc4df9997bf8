# logging_config.py
import logging
import logging.handlers  # Added for RotatingFileHandler
import sys
import os
from datetime import datetime

# Set the log directory relative to the backend directory
_backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Parent of config is backend
LOG_DIR = os.path.join(_backend_dir, 'logs')


os.makedirs(LOG_DIR, exist_ok=True)


def setup_logging():
    # Enhanced format for production
    log_format = "%(asctime)s - %(levelname)s - [%(name)s] - [%(process)d:%(threadName)s] - %(message)s (%(filename)s:%(lineno)d)"

    # Create the root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)  # Set to INFO for production

    # Create formatter
    formatter = logging.Formatter(log_format)

    # Check if handlers are already added to avoid duplicates
    if not logger.handlers:
        # Create console handler with INFO level
        ch = logging.StreamHandler(sys.stdout)
        ch.setLevel(logging.INFO)  # INFO and above to console
        ch.setFormatter(formatter)
        logger.addHandler(ch)

        # Create ROTATING file handler with INFO level
        root_log_file = os.path.join(LOG_DIR, 'backend_app.log')  # Use a general name
        # Use RotatingFileHandler: 10MB per file, keep 5 backups
        fh = logging.handlers.RotatingFileHandler(
            root_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        fh.setLevel(logging.INFO)  # INFO and above to file
        fh.setFormatter(formatter)
        logger.addHandler(fh)

    # Suppress `httpx` and `httpcore` logs to avoid noisy logs
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    
    # Set websockets logger to INFO level to avoid ping/pong debug logs
    logging.getLogger("websockets").setLevel(logging.INFO)

    return logger


def setup_predictions_logger():
    log_predictions = logging.getLogger('predictions')
    log_predictions.setLevel(logging.INFO)  # Set level to INFO
    log_predictions.propagate = True  # Send to root handlers

    # Add a filter to prevent None or Unknown mint addresses being logged
    class MintAddressFilter(logging.Filter):
        def filter(self, record):
            if "Prediction for None" in record.msg or "Prediction for None -" in record.msg:
                return False
            return True
            
    if not any(isinstance(f, MintAddressFilter) for f in log_predictions.filters):
        log_predictions.addFilter(MintAddressFilter())

    return log_predictions


def setup_swap_logger():
    swap_logger = logging.getLogger('swap')
    swap_logger.setLevel(logging.INFO)  # Set level to INFO
    swap_logger.propagate = True  # Send to root handlers
    return swap_logger


def setup_trade_logger():
    trade_logger = logging.getLogger('trade_details')
    trade_logger.setLevel(logging.INFO)  # Set level to INFO
    trade_logger.propagate = True  # Send to root handlers
    return trade_logger


def setup_price_logger():
    price_logger = logging.getLogger('price_tracking')
    price_logger.setLevel(logging.INFO)  # Set level to INFO
    price_logger.propagate = True  # Send to root handlers
    return price_logger


def setup_holders_logger():
    holders_logger = logging.getLogger('holders')
    holders_logger.setLevel(logging.INFO)  # Set level to INFO
    holders_logger.propagate = True  # Send to root handlers
    return holders_logger