import time
import logging

logger = logging.getLogger(__name__)

def initialize_accumulator():
    return {
        'start_time': time.time(),
        'transactions': [],
        'action_counts': {},
        'unique_wallets': set(),
        'wallet_holdings': {},
        'wallet_initial_holdings': {},
        'wallets_sold_recorded': set(),
        'finalized': False,
        'finalized_time': 0
    }

def process_transaction_for_accumulator(tx, mint_address, accumulator):
    if 'finalized' in accumulator and accumulator['finalized']:
        return True
    if 'transactions' not in accumulator:
        accumulator['transactions'] = []
    accumulator['transactions'].append(tx)
    
    # Extract price information immediately to keep a running count
    if 'prices' not in accumulator:
        accumulator['prices'] = []
    if 'sol_volumes' not in accumulator:
        accumulator['sol_volumes'] = []
    if 'new_buyer_flags' not in accumulator:
        accumulator['new_buyer_flags'] = []
    if 'wallet_sold_flags' not in accumulator:
        accumulator['wallet_sold_flags'] = []
    if 'unique_wallets' not in accumulator:
        accumulator['unique_wallets'] = set()
    if 'wallet_holdings' not in accumulator:
        accumulator['wallet_holdings'] = {}
    if 'wallet_initial_holdings' not in accumulator:
        accumulator['wallet_initial_holdings'] = {}
    if 'wallets_sold_recorded' not in accumulator:
        accumulator['wallets_sold_recorded'] = set()
    if 'action_counts' not in accumulator:
        accumulator['action_counts'] = {}
    if 'wallet_addresses' not in accumulator:
        accumulator['wallet_addresses'] = []
    
    # Process transaction to extract prices without finalizing
    for action in tx.get("actions", []):
        action_type = action.get("type", "UNKNOWN")
        accumulator['action_counts'][action_type] = accumulator['action_counts'].get(action_type, 0) + 1
        if action_type == "SWAP":
            # Try to get data from tokens_swapped first
            tokens_swapped = action.get("info", {}).get("tokens_swapped", {})
            if tokens_swapped:
                in_token = tokens_swapped.get("in", {}).get("token_address")
                out_token = tokens_swapped.get("out", {}).get("token_address")
                in_amount = float(tokens_swapped.get("in", {}).get("amount", 0))
                out_amount = float(tokens_swapped.get("out", {}).get("amount", 0))
                swapper = action.get("info", {}).get("swapper", "")  # Get swapper from the correct location
            else:
                # Fall back to swaps data if tokens_swapped is not available
                swaps = action["info"].get("swaps", [])
                if not swaps:
                    continue
                swap = swaps[0]  # Take the first swap
                in_token = swap.get("in", {}).get("token_address")
                out_token = swap.get("out", {}).get("token_address")
                in_amount = float(swap.get("in", {}).get("amount", 0))
                out_amount = float(swap.get("out", {}).get("amount", 0))
                swapper = action.get("info", {}).get("swapper", "")  # Get swapper from the correct location
            
            if not in_token or not out_token:
                continue
            in_token_type = token_type(in_token)
            out_token_type = token_type(out_token)
            if in_token_type == out_token_type:
                continue
            price = None
            sol_volume = None
            token_amount = None
            if in_token_type == 0:
                price = in_amount / out_amount if out_amount != 0 else None
                sol_volume = in_amount
                token_amount = out_amount
            else:
                price = out_amount / in_amount if in_amount != 0 else None
                sol_volume = -out_amount
                token_amount = -in_amount
            is_new_buyer = False
            if swapper not in accumulator['unique_wallets']:
                accumulator['unique_wallets'].add(swapper)
                accumulator['wallet_initial_holdings'][swapper] = 0
                is_new_buyer = True
            if swapper not in accumulator['wallet_holdings']:
                accumulator['wallet_holdings'][swapper] = 0
            accumulator['wallet_holdings'][swapper] += token_amount
            wallet_sold = False
            if (swapper not in accumulator['wallets_sold_recorded'] and
                    accumulator['wallet_holdings'][swapper] <= 0 and
                    accumulator['wallet_initial_holdings'][swapper] > 0):
                wallet_sold = True
                accumulator['wallets_sold_recorded'].add(swapper)
            if price is not None:
                accumulator['prices'].append(price)
                accumulator['sol_volumes'].append(sol_volume)
                accumulator['new_buyer_flags'].append(1 if is_new_buyer else 0)
                accumulator['wallet_sold_flags'].append(1 if wallet_sold else 0)
                accumulator['wallet_addresses'].append(swapper)
    
    # Check if we have enough prices
    prices_count = len(accumulator.get('prices', []))
    if prices_count >= 100:
        # Return a special value (2) when we hit exactly 100 prices to trigger immediate completion
        # Return normal True (1) when we're over 100 prices (already triggered completion)
        return 2 if prices_count == 100 else True
    
    return False

def finalize_accumulator(accumulator, mint_address):
    # With our updated fetch_chronological_transactions and complete_data_collection functions,
    # transactions are already properly ordered chronologically (oldest first). The initial transactions
    # come from the oldest signatures, and if we need more, we fetch NEWER signatures using until_signature.
    # Therefore we should NOT re-sort the transactions - they're already in the correct order.
    # accumulator['transactions'].sort(key=lambda x: (x.get('slot', 0), x.get('blockTime', 0)))
    
    # Log first few transactions for verification of chronological order
    if len(accumulator['transactions']) > 0:
        logger.debug(f"Verifying chronological order for {mint_address} (first few transactions):")
        for idx in range(min(5, len(accumulator['transactions']))):
            tx = accumulator['transactions'][idx]
            slot = tx.get('slot', 'N/A')
            block_time = tx.get('blockTime', tx.get('timestamp', 'N/A'))
            signature = tx.get('signature', 'N/A')[:8] + '...' if tx.get('signature') else 'N/A'
            logger.debug(f"  {idx+1}. Slot: {slot}, BlockTime: {block_time}, Sig: {signature}")
    
    # Get the existing extracted data arrays
    prices = accumulator.get('prices', [])
    sol_volumes = accumulator.get('sol_volumes', [])
    new_buyer_flags = accumulator.get('new_buyer_flags', [])
    wallet_sold_flags = accumulator.get('wallet_sold_flags', [])
    wallet_addresses = accumulator.get('wallet_addresses', [])
    successful_timestamps = accumulator.get('successful_timestamps', [])
    
    # Process only the timestamps which were not extracted earlier
    for tx in accumulator['transactions']:
        if tx['status'] == 'Success':
            successful_timestamps.append(tx['timestamp'])
    
    # First and last transaction timestamps for reference
    if len(accumulator['transactions']) > 0:
        accumulator['first_transaction_timestamp'] = accumulator['transactions'][0].get('timestamp')
        accumulator['last_transaction_timestamp'] = accumulator['transactions'][-1].get('timestamp')
    
    # Update the accumulator with the aggregated data
    accumulator['prices'] = prices
    accumulator['sol_volumes'] = sol_volumes
    accumulator['new_buyer_flags'] = new_buyer_flags
    accumulator['wallet_sold_flags'] = wallet_sold_flags
    accumulator['wallet_addresses'] = wallet_addresses
    accumulator['successful_timestamps'] = successful_timestamps
    
    # Ensure we get exactly 100 prices - take the EARLIEST 100 prices
    # This is critical if we've fetched newer transactions in subsequent batches
    # We must always use the oldest 100 prices for consistent prediction
    if len(accumulator['prices']) >= 100:
        # Take the first/earliest 100 prices (transactions should already be in chronological order)
        accumulator['prices'] = accumulator['prices'][:100]
        accumulator['sol_volumes'] = accumulator['sol_volumes'][:100]
        accumulator['new_buyer_flags'] = accumulator['new_buyer_flags'][:100]
        accumulator['wallet_sold_flags'] = accumulator['wallet_sold_flags'][:100]
        accumulator['wallet_addresses'] = accumulator['wallet_addresses'][:100]
        accumulator['successful_timestamps'] = accumulator['successful_timestamps'][:100]
        
        # Update first and last transaction timestamps to match the 100 transactions we're using
        if len(accumulator['successful_timestamps']) > 0:
            accumulator['first_transaction_timestamp'] = accumulator['successful_timestamps'][0]
            accumulator['last_transaction_timestamp'] = accumulator['successful_timestamps'][-1]
        accumulator['finalized'] = True
        logger.debug(f"{mint_address} has collected exactly 100 prices. Finalized.")
    else:
        accumulator['finalized'] = False
        logger.warning(f"{mint_address} has {len(prices)} prices. Collecting more...")

from .utilities import token_type

def build_processed_data_from_accumulator(accumulator, token_of_interest):
    low = 100
    prices = accumulator['prices'][:low]
    sol_volumes = accumulator['sol_volumes'][:low]
    successful_timestamps = accumulator['successful_timestamps'][:low]
    new_buyer_flags = accumulator['new_buyer_flags'][:low]
    wallet_sold_flags = accumulator['wallet_sold_flags'][:low]
    wallet_addresses = accumulator['wallet_addresses'][:low]
    
    if len(prices) < low:
        logger.warning(f"Not enough prices for {token_of_interest}. Req: {low}, Got: {len(prices)}")
        return None
    
    # Validate token_of_interest
    if token_of_interest is None or token_of_interest == '':
        logger.error("Missing token_of_interest (mint address) in build_processed_data_from_accumulator")
        token_of_interest = 'Missing_Mint_Address'
    else:
        logger.debug(f"Building processed data for mint address: {token_of_interest}")
    
    # Calculate sol_volume_sum explicitly to ensure it's included in the data
    sol_volume_sum = sum(sol_volumes)
    logger.debug(f"Calculated sol_volume_sum for {token_of_interest}: {sol_volume_sum}")
    
    prices_dict = {f'price_{i + 1}': price for i, price in enumerate(prices)}
    sol_volumes_dict = {f'sol_volume_{i + 1}': volume for i, volume in enumerate(sol_volumes)}
    timestamps_dict = {f'timestamp_{i + 1}': timestamp for i, timestamp in enumerate(successful_timestamps)}
    new_buyer_dict = {f'new_buyer_{i + 1}': flag for i, flag in enumerate(new_buyer_flags)}
    wallet_sold_dict = {f'wallet_sold_{i + 1}': flag for i, flag in enumerate(wallet_sold_flags)}
    wallet_address_dict = {f'wallet_address_{i + 1}': addr for i, addr in enumerate(wallet_addresses)}
    
    processed_data = {
        'first_transaction_timestamp': accumulator['first_transaction_timestamp'],
        'last_transaction_timestamp': accumulator['last_transaction_timestamp'],
        'dev_bought_amount': accumulator.get('dev_bought_amount', 0),
        'mint_address': token_of_interest,
        'unique_wallets_count': len(accumulator.get('unique_wallets', set())),
        'sol_volume_sum': sol_volume_sum,  # Explicitly include the sol_volume_sum
        **accumulator['action_counts'],
        **prices_dict,
        **sol_volumes_dict,
        **timestamps_dict,
        **new_buyer_dict,
        **wallet_sold_dict,
        **wallet_address_dict
    }
    
    # Log summary of processed data
    logger.debug(f"Processed data for {token_of_interest}: {len(processed_data)} features, " 
                 f"including {len(prices_dict)} prices, {len(sol_volumes_dict)} volumes")
    
    return processed_data