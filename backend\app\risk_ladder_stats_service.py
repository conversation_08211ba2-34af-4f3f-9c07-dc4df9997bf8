import asyncio
import logging
import json
import os
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import numpy as np # For percentile calculations if needed, though sum/count is fine for percentages
from fastapi import FastAPI # For type hinting app

from app.database import async_session_factory
from app.db_models import LiveModelProposals
# Import config for RISK_LADDER_POOL_SIZE and stats file path
from solbot.config import RISK_LADDER_POOL_SIZE, LIVE_RISK_LADDER_STATS_FILE_PATH

logger = logging.getLogger(__name__)

# Ensure RISK_LADDER_POOL_SIZE and LIVE_RISK_LADDER_STATS_FILE_PATH are defined in backend/solbot/config.py
# Example in config.py:
# RISK_LADDER_POOL_SIZE = int(os.getenv('RISK_LADDER_POOL_SIZE', '100'))
# LIVE_RISK_LADDER_STATS_FILE_PATH = os.getenv('LIVE_RISK_LADDER_STATS_FILE_PATH', os.path.join(os.path.dirname(__file__), '..', 'data', 'live_risk_ladder_stats.json'))
# Make sure the 'data' directory exists or is created.

async def calculate_and_store_risk_ladder_stats_task(app: FastAPI):
    """
    Periodically calculates Risk Ladder statistics from the N most recent resolved proposals
    and stores them (e.g., in a JSON file or Redis).
    """
    logger.debug("RiskLadderStatsCalculatorService: Starting service.")
    
    # Ensure the directory for the stats file exists
    stats_dir = os.path.dirname(LIVE_RISK_LADDER_STATS_FILE_PATH)
    if not os.path.exists(stats_dir):
        try:
            os.makedirs(stats_dir, exist_ok=True)
            logger.debug(f"RiskLadderStatsCalculatorService: Created directory for stats file: {stats_dir}")
        except Exception as e:
            logger.error(f"RiskLadderStatsCalculatorService: Failed to create directory {stats_dir}: {e}")
            return # Cannot proceed without directory

    # Initial short delay before first run
    await asyncio.sleep(10)

    while True:
        try:
            logger.debug("RiskLadderStatsCalculatorService: Starting statistics calculation cycle.")
            resolved_rois: List[float] = []
            async with async_session_factory() as db:
                stmt = select(LiveModelProposals.actual_max_roi).where(
                    LiveModelProposals.is_resolved == True,
                    LiveModelProposals.actual_max_roi.is_not(None) # Only include if ROI was determined
                ).order_by(LiveModelProposals.resolution_timestamp.desc()).limit(RISK_LADDER_POOL_SIZE)
                
                result = await db.execute(stmt)
                resolved_rois = result.scalars().all()

            if not resolved_rois:
                logger.debug(f"RiskLadderStatsCalculatorService: No resolved proposals with ROI found in the pool (size {RISK_LADDER_POOL_SIZE}). Skipping stats update.")
                # Optionally, write default/empty stats to the file/cache
                default_stats = {
                    "rug_pull_pct": 0.0,
                    "small_gains_pct": 0.0,
                    "good_profit_pct": 0.0,
                    "big_gains_pct": 0.0,
                    "to_the_moon_pct": 0.0,
                    "pool_sample_size": 0,
                    "last_updated_utc": datetime.utcnow().isoformat()
                }
                try:
                    with open(LIVE_RISK_LADDER_STATS_FILE_PATH, 'w') as f:
                        json.dump(default_stats, f, indent=4)
                    logger.debug(f"RiskLadderStatsCalculatorService: Wrote default empty stats to {LIVE_RISK_LADDER_STATS_FILE_PATH}")
                except Exception as e:
                    logger.error(f"RiskLadderStatsCalculatorService: Error writing default stats file: {e}")
                
                await asyncio.sleep(60) # Check again in 1 minute
                continue

            pool_actual_size = len(resolved_rois)
            logger.debug(f"RiskLadderStatsCalculatorService: Calculating stats from a pool of {pool_actual_size} resolved proposals.")

            # Define max_roi value boundaries for each ladder rung (profit factor)
            # max_roi = 0.0 (or <0.001) is -100% total loss
            # max_roi = 0.5 is +50% profit
            # max_roi = 1.0 is +100% profit
            val_rug_pull_upper_bound = 0.001  # Captures actual_max_roi = 0.0 or very near for -100% profit
            val_small_gains_upper_bound = 1.0  # Up to (but not including) +100% profit
            val_good_profit_upper_bound = 2.0  # Up to (but not including) +200% profit
            val_big_gains_upper_bound = 5.0    # Up to (but not including) +500% profit
            # TO THE MOON is >= 5.0 (+500% profit or more)

            rug_pull_count = sum(1 for roi in resolved_rois if roi < val_rug_pull_upper_bound)
            small_gains_count = sum(1 for roi in resolved_rois if val_rug_pull_upper_bound <= roi < val_small_gains_upper_bound)
            good_profit_count = sum(1 for roi in resolved_rois if val_small_gains_upper_bound <= roi < val_good_profit_upper_bound)
            big_gains_count = sum(1 for roi in resolved_rois if val_good_profit_upper_bound <= roi < val_big_gains_upper_bound)
            to_the_moon_count = sum(1 for roi in resolved_rois if roi >= val_big_gains_upper_bound)

            current_stats = {
                "rug_pull_pct": (rug_pull_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "small_gains_pct": (small_gains_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "good_profit_pct": (good_profit_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "big_gains_pct": (big_gains_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "to_the_moon_pct": (to_the_moon_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "pool_sample_size": pool_actual_size,
                "last_updated_utc": datetime.utcnow().isoformat()
            }

            # Store these stats (e.g., to a JSON file for now)
            try:
                with open(LIVE_RISK_LADDER_STATS_FILE_PATH, 'w') as f:
                    json.dump(current_stats, f, indent=4)
                logger.debug(f"RiskLadderStatsCalculatorService: Updated live stats in {LIVE_RISK_LADDER_STATS_FILE_PATH}")
                logger.debug(f"RiskLadderStatsCalculatorService: Current Stats: {current_stats}")
            except Exception as e:
                logger.error(f"RiskLadderStatsCalculatorService: Error writing live stats file: {e}")

            await asyncio.sleep(30)  # Calculate and store stats every 30 seconds

        except asyncio.CancelledError:
            logger.debug("RiskLadderStatsCalculatorService: task cancelled.")
            break
        except Exception as e:
            logger.error(f"RiskLadderStatsCalculatorService: Error in main loop: {e}", exc_info=True)
            await asyncio.sleep(60) # Wait longer after an error
