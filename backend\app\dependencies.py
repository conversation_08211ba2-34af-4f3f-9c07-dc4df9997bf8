from typing import Optional, Union
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>Bearer
from jose import jwt, JWTError
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db_session
from app.crud.user_crud import user_crud
from app.db_models import User
from app.core.security import SECRET_KEY as JWT_SECRET_KEY, ALGORITHM as JWT_ALGORITHM
from app import RateLimitExceeded
import time
import hashlib
from typing import Callable, Dict, Any

# Create logger
logger = logging.getLogger(__name__)

# Define OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/verify")

async def get_current_user(token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db_session)) -> User:
    """
    Dependency to get the current user from JWT token.
    
    Args:
        token: JWT token from Authorization header
        db: Database session
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Decode JWT
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        # Extract subject (wallet address)
        wallet_address: str | None = payload.get("sub")
        if wallet_address is None:
            logger.warning("JWT token missing 'sub' (subject) field.")
            raise credentials_exception

        # Optional: Check token expiry ('exp' claim is handled automatically by jwt.decode)

    except JWTError as e: # Catches expired signature, invalid signature, etc.
        logger.warning(f"JWT validation error: {e}")
        raise credentials_exception from e

    # Fetch user from database based on wallet address in token
    user = await user_crud.get_by_wallet_address(db, wallet_address=wallet_address)
    if user is None:
        # This case should be rare if tokens are only issued after user creation,
        # but handles scenarios where user might be deleted after token issuance.
        logger.warning(f"User associated with JWT token not found in DB: {wallet_address}")
        raise credentials_exception

    # logger.debug(f"Authenticated user via JWT: {user.wallet_address}")
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency to get the current active user.
    
    Currently just returns the current user without additional checks,
    but can be extended later to check if the user is active/inactive.
    
    Args:
        current_user: Current authenticated user from get_current_user
        
    Returns:
        User: Current active user
    """
    return current_user 

async def admin_required(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency to check if the current user is an admin.
    
    Args:
        current_user: Current authenticated user from get_current_user
        
    Returns:
        User: Current admin user
        
    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required for this action"
        )
    return current_user

class CustomRateLimiter:
    def __init__(
        self, 
        times: int = 100, 
        seconds: Optional[int] = None,
        minutes: Optional[int] = None, 
        hours: Optional[int] = None
    ):
        self.times = times
        
        # Calculate window in seconds
        self.window = 0
        if seconds:
            self.window += seconds
        if minutes:
            self.window += minutes * 60
        if hours:
            self.window += hours * 60 * 60
            
        # Default to 1 hour if no time unit specified
        if self.window == 0:
            self.window = 3600
            
    async def __call__(self, request: Request):
        # Get the app from the request
        app = request.app
        
        # Check if our custom rate limiter is available in app state
        if not hasattr(app.state, "in_memory_limiter"):
            # If no rate limiter available, allow the request
            logger.warning("No rate limiter available, allowing request")
            return
            
        # Create a key based on client IP and path
        key = f"{request.client.host}:{request.url.path}"
        
        # Check if rate limited
        is_limited = await app.state.in_memory_limiter.is_rate_limited(
            key=key,
            limit=self.times,
            window=self.window
        )
        
        if is_limited:
            logger.warning(f"Rate limit exceeded for {key}")
            raise RateLimitExceeded(
                detail=f"Rate limit exceeded. Maximum {self.times} requests per {self.window} seconds."
            )
            
        return 