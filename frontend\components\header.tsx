"use client";

import React, { useEffect, useState } from 'react';
import { Zap, AlertCircle, ChevronUp } from 'lucide-react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { AuthButton } from './auth-button';
import { useAuth } from './auth-context';
import { Button } from './ui/button';

interface HeaderProps {
    wsStatus: 'disconnected' | 'connecting' | 'connected' | 'error' | 'unauthenticated';
    activeView: "open_positions" | "history";
    setActiveView: (view: "open_positions" | "history") => void;
}

const Header: React.FC<HeaderProps> = ({ wsStatus, activeView, setActiveView }) => {
    const { publicKey, connected } = useWallet();
    const { isAuthenticated, authUserWallet, handleLogout } = useAuth();
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true); // Ensure this runs only on the client
    }, []);

    const renderWalletButton = () => {
        if (!isClient) {
            // Render a placeholder during SSR/initial load
            return <div className="h-[36px] w-[200px] bg-white/5 rounded animate-pulse"></div>;
        }

        // Case 1: Wallet is connected AND user is authenticated
        if (connected && publicKey && isAuthenticated && authUserWallet === publicKey.toBase58()) {
            // Check for any element in the DOM containing the authentication error message
            // This will find errors without relying on specific class names
            const pageContent = document.body.textContent || '';
            if (pageContent.includes('Could not validate credentials')) {
                console.log("Auth validation failed, logging out");
                // If we have a credential validation error but we're still authenticated in state, fix the inconsistency
                setTimeout(() => handleLogout(), 0);
                
                // Return the SignIn button while we reset auth state
                return <AuthButton />;
            }
            
            return (
                <div className="flex items-center space-x-2">
                    <div className="text-sm font-medium text-foreground/80 bg-foreground/5 backdrop-blur-md px-4 py-2 rounded-full border border-border h-9 flex items-center">
                        {publicKey.toBase58().substring(0, 4)}...{publicKey.toBase58().substring(publicKey.toBase58().length - 4)}
                    </div>
                    {/* Sign Out Button */}
                    <Button
                        onClick={handleLogout}
                        variant="outline"
                        size="lg"
                        className="backdrop-blur-md px-4 py-2 text-sm font-medium whitespace-nowrap"
                    >
                        Sign Out
                    </Button>
                </div>
            );
        }
        // Case 2: Wallet is connected BUT user is NOT authenticated
        else if (connected && publicKey && !isAuthenticated) {
            // Render the "Sign In" button - reuse AuthButton's logic here
            return <AuthButton />; // AuthButton now primarily handles the SIWS flow
        }
        // Case 3: Wallet is NOT connected
        else {
            // Render the WalletMultiButton to connect
            return <WalletMultiButton className="!bg-primary !border-primary !border hover:shadow-primary !h-9 !text-sm !rounded-full transition-all duration-200" />;
        }
    }

    return (
        <header className="bg-black/20 backdrop-blur-md border-b border-border/20 h-[70px] px-8 flex items-center justify-between sticky top-0 z-50">
            {/* Left Side - Logo and Navigation */}
            <div className="flex items-center space-x-6">
                <span className="text-sans text-2xl font-bold text-white">
                    ScryBot
                </span>

                {/* Navigation Links Removed */}
            </div>

            {/* Right Side - Status & Wallet */}
            <div className="flex items-center space-x-4">
                {/* Wallet/Auth Button Area */}
                {renderWalletButton()}
            </div>
        </header>
    );
};

export default Header; 