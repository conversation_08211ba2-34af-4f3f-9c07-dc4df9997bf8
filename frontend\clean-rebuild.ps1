# ----------------------------------------
# Clean rebuild script for Next.js project
# ----------------------------------------

Write-Host "Starting clean rebuild process..." -ForegroundColor Cyan

# Step 1: Remove existing build artifacts
Write-Host "Removing .next directory..." -ForegroundColor Yellow
if (Test-Path -Path ".next") {
    Remove-Item -Path ".next" -Recurse -Force
    Write-Host ".next directory removed." -ForegroundColor Green
}

# Step 2: Clear npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force
Write-Host "npm cache cleared." -ForegroundColor Green

# Step 3: Reinstall dependencies
Write-Host "Reinstalling dependencies..." -ForegroundColor Yellow
npm ci
Write-Host "Dependencies reinstalled." -ForegroundColor Green

# Step 4: Rebuild the project
Write-Host "Rebuilding project..." -ForegroundColor Yellow
npm run build
Write-Host "Project rebuilt successfully!" -ForegroundColor Green

# Step 5: Start the development server
Write-Host "Starting development server..." -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server." -ForegroundColor Yellow
npm run dev 