-- Add is_admin column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;

-- Set treasury wallet as admin (replace with your actual treasury wallet address)
-- You can find your treasury wallet address in the .env file under TREASURY_WALLET_ADDRESS
UPDATE users SET is_admin = TRUE WHERE wallet_address = 'YOUR_TREASURY_WALLET_ADDRESS_HERE';

-- Example: If your treasury wallet is 'ABC123...', uncomment and modify this line:
-- UPDATE users SET is_admin = TRUE WHERE wallet_address = 'ABC123...';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);

-- Add comment for documentation
COMMENT ON COLUMN users.is_admin IS 'Whether this user has admin privileges for system management';
