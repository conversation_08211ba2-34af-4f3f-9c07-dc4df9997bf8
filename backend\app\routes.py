from fastapi import Request, HTTPException, Body, Depends, status
from fastapi.responses import JSONResponse
from .models import ModelLoader
from config.logging_config import setup_logging  # Import centralized logging functions
from . import CustomFastAPI
import logging
from app.routers.auth_router import router as auth_router
from app.routers.config_router import router as config_router
from app.routers.bot_router import router as bot_router
from app.routers.trade_router import router as trade_router
from app.routers.websocket_router import router as websocket_router
from app.routers.dashboard_router import router as dashboard_router
from app.routers.risk_ladder_router import router as risk_ladder_router
from app import RateLimitExceeded
import os

# Initialize logging
setup_logging()

# Now get the module-level logger
logger = logging.getLogger(__name__)

# Get the centralized predictions logger
log_predictions = logging.getLogger('predictions')  # Use the function from logging_config.py to get the predictions logger

# Global set to keep track of logged mint addresses
logged_mint_addresses = set()

# Initialize H2O model
ModelLoader.load_model()

def configure_routes(app: CustomFastAPI):
    # Rate Limit Exceeded Handler
    @app.exception_handler(RateLimitExceeded)
    async def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
        logger.warning(f"Rate limit exceeded for {request.method} {request.url.path} - IP: {request.client.host}")
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={"detail": f"Rate limit exceeded: {exc.detail}"},
        )

    # Include routers
    app.include_router(auth_router)
    app.include_router(config_router)
    app.include_router(bot_router)
    app.include_router(trade_router)
    app.include_router(websocket_router)
    app.include_router(dashboard_router)
    app.include_router(risk_ladder_router)

    @app.exception_handler(Exception)
    async def generic_exception_handler(request: Request, exc: Exception):
        logger.exception(f"Unhandled error on path {request.url.path}", exc_info=exc)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal Server Error. Please try again later."}
        )
