import { SolanaSignInInput, SolanaSignInOutput } from '@solana/wallet-standard-features';
import { analyzeSiwsMessage, logSiwsVerificationDetails } from '@/utils/siws-debug';
import { DashboardDataResponse, ApiResponse, ConfigurationResponse } from '@/types/api';

// Define expected backend response structure
interface VerifyResponse {
    access_token: string;
    token_type: string;
    walletAddress: string;
}

// Define structure matching backend Pydantic model AuthVerifyPayload
interface BackendVerifyPayload {
    walletAddress: string;
    message: string; // SIWS message string
    signature: string; // Base64 encoded signature
    nonce: string; // The nonce used
}

// Helper function to safely convert Uint8Array to base64 string
function arrayBufferToBase64(buffer: Uint8Array): string {
    // Use a more compact approach for browser environments
    return btoa(Array.from(new Uint8Array(buffer))
        .map(b => String.fromCharCode(b))
        .join(''));
}

export async function fetchSiwsChallenge(): Promise<{ nonce: string } | null> {
    try {
        // Use NEXT_PUBLIC_API_BASE_URL or hardcode for now
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
        const response = await fetch(`${apiBaseUrl}/api/auth/challenge`);
        if (!response.ok) {
            throw new Error(`Failed to fetch challenge: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.nonce) {
             throw new Error("Nonce not found in challenge response");
        }
        console.log("Received nonce:", data.nonce);
        return data;
    } catch (error) {
        console.error("Error fetching SIWS challenge:", error);
        return null;
    }
}

export async function verifySiwsOnBackend(
    input: SolanaSignInInput, // The input originally sent to wallet.signIn
    output: SolanaSignInOutput // The output received from wallet.signIn
): Promise<VerifyResponse | null> {
    try {
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

        // Log the raw output for debugging
        console.log("Raw SignIn Output:", {
            signedMessage: output.signedMessage ? `[${output.signedMessage.byteLength} bytes]` : 'undefined',
            signature: output.signature ? `[${output.signature.byteLength} bytes]` : 'undefined',
            account: output.account
        });

        // CRITICAL: Use the exact message string from the signedMessage
        // The backend needs to verify this exact string against the signature
        const message = new TextDecoder().decode(output.signedMessage);
        console.log("Decoded message:", message);
        
        // Check signature length
        console.log("Original signature length:", output.signature.byteLength);
        
        // The backend expects exactly 64 bytes, no more no less
        // Some wallets may provide a different format, handle with care
        let signatureToUse = output.signature;
        // If signature is longer than 64 bytes (e.g., includes recovery id), take first 64 bytes
        if (output.signature.byteLength > 64) {
            console.log("Signature is longer than 64 bytes, slicing to get first 64 bytes");
            signatureToUse = output.signature.slice(0, 64);
        } else if (output.signature.byteLength < 64) {
            console.error("Signature is shorter than 64 bytes, this may cause verification issues");
        }
        
        const signature = arrayBufferToBase64(signatureToUse);
        console.log("Encoded signature (base64):", signature);
        
        // Extract nonce from input
        const nonce = input.nonce;
        console.log("Nonce from input:", nonce);
        
        // Get address from output
        const walletAddress = output.account.address;
        console.log("Wallet address:", walletAddress);

        // Use our debugging utility to analyze the SIWS message
        logSiwsVerificationDetails(input, output, message, output.signature, walletAddress);
        
        // Check if message format is valid, and use the corrected format if available
        const messageAnalysis = analyzeSiwsMessage(message);
        let messageToUse = message;
        
        // If the message format isn't valid and we have a suggested correction, use it
        if (!messageAnalysis.valid && messageAnalysis.formattedMessage) {
            console.log("Using corrected message format instead of original");
            messageToUse = messageAnalysis.formattedMessage;
        }

        if (!nonce) {
            throw new Error("Nonce missing in original input for verification");
        }

        const payload: BackendVerifyPayload = {
            walletAddress,
            message: messageToUse,
            signature,
            nonce
        };

        console.log("Sending verification payload to backend:", JSON.stringify(payload, null, 2));

        const response = await fetch(`${apiBaseUrl}/api/auth/verify`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
        });

        // Log the raw response for debugging
        console.log("Backend response status:", response.status, response.statusText);
        
        let responseText = '';
        try {
            // Try to get raw response text first for debugging
            responseText = await response.text();
            console.log("Raw response text:", responseText);
            
            // Then parse it as JSON if needed
            const errorData = responseText ? JSON.parse(responseText) : { detail: 'Unknown verification error' };
            
            if (!response.ok) {
                console.error("Backend error data:", errorData);
                throw new Error(`Backend verification failed: ${response.status} ${response.statusText} - ${errorData.detail || 'No details provided'}`);
            }
            
            const data: VerifyResponse = errorData;
            console.log("Backend verification successful, received JWT:", data.access_token.substring(0, 10) + "...");
            return data;
        } catch (parseError) {
            console.error("Error parsing response:", parseError);
            if (!response.ok) {
                throw new Error(`Backend verification failed: ${response.status} ${response.statusText} - Raw response: ${responseText || 'Empty response'}`);
            }
            return null;
        }

    } catch (error) {
        console.error("Error verifying SIWS on backend:", error);
        return null;
    }
}

export async function fetchAuthenticatedApi<T = any>(
    endpoint: string,
    options: RequestInit = {}
): Promise<ApiResponse<T>> {
    try {
        // Get JWT from localStorage
        const jwt = localStorage.getItem('authToken');
        if (!jwt) {
            console.error('Authentication token not found');
            return { 
                success: false, 
                error: 'Authentication token not found. Please sign in again.' 
            };
        }
        
        // Create Headers object from options.headers
        const headers = new Headers(options.headers);
        
        // Set Authorization header with JWT
        headers.set('Authorization', `Bearer ${jwt}`);
        
        // Set Content-Type if not already set and body is not FormData
        if (!headers.has('Content-Type') && 
            options.body && 
            !(options.body instanceof FormData)) {
            headers.set('Content-Type', 'application/json');
        }
        
        // Construct full API URL
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
        const url = `${apiBaseUrl}${endpoint}`;
        
        // Create updated options with modified headers
        const updatedOptions = {
            ...options,
            headers
        };
        
        // Log request details for debugging
        console.log(`Fetching: ${url}`);
        console.log(`Auth token present: ${jwt ? 'Yes (' + jwt.substring(0, 5) + '...)' : 'No'}`);
        console.log('Request options:', {
            ...updatedOptions,
            headers: Object.fromEntries(headers.entries())
        });
        
        // Make the fetch request
        const response = await fetch(url, updatedOptions);
        
        // Log response status
        console.log(`Response status: ${response.status} ${response.statusText}`);
        
        // Handle 204 No Content response
        if (response.status === 204) {
            console.log('Success with no content');
            return { success: true };
        }
        
        // Get raw response text for logging
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        // Handle non-ok responses
        if (!response.ok) {
            let error = 'Unknown error';
            
            try {
                if (responseText) {
                    const errorData = JSON.parse(responseText);
                    error = errorData.detail || `Error: ${response.status} ${response.statusText}`;
                }
            } catch (e) {
                error = responseText || `Error: ${response.status} ${response.statusText}`;
            }
            
            console.error('API request failed:', error);
            return {
                success: false,
                error
            };
        }
        
        // Parse JSON for successful responses
        let data: T;
        try {
            data = responseText ? JSON.parse(responseText) as T : {} as T;
        } catch (e) {
            console.error('Error parsing JSON response:', e);
            return {
                success: false,
                error: 'Failed to parse API response'
            };
        }
        
        console.log('API request successful');
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('API request failed:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
}

/**
 * Validates if a token is still valid by making a lightweight request to the backend
 * Returns true if token is valid, false otherwise
 */
export async function validateToken(): Promise<boolean> {
  try {
    const jwt = localStorage.getItem('authToken');
    if (!jwt) {
      console.log("[Token Validation] No token found");
      return false;
    }

    // Use the same base URL as other API requests
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
    const checkUrl = `${apiBaseUrl}/api/auth/check`;
    
    const response = await fetch(checkUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwt}`
      }
    });
    
    const valid = response.ok && response.status !== 401;
    console.log(`[Token Validation] Token is ${valid ? 'valid' : 'invalid'} (Status: ${response.status})`);
    return valid;
  } catch (error) {
    console.error("[Token Validation] Error during validation:", error);
    // If there's a network error, we can't determine if token is valid
    // Return null to indicate we couldn't check (different from explicitly invalid)
    return false;
  }
} 