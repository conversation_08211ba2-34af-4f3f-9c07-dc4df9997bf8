import logging
import pandas as pd
import numpy as np
import xgboost as xgb
from fastapi import HTTP<PERSON>x<PERSON>, Fast<PERSON><PERSON>
from app.models import Model<PERSON>oader
from .config import USE_FEATURE_AGGREGATION, MERGED_TRANSACTIONS_FILE_AGG, MERGED_TRANSACTIONS_FILE, DESIRED_COLUMNS, PRESERVE_TRANSACTION_LEVEL_DATA, TRANSACTION_LEVEL_COLUMN_PATTERNS, LIVE_RISK_LADDER_STATS_FILE_PATH
from .data_storage import load_merged_transactions_df, save_dataframe, create_earliest_transactions_df
from .data_fetching import fetch_holders
import numbers
import asyncio
import datetime
import time
import os
import json
from app.user_context import UserBotContext
from app.websocket_utils import send_proposal_to_user
from app.database import async_session_factory  # For DB access
from app.db_models import LiveModelProposals    # The new model

logger = logging.getLogger(__name__)
log_predictions = logging.getLogger('predictions')

pd.set_option('display.max_columns', None)
logged_mint_addresses = set()

def feature_engineering(processed_data, preserve_transaction_data=None):
    """Convert processed_data dictionary to DataFrame and compute features."""
    # Check if we should preserve transaction-level data
    if preserve_transaction_data is None:
        preserve_transaction_data = PRESERVE_TRANSACTION_LEVEL_DATA
    
    # Create dictionaries for all our features first, then create the DataFrame in one go
    feature_dict = {}
    
    prices = [processed_data[f'price_{i+1}'] for i in range(100) if f'price_{i+1}' in processed_data]
    sol_volumes = [processed_data[f'sol_volume_{i+1}'] for i in range(100) if f'sol_volume_{i+1}' in processed_data]
    timestamps = [processed_data[f'timestamp_{i+1}'] for i in range(100) if f'timestamp_{i+1}' in processed_data]
    new_buyer_flags = [processed_data[f'new_buyer_{i+1}'] for i in range(100) if f'new_buyer_{i+1}' in processed_data]
    wallet_sold_flags = [processed_data[f'wallet_sold_{i+1}'] for i in range(100) if f'wallet_sold_{i+1}' in processed_data]
    wallet_addresses = [processed_data[f'wallet_address_{i+1}'] for i in range(100) if f'wallet_address_{i+1}' in processed_data]

    if len(prices) < 100:
        logger.warning(f"Not enough price data for mint {processed_data['mint_address']}: {len(prices)} prices")
        return None
    
    # Parse ISO format timestamps if needed and convert to unix timestamp
    def parse_timestamp(ts):
        if isinstance(ts, str):
            if 'T' in ts and 'Z' in ts:  # ISO format like '2025-03-01T16:35:57.000Z'
                try:
                    dt = datetime.datetime.fromisoformat(ts.replace('Z', '+00:00'))
                    return dt.timestamp()
                except ValueError:
                    # Fallback to manual parsing if fromisoformat fails
                    try:
                        dt = datetime.datetime.strptime(ts, '%Y-%m-%dT%H:%M:%S.%fZ')
                        return dt.timestamp()
                    except ValueError:
                        logger.error(f"Could not parse timestamp: {ts}")
                        return time.time()  # Return current time as fallback
            else:
                try:
                    return float(ts)
                except ValueError:
                    logger.error(f"Could not convert timestamp to float: {ts}")
                    return time.time()  # Return current time as fallback
        return ts  # Already a number
    
    first_timestamp = parse_timestamp(processed_data['first_transaction_timestamp'])
    last_timestamp = parse_timestamp(processed_data['last_transaction_timestamp'])
    
    # Add basic features to dictionary
    feature_dict['mint_address'] = processed_data.get('mint_address', 'unknown')
    feature_dict['dev_bought_amount'] = processed_data.get('dev_bought_amount', 0)
    
    # Calculate total_duration based on actual transaction timestamps
    if len(timestamps) >= 2:
        first_tx_timestamp = parse_timestamp(timestamps[0])
        last_tx_timestamp = parse_timestamp(timestamps[-1])
        feature_dict['total_duration'] = (last_tx_timestamp - first_tx_timestamp)  
    else:
        # Fallback to the first/last transaction timestamps from the accumulator
        feature_dict['total_duration'] = (last_timestamp - first_timestamp)  
    
    feature_dict['holders'] = processed_data.get('unique_wallets_count', 0)
    
    # If preserving transaction data, add all raw transaction-level data to the feature dictionary
    if preserve_transaction_data:
        logger.debug(f"Preserving transaction-level data for {feature_dict['mint_address']}")
        # Add all price, volume, timestamp, and flag data
        for i in range(100):
            if f'price_{i+1}' in processed_data:
                feature_dict[f'price_{i+1}'] = processed_data[f'price_{i+1}']
            if f'sol_volume_{i+1}' in processed_data:
                feature_dict[f'sol_volume_{i+1}'] = processed_data[f'sol_volume_{i+1}']
            if f'timestamp_{i+1}' in processed_data:
                feature_dict[f'timestamp_{i+1}'] = processed_data[f'timestamp_{i+1}']
            if f'new_buyer_{i+1}' in processed_data:
                feature_dict[f'new_buyer_{i+1}'] = processed_data[f'new_buyer_{i+1}']
            if f'wallet_sold_{i+1}' in processed_data:
                feature_dict[f'wallet_sold_{i+1}'] = processed_data[f'wallet_sold_{i+1}']
            if f'wallet_address_{i+1}' in processed_data:
                feature_dict[f'wallet_address_{i+1}'] = processed_data[f'wallet_address_{i+1}']
    
    # Add base statistical features
    feature_dict['sol_volume_mean'] = np.mean(sol_volumes)
    feature_dict['sol_volume_max'] = np.max(sol_volumes)
    feature_dict['sol_volume_min'] = np.min(sol_volumes)
    feature_dict['sol_volume_std'] = np.std(sol_volumes)
    feature_dict['sol_volume_median'] = np.median(sol_volumes)
    feature_dict['price_mean'] = np.mean(prices)
    feature_dict['price_max'] = np.max(prices)
    feature_dict['price_min'] = np.min(prices)
    feature_dict['price_std'] = np.std(prices)
    feature_dict['price_median'] = np.median(prices)
    
    # Convert timestamps array and calculate time differences
    numeric_timestamps = [parse_timestamp(ts) for ts in timestamps]
    time_diffs = np.diff(numeric_timestamps) 
    
    # Time difference features
    feature_dict['time_diff_mean'] = np.mean(time_diffs) if len(time_diffs) > 0 else 0
    feature_dict['time_diff_max'] = np.max(time_diffs) if len(time_diffs) > 0 else 0
    feature_dict['time_diff_min'] = np.min(time_diffs) if len(time_diffs) > 0 else 0
    feature_dict['time_diff_std'] = np.std(time_diffs) if len(time_diffs) > 0 else 0
    feature_dict['time_diff_median'] = np.median(time_diffs) if len(time_diffs) > 0 else 0
    
    # Volume and transaction count features
    feature_dict['sum_swapped_in_sol'] = sum(v for v in sol_volumes if v > 0)
    feature_dict['sum_swapped_out_sol'] = abs(sum(v for v in sol_volumes if v < 0))
    feature_dict['num_swapped_in_sol'] = sum(1 for v in sol_volumes if v > 0)
    feature_dict['num_swapped_out_sol'] = sum(1 for v in sol_volumes if v < 0)
    feature_dict['sol_volume_sum'] = sum(sol_volumes)
    feature_dict['new_buyer_sum'] = sum(new_buyer_flags)
    feature_dict['wallet_sold_sum'] = sum(wallet_sold_flags)
    
    # Add holder data placeholders required by DESIRED_COLUMNS
    feature_dict['holder_1_token_balance'] = 0
    feature_dict['holder_1_perc_of_total_supply'] = 0
    feature_dict['holder_1_sol_balance'] = 0
    
    # Process chunks of 10 transactions at a time and build features
    for i in range(0, 100, 10):
        start = i + 1
        end = i + 10
        
        # CRITICAL: Follow the exact format used in DESIRED_COLUMNS
        # In config.py they use: *[f'feature_{i}_{i+9}' for i in range(1, 100, 10)]
        chunk_idx = f"{start}_{start+9}"
        
        # Select the range of data for this chunk
        range_prices = prices[i:i+10]
        range_volumes = sol_volumes[i:i+10]
        range_timestamps = numeric_timestamps[i:i+10]
        range_new_buyers = new_buyer_flags[i:i+10]
        range_wallet_sold = wallet_sold_flags[i:i+10]
        range_wallet_addresses = wallet_addresses[i:i+10]
        
        # Statistical features for each chunk
        feature_dict[f'sol_volume_mean_{chunk_idx}'] = np.mean(range_volumes)
        feature_dict[f'sol_volume_max_{chunk_idx}'] = np.max(range_volumes)
        feature_dict[f'sol_volume_min_{chunk_idx}'] = np.min(range_volumes)
        feature_dict[f'sol_volume_std_{chunk_idx}'] = np.std(range_volumes)
        feature_dict[f'sol_volume_median_{chunk_idx}'] = np.median(range_volumes)
        feature_dict[f'price_mean_{chunk_idx}'] = np.mean(range_prices)
        feature_dict[f'price_std_{chunk_idx}'] = np.std(range_prices)
        feature_dict[f'price_median_{chunk_idx}'] = np.median(range_prices)
        
        # Time difference features for each chunk
        if len(range_timestamps) > 1:
            range_time_diffs = np.diff(range_timestamps) 
            feature_dict[f'time_diff_mean_{chunk_idx}'] = np.mean(range_time_diffs) if len(range_time_diffs) > 0 else 0
            feature_dict[f'time_diff_max_{chunk_idx}'] = np.max(range_time_diffs) if len(range_time_diffs) > 0 else 0
            feature_dict[f'time_diff_min_{chunk_idx}'] = np.min(range_time_diffs) if len(range_time_diffs) > 0 else 0
            feature_dict[f'time_diff_std_{chunk_idx}'] = np.std(range_time_diffs) if len(range_time_diffs) > 0 else 0
            feature_dict[f'time_diff_median_{chunk_idx}'] = np.median(range_time_diffs) if len(range_time_diffs) > 0 else 0
        else:
            feature_dict[f'time_diff_mean_{chunk_idx}'] = 0
            feature_dict[f'time_diff_max_{chunk_idx}'] = 0
            feature_dict[f'time_diff_min_{chunk_idx}'] = 0
            feature_dict[f'time_diff_std_{chunk_idx}'] = 0
            feature_dict[f'time_diff_median_{chunk_idx}'] = 0
            
        # Volume and transaction count features for each chunk
        feature_dict[f'sum_swapped_in_sol_{chunk_idx}'] = sum(v for v in range_volumes if v > 0)
        feature_dict[f'sum_swapped_out_sol_{chunk_idx}'] = abs(sum(v for v in range_volumes if v < 0))
        feature_dict[f'num_swapped_in_sol_{chunk_idx}'] = sum(1 for v in range_volumes if v > 0)
        feature_dict[f'num_swapped_out_sol_{chunk_idx}'] = sum(1 for v in range_volumes if v < 0)
        feature_dict[f'sol_volume_sum_{chunk_idx}'] = sum(range_volumes)
        feature_dict[f'new_buyer_sum_{chunk_idx}'] = sum(range_new_buyers)
        feature_dict[f'wallet_sold_sum_{chunk_idx}'] = sum(range_wallet_sold)
        
        # Missing features required by DESIRED_COLUMNS
        feature_dict[f'holders_{chunk_idx}'] = len(set(range_wallet_addresses))  # Count unique wallets in this chunk
        
        # ROI calculations
        first_price = range_prices[0] if range_prices[0] != 0 else 1e-10  # Avoid division by zero
        feature_dict[f'price_roi_{chunk_idx}'] = (range_prices[-1] - first_price) / first_price
        
        min_price = min(range_prices) if min(range_prices) != 0 else 1e-10  # Avoid division by zero
        feature_dict[f'max_roi_{chunk_idx}'] = (max(range_prices) - min_price) / min_price
        
        # Technical indicators for price chunks
        feature_dict[f'sma_price_{chunk_idx}'] = np.mean(range_prices)
        feature_dict[f'ema_price_{chunk_idx}'] = pd.Series(range_prices).ewm(span=10, adjust=False).mean().iloc[-1]
        feature_dict[f'max_price_{chunk_idx}'] = max(range_prices)
        feature_dict[f'min_price_{chunk_idx}'] = min(range_prices)
        feature_dict[f'tr_{chunk_idx}'] = max(range_prices) - min(range_prices)
        
        # ATR calculation
        atr_series = pd.Series(range_prices).rolling(window=min(10, len(range_prices))).apply(lambda x: max(x) - min(x), raw=True)
        feature_dict[f'atr_{chunk_idx}'] = atr_series.mean() if not atr_series.empty else 0
    
    # Create DataFrame from the complete feature dictionary
    df = pd.DataFrame([feature_dict])
    
    # Add ratio features that require DataFrame operations
    with np.errstate(divide='ignore', invalid='ignore'):
        # Global ratios
        df['sum_swapped_in_out_ratio_sol'] = np.where(
            df['sum_swapped_out_sol'] == 0, 
            0, 
            df['sum_swapped_in_sol'] / df['sum_swapped_out_sol']
        )
        
        df['num_swapped_in_out_ratio_sol'] = np.where(
            df['num_swapped_out_sol'] == 0,
            0,
            df['num_swapped_in_sol'] / df['num_swapped_out_sol']
        )
        
        # Chunk ratios
        for i in range(0, 100, 10):
            start = i + 1
            # CRITICAL: Follow the exact format used in DESIRED_COLUMNS
            chunk_idx = f"{start}_{start+9}"
            
            df[f'sum_swapped_in_out_ratio_sol_{chunk_idx}'] = np.where(
                df[f'sum_swapped_out_sol_{chunk_idx}'] == 0,
                0,
                df[f'sum_swapped_in_sol_{chunk_idx}'] / df[f'sum_swapped_out_sol_{chunk_idx}']
            )
            
            df[f'num_swapped_in_out_ratio_sol_{chunk_idx}'] = np.where(
                df[f'num_swapped_out_sol_{chunk_idx}'] == 0,
                0,
                df[f'num_swapped_in_sol_{chunk_idx}'] / df[f'num_swapped_out_sol_{chunk_idx}']
            )
    
    # If preserving transaction data, create a new list of columns that includes both DESIRED_COLUMNS and transaction-level data
    if preserve_transaction_data:
        # Get all columns from the DataFrame
        all_columns = df.columns.tolist()
        
        # Create a set of transaction-level columns based on patterns
        transaction_level_columns = []
        for pattern in TRANSACTION_LEVEL_COLUMN_PATTERNS:
            transaction_level_columns.extend([col for col in all_columns if pattern in col and col not in DESIRED_COLUMNS])
        
        # Combine DESIRED_COLUMNS with transaction-level columns
        result_columns = ['mint_address'] + [col for col in DESIRED_COLUMNS if col != 'mint_address'] + transaction_level_columns
        
        # Log the number of transaction-level columns being preserved
        logger.debug(f"Preserving {len(transaction_level_columns)} transaction-level columns")
        
        # Create a fresh copy with all columns
        result_df = df[result_columns].copy()
    else:
        # Ensure we only return the columns expected in DESIRED_COLUMNS
        # First check if any required columns are missing and fill with defaults if needed
        for col in DESIRED_COLUMNS:
            if col not in df.columns:
                logger.warning(f"Adding missing column '{col}' with default value 0.0")
                df[col] = 0.0
        
        # Create a fresh copy to defragment the DataFrame before returning
        result_df = df[DESIRED_COLUMNS].copy()
    
    return result_df

async def process_predictions(merged_df, app, thresholds=None):
    """Process each row in the merged DataFrame and make predictions."""
    predictions = []
    
    # Import log_predictions from the module scope, don't recreate it
    # log_predictions = setup_predictions_logger()
    
    if merged_df is None or merged_df.empty:
        logger.warning("DataFrame is empty or None. Cannot process predictions.")
        return predictions
    
    if thresholds is None:
        thresholds = ModelLoader.get_thresholds()
    
    # Clean up DataFrame column names
    merged_df = merged_df.loc[:, ~merged_df.columns.str.contains('^Unnamed')]
    
    # Ensure the mint_address column exists and has no missing values
    if 'mint_address' not in merged_df.columns:
        logger.error("merged_df does not have a mint_address column. This will cause issues in prediction.")
        logger.debug(f"Available columns: {merged_df.columns.tolist()}")
        # Add a placeholder mint_address column
        merged_df['mint_address'] = 'Missing_Mint_Address'
    
    # Replace missing or NaN mint addresses with a placeholder
    mask_missing_mint = merged_df['mint_address'].isna() | (merged_df['mint_address'] == "")
    if mask_missing_mint.any():
        logger.warning(f"Found {mask_missing_mint.sum()} rows with missing mint_address. Using placeholder value.")
        merged_df.loc[mask_missing_mint, 'mint_address'] = 'Missing_Mint_Address'
    
    # Get feature list from model
    feature_list = ModelLoader.get_feature_list()
    if feature_list is not None:
        if 'mint_address' not in feature_list:
            feature_list = ['mint_address'] + [feat for feat in feature_list if feat != 'mint_address']
        merged_df = merged_df.reindex(columns=feature_list)
    else:
        logger.warning("Could not get feature list from model.")
    
    async with app.state.lock:
        for index, row in merged_df.iterrows():
            mint_address = row.get('mint_address')
            if mint_address is None or mint_address == '' or mint_address == 'Missing_Mint_Address':
                logger.warning(f"Row {index} has invalid mint_address: {mint_address}. Skipping prediction.")
                continue
                
            logger.debug(f"Processing row {index} for {mint_address}")
            if mint_address in app.state.processed_addresses:
                logger.debug(f"{mint_address} already processed.")
                continue
                
            if not is_row_complete(row):
                logger.info(f"Row {index} is not complete.")
                continue
                
            logger.debug(f"Row complete for {mint_address}")
            try:
                data = row.to_dict()
                # Ensure mint_address is in the data dict
                if 'mint_address' not in data or data['mint_address'] is None or data['mint_address'] == '':
                    data['mint_address'] = mint_address
                    
                prediction_result = await predict_internal(data, thresholds)
                if prediction_result['buy_signal']:
                    amount = 0.01
                    # Replace with placeholder function that just logs
                    asyncio.create_task(run_buy_and_monitor(output_mint=mint_address, amount=amount))
                    logger.info(f"Started buy task for {mint_address}")
                    
                    # Record the BUY proposal to LiveModelProposals table
                    try:
                        async with async_session_factory() as db_session:
                            # Assume model_version is available, e.g., from ModelLoader or config
                            # For now, we can use a placeholder if model versioning isn't fully implemented
                            model_version_str = "xgboost_v1_placeholder"  # Replace with actual model version later
                            
                            new_proposal_record = LiveModelProposals(
                                mint_address=mint_address,
                                proposal_timestamp=datetime.datetime.utcnow(),
                                model_version=model_version_str,  # TODO: Get this dynamically later
                                is_resolved=False,
                                actual_max_roi=None,
                                resolution_timestamp=None
                            )
                            db_session.add(new_proposal_record)
                            await db_session.commit()
                            logger.info(f"[{mint_address}] Recorded new BUY proposal to LiveModelProposals table.")
                    except Exception as e:
                        logger.error(f"[{mint_address}] Failed to record BUY proposal to LiveModelProposals table: {e}", exc_info=True)
                        # We'll just log the error and continue - this shouldn't prevent other actions
                    
                app.state.processed_addresses.add(mint_address)
                log_message = (
                    f"Prediction for {mint_address} - "
                    f"Buy signal: {prediction_result['buy_signal']}, Probabilities: {prediction_result['probabilities']}"
                )
                log_predictions.info(log_message)
            except Exception as e:
                logger.error(f"Error processing row {index}: {e}")
                app.state.processed_addresses.add(mint_address)
                
    logger.info(f"Processed {len(merged_df)} rows")

def is_row_complete(row):
    required_columns = ModelLoader.get_feature_list()
    if required_columns is None:
        logger.error("Cannot load feature list from model.")
        return True
    if 'mint_address' in required_columns:
        required_columns.remove('mint_address')
    missing_columns = [col for col in required_columns if col not in row.index]
    if missing_columns:
        logger.info(f"Missing columns: {missing_columns}")
        return False
    invalid_columns = [col for col in required_columns if pd.isna(row[col]) or row[col] is None or np.isinf(row[col])]
    if invalid_columns:
        logger.info(f"Invalid values in: {invalid_columns}")
        return False
    non_numeric_columns = [col for col in required_columns if not isinstance(row[col], numbers.Number)]
    if non_numeric_columns:
        logger.info(f"Non-numeric values in: {non_numeric_columns}")
        return False
    return True

def scale_features(X):
    scaler = ModelLoader.get_scaler()
    if scaler is None:
        logger.info("No scaler available - model was trained without scaling. Returning features as-is.")
        return X.copy()
    
    # Get the feature names the scaler expects
    scaler_feature_names = scaler.feature_names_in_
    
    # Check if features are missing or extra
    missing_features = [feat for feat in scaler_feature_names if feat not in X.columns]
    extra_features = [col for col in X.columns if col not in scaler_feature_names]
    
    if missing_features:
        logger.error(f"Input data is missing {len(missing_features)} features expected by the scaler")
        logger.error(f"Missing features: {missing_features[:10]}{'...' if len(missing_features) > 10 else ''}")
        raise ValueError(f"Missing required features for prediction. Cannot proceed without complete feature set.")
    
    # If there are extra features or the column order doesn't match exactly what the scaler expects
    # Create a new dataframe with only the features the scaler knows about and in the correct order
    if extra_features or list(X.columns) != list(scaler_feature_names):
        if extra_features:
            logger.warning(f"Extra {len(extra_features)} features found: {extra_features[:5]}...")
        logger.info("Creating dataframe with features in correct order for scaling")
        X_ordered = X[scaler_feature_names].copy()
        X_scaled_values = scaler.transform(X_ordered)
        X_scaled = pd.DataFrame(X_scaled_values, columns=scaler_feature_names, index=X.index)
    else:
        logger.info("All features match scaler and are in correct order.")
        X_scaled_values = scaler.transform(X)
        X_scaled = pd.DataFrame(X_scaled_values, columns=X.columns, index=X.index)
    
    return X_scaled

async def predict_internal(data: dict, thresholds=None, use_clustering=False, user_wallet: str = "unknown"):
    """Run a prediction on a single data point using XGBoost model."""
    from app.models import ModelLoader
    from config.logging_config import setup_logging
    
    # Fetch artifacts from ModelLoader
    xgboost_model = ModelLoader.get_xgboost_model()
    feature_list = ModelLoader.get_feature_list()
    scaler = ModelLoader.get_scaler()
    optimal_threshold = ModelLoader.get_optimal_threshold()
    calibration_settings = ModelLoader.get_calibration_settings()  # e.g., returns {'has_calibrator': True, 'method': 'isotonic'}
    calibrator = ModelLoader.get_calibrator() if calibration_settings.get('has_calibrator') else None
    
    if thresholds is None:
        # Thresholds are intentionally global for all users
        thresholds = ModelLoader.get_thresholds()
    
    # Check if essential components are available
    if xgboost_model is None or feature_list is None:
        logger.error(f"[{user_wallet}] Essential model components (model or features) not loaded. Cannot predict for {data.get('mint_address', 'Unknown')}.")
        # Return a default non-buy signal or raise an error
        return {"buy_signal": False, "probabilities": {'0': 1.0, '1': 0.0}, "mint_address": data.get('mint_address', 'Unknown')}
    
    # Get mint_address from the data
    mint_address = data.get('mint_address')
    if mint_address is None or mint_address == "":
        logger.warning("Missing mint_address in prediction data. Using default 'Unknown'.")
        mint_address = 'Unknown'
    else:
        logger.debug(f"Processing prediction for mint_address: {mint_address}")
    
    # Ensure the mint_address is included and properly set
    df = pd.DataFrame([data])
    if 'mint_address' not in df.columns:
        df['mint_address'] = mint_address
    elif pd.isna(df['mint_address'].iloc[0]) or df['mint_address'].iloc[0] == "":
        df.loc[0, 'mint_address'] = mint_address
    
    # Get features for the model (excluding mint_address)
    model_input_features = [f for f in feature_list if f != 'mint_address'] # Features for the model itself
    
    # Check for missing features in the input 'data' (now in 'df')
    missing_in_df = [f for f in model_input_features if f not in df.columns]
    if missing_in_df:
        logger.error(f"[{user_wallet}/{mint_address}] Input data is missing required features: {missing_in_df}. Prediction aborted.")
        return {"buy_signal": False, "probabilities": {'0': 1.0, '1': 0.0}, "mint_address": mint_address}
    
    # Select and reorder columns for the model
    X_input = df[model_input_features].copy() # Use .copy() to avoid SettingWithCopyWarning
    
    # Handle NaNs - log warning if present and fill with 0
    if X_input.isnull().values.any():
        nan_cols = X_input.columns[X_input.isnull().any()].tolist()
        logger.warning(f"[{user_wallet}/{mint_address}] NaN values found in input features: {nan_cols}. Filling with 0 before scaling.")
        X_input = X_input.fillna(0)
    
    # Feature Scaling (if scaler is available)
    try:
        if scaler is not None:
            # The scaler expects feature names in the order it was trained on.
            X_scaled_values = scaler.transform(X_input)
            X_model_scaled = pd.DataFrame(X_scaled_values, columns=X_input.columns, index=X_input.index)
            logger.debug(f"[{user_wallet}/{mint_address}] Features scaled. Shape: {X_model_scaled.shape}")
        else:
            # No scaling was used during training, use features as-is
            X_model_scaled = X_input.copy()
            logger.debug(f"[{user_wallet}/{mint_address}] No scaling applied (model trained without scaling). Shape: {X_model_scaled.shape}")
    except Exception as e:
        logger.error(f"[{user_wallet}/{mint_address}] Error during feature scaling: {e}", exc_info=True)
        return {"buy_signal": False, "probabilities": {'0': 1.0, '1': 0.0}, "mint_address": mint_address}
    
    # Make prediction with XGBoost model (XGBClassifier from ml_pipeline)
    try:
        # Use XGBClassifier sklearn interface - predict_proba returns probabilities directly
        probabilities = xgboost_model.predict_proba(X_model_scaled)
        # predict_proba returns [[P(class_0), P(class_1)]] for single prediction
        raw_prob_class1 = float(probabilities[0][1])  # Probability of class 1 (buy signal)
        logger.debug(f"[{user_wallet}/{mint_address}] XGBClassifier probability (class 1): {raw_prob_class1:.4f}")

        # Initialize final probability with raw probability
        final_prob_class1 = raw_prob_class1
    except Exception as e:
        logger.error(f"[{user_wallet}/{mint_address}] Error during XGBoost prediction: {e}", exc_info=True)
        return {"buy_signal": False, "probabilities": {'0': 1.0, '1': 0.0}, "mint_address": mint_address}
    
    # Apply Probability Calibration if available
    if calibration_settings.get('has_calibrator') and calibrator:
        try:
            calibration_method = calibration_settings.get('method', 'isotonic')
            # Reshape raw_prob_class1 for scikit-learn calibrators
            
            if calibration_method == 'isotonic':
                 # IsotonicRegression.transform expects 1D array
                calibrated_value = calibrator.transform([raw_prob_class1])[0]
            elif calibration_method == 'sigmoid':
                # LogisticRegression's predict_proba expects 2D array [[value]]
                # and outputs [[P(0), P(1)]]
                calibrated_value = calibrator.predict_proba(np.array([[raw_prob_class1]]))[0, 1]
            else: # Fallback or if calibrator has a simple 'predict'
                logger.warning(f"[{user_wallet}/{mint_address}] Unknown calibration method '{calibration_method}' or calibrator structure. Attempting generic .predict().")
                # This part is tricky without knowing exact calibrator object type.
                # If it's a CalibratedClassifierCV, it might need predict_proba on feature matrix.
                # For now, assume the pickled calibrator is the fitted sklearn calibrator (e.g. Isotonic/LogisticRegression)
                # that was trained on the probabilities.
                if hasattr(calibrator, 'predict_proba'): # Sigmoid-like
                    calibrated_value = calibrator.predict_proba(np.array([[raw_prob_class1]]))[0,1]
                elif hasattr(calibrator, 'transform'): # Isotonic-like
                    calibrated_value = calibrator.transform([raw_prob_class1])[0]
                elif hasattr(calibrator, 'predict'):
                     calibrated_value = calibrator.predict([raw_prob_class1])[0] # Check if this is right for your calibrator
                else:
                    logger.error(f"[{user_wallet}/{mint_address}] Calibrator object does not have a recognized prediction method.")
                    calibrated_value = raw_prob_class1 # Fallback
            
            # Log the calibrator output for verification before any clipping
            logger.debug(f"[{user_wallet}/{mint_address}] --- DEBUG --- Calibrator output: {calibrated_value}")
            
            # Check if calibrated value is outside [0,1] range
            if calibrated_value < 0 or calibrated_value > 1:
                logger.warning(f"[{user_wallet}/{mint_address}] --- WARNING --- Calibrated value outside [0,1] range: {calibrated_value}. This indicates a possible calibration issue.")
            
            # Ensure the calibrated value is clipped to [0,1]
            final_prob_class1 = float(np.clip(calibrated_value, 0, 1))
            logger.debug(f"[{user_wallet}/{mint_address}] Calibrated P(class=1) using {calibration_method}: {final_prob_class1:.4f} (raw: {raw_prob_class1:.4f})")

        except Exception as e:
            logger.error(f"[{user_wallet}/{mint_address}] Error during probability calibration: {e}. Using raw probability.", exc_info=True)
            final_prob_class1 = raw_prob_class1 # Fallback to raw if calibration fails
    
    # Final safeguard: ensure final_prob_class1 is definitively clipped before further use
    final_prob_class1 = float(np.clip(final_prob_class1, 0, 1))
    logger.debug(f"[{user_wallet}/{mint_address}] Final P(class=1) after any calibration and clipping: {final_prob_class1:.4f}")
    
    # Determine final prediction and return
    prob_class0_clipped = 1.0 - final_prob_class1 # final_prob_class1 is already clipped
    class_probabilities = {'0': prob_class0_clipped, '1': final_prob_class1}

    # Apply threshold to determine buy signal
    threshold_to_use = optimal_threshold if optimal_threshold is not None else thresholds[1]
    predicted_class_binary = 1 if final_prob_class1 >= threshold_to_use else 0
    buy_signal = (predicted_class_binary == 1)

    if buy_signal:
        logger.info(f"[{user_wallet}/{mint_address}] BUY signal generated. P(1)={final_prob_class1:.4f}, Threshold={threshold_to_use:.4f}")
    else:
        logger.info(f"[{user_wallet}/{mint_address}] NO BUY signal. P(1)={final_prob_class1:.4f}, Threshold={threshold_to_use:.4f}")

    # Logging to predictions logger
    log_message = (
        f"Prediction for {mint_address} (User: {user_wallet}) - "
        f"Buy signal: {buy_signal}, Probabilities: {class_probabilities}, Threshold: {threshold_to_use:.4f}"
    )
    if 'log_predictions' in globals() or 'log_predictions' in locals():
        log_predictions.info(log_message)
    else:
        logger.info(log_message) # Fallback to main logger
    
    # Create model insights table
    try:
        # Build data dictionary with all columns at once to avoid DataFrame fragmentation
        insights_data = {
            'identifier': [mint_address],
            'predicted_probability': [float(class_probabilities['1'])],
            'timestamp': [datetime.datetime.now()],
            'user_wallet': [user_wallet]
        }
        
        # Add all feature columns at once (scaled values)
        for col in X_model_scaled.columns:
            insights_data[col] = [X_model_scaled[col].iloc[0]]
            
        # Create the DataFrame with all columns in one operation
        insights_table = pd.DataFrame(insights_data)
        
        # Ensure directory exists - place model_insights in backend/artifacts
        backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # backend directory
        insights_dir = os.path.join(backend_dir, 'artifacts', 'model_insights')
        os.makedirs(insights_dir, exist_ok=True)
        
        # Use a single file per day for insights (YYYYMMDD format)
        date_str = datetime.datetime.now().strftime('%Y%m%d')
        insights_file_path = os.path.join(insights_dir, f"model_insights_{date_str}.csv")
        
        # Check if file exists before writing to determine if we need to include headers
        file_exists_before_write = os.path.isfile(insights_file_path)
        insights_table.to_csv(insights_file_path, mode='a', header=not file_exists_before_write, index=False)
        logger.debug(f"[{user_wallet}/{mint_address}] Model insights appended to: {insights_file_path}")
    except Exception as e:
        logger.error(f"[{user_wallet}/{mint_address}] Error generating or saving model insights table: {str(e)}", exc_info=True)
    
    return {
        "buy_signal": buy_signal,
        "probabilities": class_probabilities,
        "mint_address": mint_address
    }

async def run_user_bot_session(user_wallet: str, user_config: dict, user_context: UserBotContext, app: FastAPI = None):
    """
    Main entry point for running a user-specific bot session.
    This function creates an isolated bot execution flow for a single user.
    
    Args:
        user_wallet: str - The wallet address of the user running the bot
        user_config: dict - User-specific configuration (will be a Pydantic model later)
        user_context: UserBotContext - The context object containing the signal queue
        app: FastAPI - The FastAPI application instance for WebSocket access
    """
    logger.info(f"[{user_wallet}] Starting bot session with user-specific configuration")
    
    try:
        # Main reactive loop - wait for signals from the queue
        while True:
            # Create tasks to wait for either buy or sell signals with a timeout
            buy_signal_task = asyncio.create_task(
                asyncio.wait_for(
                    user_context.incoming_buy_signals.get(),
                    timeout=60.0
                )
            )
            sell_signal_task = asyncio.create_task(
                asyncio.wait_for(
                    user_context.incoming_sell_signals.get(),
                    timeout=60.0
                )
            )
            
            # Wait for the first task to complete or both to timeout
            done, pending = await asyncio.wait(
                [buy_signal_task, sell_signal_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                
            # If no tasks completed (both timed out), continue the loop
            if not done:
                continue
                
            # Process the completed task
            for completed_task in done:
                try:
                    result = await completed_task
                    
                    # Determine if it's a buy or sell signal
                    if completed_task == buy_signal_task:
                        # Process buy signal
                        await process_buy_signal(user_wallet, user_config, user_context, app, result)
                    elif completed_task == sell_signal_task:
                        # Process sell signal
                        await process_sell_signal(user_wallet, user_config, user_context, app, result)
                        
                except asyncio.TimeoutError:
                    # This should not happen since we already handled timeouts in wait
                    continue
                except Exception as e:
                    logger.error(f"[{user_wallet}] Error processing signal: {e}")
                    
    except asyncio.CancelledError:
        logger.info(f"[{user_wallet}] Bot session cancelled - shutting down gracefully")
        raise
        
    except Exception as e:
        # Log any other exceptions but don't crash the whole session
        logger.exception(f"[{user_wallet}] Error in user bot session: {e}")
        
async def process_buy_signal(user_wallet: str, user_config: dict, user_context: UserBotContext, app: FastAPI, signal_payload):
    """Process a buy signal from the queue"""
    try:
        # Unpack the tuple
        received_mint_address, prediction_details = signal_payload
        logger.info(f"[{user_wallet}] Incrementing counter for context ID: {id(user_context)}")
        user_context.session_predicted_signals += 1
        logger.info(f"[{user_wallet}] Received signal #{user_context.session_predicted_signals} for {received_mint_address}...")
        
        # Apply user filters based on user_config and prediction_details
        should_buy = True
        mint_address = received_mint_address  # Use the unpacked mint address
        
        # Perform strict validation - all required fields must be present
        required_fields = ['unique_wallets_count', 'sol_volume_sum', 'holder_1_perc_of_total_supply']
        missing_fields = []
        
        for field in required_fields:
            if field not in prediction_details or prediction_details[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"[{user_wallet}] REJECTING signal for {mint_address} due to missing required fields: {', '.join(missing_fields)}")
            logger.error(f"[{user_wallet}] Trading decisions require complete data integrity. Signal rejected.")
            should_buy = False
            
        # Apply filters from user configuration
        if should_buy and user_config.get('min_unique_wallets') is not None:
            min_unique_wallets = user_config.get('min_unique_wallets')
            if prediction_details['unique_wallets_count'] < min_unique_wallets:
                logger.info(f"[{user_wallet}] Rejected signal for {mint_address}: unique wallets {prediction_details['unique_wallets_count']} below minimum {min_unique_wallets}")
                should_buy = False
        
        if should_buy and user_config.get('min_total_volume') is not None:
            min_volume = user_config.get('min_total_volume')
            if prediction_details['sol_volume_sum'] < min_volume:
                logger.info(f"[{user_wallet}] Rejected signal for {mint_address}: volume {prediction_details['sol_volume_sum']} SOL below minimum {min_volume} SOL")
                should_buy = False
        
        if should_buy and user_config.get('max_holder1_percent') is not None:
            max_holder1_percent = user_config.get('max_holder1_percent')
            # Default to 101.0 if None to effectively disable the check
            if prediction_details['holder_1_perc_of_total_supply'] > max_holder1_percent:
                logger.info(f"[{user_wallet}] Rejected signal for {mint_address}: holder 1 percentage {prediction_details['holder_1_perc_of_total_supply']}% above maximum {max_holder1_percent}%")
                should_buy = False
        
        # If all filters pass, proceed with the transaction proposal
        if should_buy:
            # Read the live risk ladder statistics
            live_risk_ladder_stats = None
            try:
                if os.path.exists(LIVE_RISK_LADDER_STATS_FILE_PATH):
                    with open(LIVE_RISK_LADDER_STATS_FILE_PATH, 'r') as f:
                        stats_data = json.load(f)
                    # Basic validation of the loaded stats
                    required_keys = ["rug_pull_pct", "small_gains_pct", "good_profit_pct", "big_gains_pct", "to_the_moon_pct", "pool_sample_size"]
                    if all(key in stats_data for key in required_keys):
                        live_risk_ladder_stats = {key: stats_data[key] for key in required_keys}
                        # Also include last_updated_utc if available
                        if "last_updated_utc" in stats_data:
                            live_risk_ladder_stats["last_updated_utc"] = stats_data["last_updated_utc"]
                        logger.info(f"[{user_wallet}/{mint_address}] Successfully loaded live risk ladder stats for proposal. Pool size: {stats_data.get('pool_sample_size', 'N/A')}")
                    else:
                        logger.warning(f"[{user_wallet}/{mint_address}] Live risk ladder stats file ({LIVE_RISK_LADDER_STATS_FILE_PATH}) is missing some required keys. Stats: {stats_data}")
                else:
                    logger.warning(f"[{user_wallet}/{mint_address}] Live risk ladder stats file not found at {LIVE_RISK_LADDER_STATS_FILE_PATH}. Sending proposal without these stats.")
            except Exception as e:
                logger.error(f"[{user_wallet}/{mint_address}] Error loading or parsing live risk ladder stats file: {e}", exc_info=True)

            # Construct the transaction proposal dictionary
            buy_amount_sol = user_config.get('max_buy_sol', 0.01)
            buy_slippage = user_config.get('buy_slippage_bps', 150)  # Default 150 BPS (1.5%)
            transaction_proposal = {
                'type': 'buy',
                'user_wallet': user_wallet,
                'mint_address': mint_address,
                'sol_amount': buy_amount_sol,  # For buys
                'slippage_bps': buy_slippage,  # Use the configured value
                'reason': 'BOT_SIGNAL'  # Add a reason
            }
            
            # Only add risk_ladder_stats if successfully loaded
            if live_risk_ladder_stats:
                transaction_proposal['risk_ladder_stats'] = live_risk_ladder_stats
            else:
                # Send a default structure when stats are unavailable
                transaction_proposal['risk_ladder_stats'] = {
                    "rug_pull_pct": None, "small_gains_pct": None, "good_profit_pct": None,
                    "big_gains_pct": None, "to_the_moon_pct": None, "pool_sample_size": 0,
                    "last_updated_utc": None, "status_message": "Live risk profile data currently unavailable."
                }
            
            # Send proposal using the utility function
            send_ok = await send_proposal_to_user(app, user_wallet, transaction_proposal)
            if send_ok:
                logger.info(f"[{user_wallet}] Successfully sent BUY proposal for {mint_address}.")
            else:
                logger.error(f"[{user_wallet}] FAILED to send BUY proposal for {mint_address}.")
        
        # Mark task as done regardless of whether we decided to buy or not
        user_context.incoming_buy_signals.task_done()
        
    except Exception as e:
        logger.error(f"[{user_wallet}] Error processing buy signal: {e}")
        # Ensure we mark the task as done even on error
        try:
            user_context.incoming_buy_signals.task_done()
        except Exception:
            pass
        
async def process_sell_signal(user_wallet: str, user_config: dict, user_context: UserBotContext, app: FastAPI, mint_address: str):
    """Process a sell signal from the queue"""
    try:
        logger.info(f"[{user_wallet}] Received SELL signal for {mint_address}")
        
        # For sell signals, we always want to create a proposal (no filtering)
        sell_slippage = user_config.get('sell_slippage_bps', 150)  # Default 150 BPS (1.5%)
        
        # Construct the transaction proposal dictionary for a sell
        transaction_proposal = {
            'type': 'sell',
            'user_wallet': user_wallet,
            'mint_address': mint_address,
            'token_amount': None,  # The frontend will determine the actual amount to sell
            'slippage_bps': sell_slippage
        }
        
        # Send proposal using the utility function
        send_ok = await send_proposal_to_user(app, user_wallet, transaction_proposal)
        if send_ok:
            logger.info(f"[{user_wallet}] Successfully sent SELL proposal for {mint_address}.")
        else:
            logger.error(f"[{user_wallet}] FAILED to send SELL proposal for {mint_address}.")
        
        # Mark task as done
        user_context.incoming_sell_signals.task_done()
        
    except Exception as e:
        logger.error(f"[{user_wallet}] Error processing sell signal: {e}")
        # Ensure we mark the task as done even on error
        try:
            user_context.incoming_sell_signals.task_done()
        except Exception:
            pass


async def process_predictions_for_user(merged_df, app, user_wallet: str, user_config: dict, thresholds=None):
    """Process each row in the merged DataFrame and make predictions for a specific user."""
    predictions = []
    
    if merged_df is None or merged_df.empty:
        logger.warning(f"[{user_wallet}] DataFrame is empty or None. Cannot process predictions.")
        return predictions
    
    # Clean up DataFrame column names
    merged_df = merged_df.loc[:, ~merged_df.columns.str.contains('^Unnamed')]
    
    # Ensure the mint_address column exists and has no missing values
    if 'mint_address' not in merged_df.columns:
        logger.error(f"[{user_wallet}] merged_df does not have a mint_address column.")
        merged_df['mint_address'] = 'Missing_Mint_Address'
    
    # Replace missing or NaN mint addresses with a placeholder
    mask_missing_mint = merged_df['mint_address'].isna() | (merged_df['mint_address'] == "")
    if mask_missing_mint.any():
        logger.warning(f"[{user_wallet}] Found {mask_missing_mint.sum()} rows with missing mint_address.")
        merged_df.loc[mask_missing_mint, 'mint_address'] = 'Missing_Mint_Address'
    
    # Get feature list from model
    feature_list = ModelLoader.get_feature_list()
    if feature_list is not None:
        if 'mint_address' not in feature_list:
            feature_list = ['mint_address'] + [feat for feat in feature_list if feat != 'mint_address']
        merged_df = merged_df.reindex(columns=feature_list)
    else:
        logger.warning(f"[{user_wallet}] Could not get feature list from model.")
    
    async with app.state.lock:
        # Warning about shared state
        logger.warning(f"[{user_wallet}] Accessing shared app.state.lock - needs isolation for multi-user support.")
        
        for index, row in merged_df.iterrows():
            mint_address = row.get('mint_address')
            if mint_address is None or mint_address == '' or mint_address == 'Missing_Mint_Address':
                logger.warning(f"[{user_wallet}] Row {index} has invalid mint_address. Skipping prediction.")
                continue
                
            logger.debug(f"[{user_wallet}] Processing row {index} for {mint_address}")
            
            # Warning about shared state
            logger.warning(f"[{user_wallet}] Accessing shared app.state.processed_addresses - needs isolation for multi-user support.")
            if mint_address in app.state.processed_addresses:
                logger.debug(f"[{user_wallet}] {mint_address} already processed.")
                continue
                
            if not is_row_complete(row):
                logger.info(f"[{user_wallet}] Row {index} is not complete.")
                continue
                
            logger.debug(f"[{user_wallet}] Row complete for {mint_address}")
            try:
                data = row.to_dict()
                # Ensure mint_address is in the data dict
                if 'mint_address' not in data or data['mint_address'] is None or data['mint_address'] == '':
                    data['mint_address'] = mint_address
                    
                prediction_result = await predict_internal(data, thresholds, user_wallet=user_wallet)
                if prediction_result['buy_signal']:
                    # Use buy amount from user_config instead of hardcoded value
                    amount = user_config.get('buy_amount', 0.01)
                    # Add user context to the buying task
                    asyncio.create_task(run_buy_and_monitor_for_user(output_mint=mint_address, amount=amount, user_wallet=user_wallet))
                    logger.info(f"[{user_wallet}] Started buy task for {mint_address} with amount {amount}")
                    
                # Warning about shared state
                logger.warning(f"[{user_wallet}] Accessing shared app.state.processed_addresses - needs isolation for multi-user support.")
                app.state.processed_addresses.add(mint_address)
                
                log_message = (
                    f"[{user_wallet}] Prediction for {mint_address} - "
                    f"Buy signal: {prediction_result['buy_signal']}, Probabilities: {prediction_result['probabilities']}"
                )
                log_predictions.info(log_message)
            except Exception as e:
                logger.error(f"[{user_wallet}] Error processing row {index}: {e}")
                # Warning about shared state
                logger.warning(f"[{user_wallet}] Accessing shared app.state.processed_addresses - needs isolation for multi-user support.")
                app.state.processed_addresses.add(mint_address)
                
    logger.info(f"[{user_wallet}] Processed {len(merged_df)} rows")


# Add placeholder function for the deleted function
async def run_buy_and_monitor(output_mint: str, amount: float, user_wallet: str = "unknown"):
    """
    Placeholder function that replaces the original swap implementation.
    This function only logs the action that would have been taken.
    """
    logger.info(f"[{user_wallet}] PLACEHOLDER: Would have bought {output_mint} with {amount} SOL")
    # In a real implementation, this would execute the swap and monitor the price
    return {"status": "simulated", "message": f"Simulated buy of {output_mint}"}

async def run_buy_and_monitor_for_user(output_mint: str, amount: float, user_wallet: str):
    """
    Enhanced version of run_buy_and_monitor that includes user context.
    This wraps the placeholder function and adds user information.
    """
    logger.info(f"[{user_wallet}] Starting buy and monitor for {output_mint}, amount: {amount} SOL")
    
    try:
        # Call the placeholder function with the same parameters
        await run_buy_and_monitor(output_mint=output_mint, amount=amount, user_wallet=user_wallet)
    except Exception as e:
        logger.exception(f"[{user_wallet}] Error in run_buy_and_monitor: {e}")