from pydantic import BaseModel


class AuthVerifyPayload(BaseModel):
    """
    Payload for wallet verification.
    """
    walletAddress: str
    message: str  # The full SIWS message that was signed by frontend
    signature: str  # Base64 encoded signature from frontend


class AuthVerifyResponse(BaseModel):
    """
    Response for wallet verification.
    """
    access_token: str
    token_type: str = "bearer"  # Default to bearer
    walletAddress: str 