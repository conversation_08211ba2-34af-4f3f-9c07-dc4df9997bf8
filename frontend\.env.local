# Frontend Local Environment Variables
# ------------------------------------

# Solana RPC Endpoint for wallet adapter and direct on-chain reads by frontend. (+1226)
# This key will be exposed in the browser bundle. Use a key with appropriate permissions
# (e.g., primarily for read access if possible, or a general purpose dev key for local).
NEXT_PUBLIC_SOLANA_RPC_ENDPOINT="https://rpc.shyft.to?api_key=Ajwam2blTR6Q4z-x"

# Base URL for your backend API.
NEXT_PUBLIC_API_BASE_URL="http://localhost:8000"

# Platform fee percentage (e.g., "1" for 1%, "0.5" for 0.5%).
# Used by frontend/lib/constants.ts.
NEXT_PUBLIC_FEE="1"

