Okay, I understand your feedback and priorities. Let's integrate them into a revised, optimal plan focusing on getting a stable, secure, and functional MVP ready for potential users, including the key monitoring and alerting aspects needed for a live service.

Here is the revised plan:

**Revised Development Plan (Post Phase F7)**

**Phase F8: Immediate Fixes & Core Stability** *(Highest Priority)*

*   **Step F8.1: Fix `layout.js` Syntax/ChunkLoadError:**
    *   **Goal:** Resolve critical JS errors preventing the app from loading.
    *   **Action:** Debug `layout.tsx` / build output.
*   **Step F8.2: Address Overlapping/Cancelled Proposals:**
    *   **Goal:** Prevent new proposals from interfering with an open modal.
    *   **Action:** Modify `handleTransactionProposal` in `page.tsx` to ignore new proposals if one is already open.
*   **Step F8.3: Apply Proposal Rejection `useRef` Fix (Former F6.9):**
    *   **Goal:** Ensure cancelled proposals are reliably ignored immediately using `useRef`.
    *   **Action:** Implement the `useRef` logic alongside `rejectedProposals` state in `page.tsx` as detailed previously. *(Even though it seemed to work once, this makes it robust).*

**Phase F9: Enhance Trading Logic & Reliability** *(High Priority)*

*   **Step F9.1: Implement Bonding Curve Check:**
    *   **Goal:** Verify token is on bonding curve before attempting Pump.fun SDK actions.
    *   **Action (Frontend):** In `executeApprovedProposal`/`handleManualSell`, call `sdk.getBondingCurveAccount()` and check `bondingCurveAccount.complete`.
*   **Step F9.2: Seamless Migrated Token Handling (Jupiter Integration - MVP):**
    *   **Goal:** If `bondingCurveAccount.complete` is true, automatically attempt swap via Jupiter API without extra user prompts. Provide clear feedback only on *failure*.
    *   **Action (Frontend):**
        *   Integrate `@jup-ag/api` (or direct fetch calls to Jupiter API).
        *   Modify `executeApprovedProposal`/`handleManualSell`: If check F9.1 indicates completion, call Jupiter `/quote` and `/swap` endpoints. Build, sign, send the transaction from Jupiter.
        *   Update `/api/trades/report` payload slightly if needed to differentiate source (Pump/Jupiter).
    *   **Action (Backend - Optional):** `price_monitor` can check `complete` status before sending SELL proposals.

**Phase F10: API Key Management & Monitoring Setup** *(Medium-High Priority)*

*   **Step F10.1: Centralize Backend Keys & Endpoints:**
    *   **Goal:** Move all backend external API keys/URLs (Shyft Parse, Shyft RPC, Moralis Price, Helius Listener) to `config.ini`.
    *   **Action (Backend):** Refactor relevant Python files (`data_fetching`, `price_monitor`, `new_token_listener_pumpfun`, etc.) to read credentials/URLs from the central config object.
*   **Step F10.2: Implement *Separate* Key Managers:**
    *   **Goal:** Create distinct key managers for different services (RPCs, Moralis) without modifying the existing Shyft parsing manager.
    *   **Action (Backend):** Create new classes/instances (e.g., `RPCKeyManager`, `MoralisKeyManager`) modelled after `APIKeyManager`. Integrate them into the services identified in F10.1. Implement basic rotation and unavailable marking based on API responses (rate limits, errors).
*   **Step F10.3: Consolidate Backend RPC Provider:**
    *   **Goal:** Ensure only Shyft RPC endpoints (managed by `RPCKeyManager`) are used for backend RPC calls.
    *   **Action (Backend):** Review `data_fetching.py` and any other places making direct RPC calls; ensure they use the `RPCKeyManager`.
*   **Step F10.4: Key Exhaustion Warnings & Basic Telegram Alerting:**
    *   **Goal:** Log clear warnings and send Telegram alerts when API key pools get low.
    *   **Action (Backend):**
        *   Modify key managers (`APIKeyManager`, `RPCKeyManager`, etc.) to track available keys.
        *   Add logic to check pool status (e.g., < 20% available).
        *   If low, log `WARNING` / `ERROR`.
        *   Implement a simple Telegram sending function (using `python-telegram-bot` or `requests`).
        *   Add Telegram Bot Token and Chat ID to `config.ini`.
        *   Call the Telegram function from the key managers on low-key detection.

**Phase F-SEC: Security Fundamentals (MVP)** *(Medium-High Priority)*

*   **Step F-SEC.1: Backend Input Validation Review:**
    *   **Goal:** Ensure all API endpoints properly validate incoming data types and ranges using Pydantic models.
    *   **Action (Backend):** Review all Pydantic models used in API request bodies (`AuthVerifyPayload`, `ConfigurationCreate`, `TradeReportPayload`). Ensure types, constraints (e.g., `ge=0`), and validators are appropriate.
*   **Step F-SEC.2: Basic Rate Limiting (Backend):**
    *   **Goal:** Implement simple API rate limiting to prevent abuse.
    *   **Action (Backend):** Add a dependency using `slowapi` or a similar library to rate-limit key endpoints (e.g., `/config` POST, `/bot/start`, `/bot/stop`, `/auth/verify`). Start with sensible limits (e.g., 10 requests per minute per user).
*   **Step F-SEC.3: Review Dependencies:**
    *   **Goal:** Check for known vulnerabilities in dependencies.
    *   **Action (Both):** Run `npm audit` (frontend) and `pip-audit` or `safety check` (backend) to identify vulnerable packages. Update or replace where feasible.
*   **Step F-SEC.4: Secrets Management:**
    *   **Goal:** Ensure no sensitive keys/credentials are hardcoded or committed to Git.
    *   **Action (Both):** Double-check that all API keys, DB URLs, JWT secrets, Telegram tokens etc., are loaded *only* from `config.ini` / `.env` / environment variables and these config files are listed in `.gitignore`.
*   **Step F-SEC.5: CORS Configuration:**
    *   **Goal:** Ensure CORS is configured securely for production.
    *   **Action (Backend):** Review the `CORSMiddleware` setup in `app/__init__.py`. For production, change `allow_origins=["http://localhost:3000"]` to the specific deployed frontend domain(s). Avoid using `"*"`.
*   **Step F-SEC.6: HTTPS:**
    *   **Goal:** Ensure the deployed application uses HTTPS.
    *   **Action (Deployment):** Configure the deployment environment (e.g., Nginx reverse proxy, cloud load balancer) to handle SSL termination and enforce HTTPS.

**Phase F-MON: Live Monitoring & Alerting (MVP)** *(Medium Priority)*

*   **Step F-MON.1: Logging Review:**
    *   **Goal:** Ensure critical errors and key events are logged effectively.
    *   **Action (Backend):** Review existing logging. Add `logger.exception()` calls in key `try...except` blocks where errors might be critical (e.g., database errors, swap failures, key exhaustion). Ensure user identifiers (like wallet address snippets) are included in relevant logs for easier debugging.
*   **Step F-MON.2: Basic Health Check Endpoint:**
    *   **Goal:** Create a simple endpoint for external monitoring.
    *   **Action (Backend):** Add a `/health` endpoint (e.g., in `app/routes.py`) that returns a simple `{"status": "ok"}` JSON response with a 200 status if the basic FastAPI app is running.
*   **Step F-MON.3: External Uptime Monitoring:**
    *   **Goal:** Use a free external service to monitor the `/health` endpoint.
    *   **Action (Ops):** Configure a service like UptimeRobot (free tier available) to ping the deployed `/health` endpoint periodically and send alerts (email, etc.) if it becomes unresponsive.
*   **Step F-MON.4: Critical Error Alerting (Telegram):**
    *   **Goal:** Send Telegram alerts for critical backend exceptions.
    *   **Action (Backend):**
        *   Use the Telegram function created in F10.4.
        *   In critical `try...except Exception as e:` blocks (e.g., main loops of background services like `price_monitor`, `websocket_listener`, database connection errors), call the Telegram alert function, sending a concise error summary. Be careful not to spam alerts for frequent, recoverable errors.

**Phase F11: UI/UX Refinements (Post-MVP / Lower Priority)**

*   *(These steps remain largely the same as before, implemented after the higher priority phases are complete)*
    *   Step F11.1: Implement Sonner Toasts
    *   Step F11.2: Improve Loading/Unauthenticated UX
    *   Step F11.3: Unify Connect/Sign-In Button
    *   Step F11.4: Remove Mock Bot Metrics (Already done in F11.1 technically)
    *   Step F11.5: Enhance Proposal Modal (Ticker)
    *   Step F11.6: Enhance "Bot Status & Control" Card (Real Stats)
    *   Step F11.7: Refine "Current Holdings" Card (Partial Sell)
    *   Step F11.8: Refine "Recent Completed Trades" Card (Session Filter)
    *   Step F11.9: Improve Trade History UX (Pagination/Grouping)

**Phase F12: Monetization & Long-Term** *(Lowest Priority for MVP)*

*   Step F12.1: Implement Transaction Fee
*   Step F12.2: Auto-Logout/Bot Timeout

---

**Phase F11: UI/UX Refinements**

*   **Step F11.1: Implement Sonner Toasts:**
    *   **Goal:** Replace all `alert()` calls with non-blocking toast notifications using Sonner.
    *   **Action (Frontend):**
        *   Import `toast` from 'sonner'.
        *   Add `<Toaster />` component in `layout.tsx` or `page.tsx`.
        *   Replace `alert(...)` calls in `handleManualSell`, `executeApprovedProposal`, `handleConfigSave`, `handleBotControlClick`, etc., with `toast.success(...)` or `toast.error(...)`.
*   **Step F11.2: Improve Loading/Unauthenticated UX:**
    *   **Goal:** Show a specific "Connect & Sign In" prompt instead of "Loading Dashboard Data..." when unauthenticated on refresh.
    *   **Action (Frontend):** Modify the loading/auth checks in `page.tsx`. After `isLoadingAuth` becomes `false`, if `!isAuthenticated`, render the "Authentication Required" card immediately instead of waiting for `dashboardData` (which won't be fetched).
*   **Step F11.3: Unify Connect/Sign-In Button:**
    *   **Goal:** Combine the wallet connection and SIWS sign-in steps into a single button flow.
    *   **Action (Frontend):** Modify `ClientWalletButton` and `AuthButton`. If the wallet is *not* connected, the main button should act as `WalletMultiButton`. If the wallet *is* connected but the user is *not* authenticated (`isAuthenticated` is false), the button should become the "Sign In to Verify" button (`AuthButton` logic). If connected *and* authenticated, show the "Sign Out" button and wallet address. This requires careful state management between the wallet adapter's state and the `AuthContext`.
*   **Step F11.4: Remove Mock Bot Metrics:**
    *   **Goal:** Remove the placeholder "BOT METRICS" section from the sidebar.
    *   **Action (Frontend):** Delete the relevant JSX `div` from the `aside` element in `page.tsx`.
*   **Step F11.5: Enhance Proposal Modal (Ticker):**
    *   **Goal:** Display token ticker/name instead of just the mint address in the proposal modal.
    *   **Action (Backend):** When generating the proposal in `run_user_bot_session` or `price_monitor`, try to fetch the token metadata (e.g., using a simple RPC call `getTokenMetadata` or a helper function) to get the `name` and `symbol`. Add these to the `TransactionProposalData` sent via WebSocket.
    *   **Action (Frontend):** Update the `TransactionProposalData` type. Update the JSX in the `AlertDialog` to display `proposalData.symbol` or `proposalData.name` if available, falling back to the shortened mint address.
*   **Step F11.6: Enhance "Bot Status & Control" Card:**
    *   **Goal:** Display real-time session statistics.
    *   **Action (Backend):**
        *   Requires significant state tracking *per user session* in the `UserBotContext` or a related structure managed by `BotManager`. Track buys, sells, PNL, current investment, etc.
        *   Modify the `/api/dashboard/data` endpoint (or create a new one like `/api/bot/session_stats`) to fetch and return this data.
        *   Implement real uptime calculation based on `BotState.last_changed` when status is `RUNNING`.
    *   **Action (Frontend):** Update `DashboardDataResponse` type. Modify the "Bot Status & Control" card JSX in `page.tsx` to display the new data fields.
*   **Step F11.7: Refine "Current Holdings" Card:**
    *   **Goal:** Adjust layout, add partial sell option, rename labels.
    *   **Action (Frontend):**
        *   Modify grid classes in `page.tsx` for the holdings container (`xl:grid-cols-2` or `3`).
        *   Modify `HoldingCard.tsx`:
            *   Add an `Input` field (type number, min 1, max 100, default 50) and a new "Sell %" button next to the "Sell Full Amount" button.
            *   The "Sell %" button's `onClick` will need a new handler function passed down (e.g., `onPartialSell(holding, percentage)`) which calculates the token amount based on `holding.amount_held` and the percentage, then likely calls `executeApprovedProposal` with modified parameters (needs careful implementation).
            *   Rename the labels "Avg Buy (SOL)" to "Buy Price (SOL)" and "Current (SOL)" to "Current Price (SOL)".
*   **Step F11.8: Refine "Recent Completed Trades" Card:**
    *   **Goal:** Rename card, show only current session trades, add ticker, rename price column.
    *   **Action (Backend):** Modify `/api/dashboard/data` or create a new endpoint. The query in `trade_crud.get_recent_trades_by_user` needs to be adapted to filter trades based on the *current active session* start time (which needs to be stored, perhaps in `BotState` when status changes to `RUNNING`). Fetch and include ticker symbols.
    *   **Action (Frontend):** Update `TradeItem` type. Update card title. Modify table header from "Avg Price (SOL)" to "Price (SOL)". Add a column for the Ticker. Update the data mapping.
*   **Step F11.9: Improve Trade History UX:**
    *   **Goal:** Paginate or group the full trade history view instead of one long table.
    *   **Action (Backend):** Modify the `/api/trades/history` endpoint (or similar) to support pagination (limit/offset parameters) and potentially date range filtering. The `trade_crud` function needs updating.
    *   **Action (Frontend):** Implement UI controls for pagination (Previous/Next buttons, page numbers) or date filtering in the "Trade History" view. Update the API call to include pagination/filter parameters. Manage the display of fetched data pages.

**Phase F12: Monetization & Post-MVP**

*   **Step F12.1: Implement Transaction Fee:**
    *   **Goal:** Add a 1% fee mechanism on buys and sells.
    *   **Action (Backend):** This is complex. The fee needs to be incorporated *into the swap instructions*.
        *   For Pump.fun buys/sells via SDK: It's unclear if the SDK directly supports adding an arbitrary `transfer` instruction for a fee within the same transaction. It might require constructing the transaction manually or using a separate transaction.
        *   For Jupiter swaps: Jupiter API might have parameters for platform fees/splits that could potentially be used.
        *   Requires a designated fee collection wallet.
        *   Needs careful calculation based on SOL amount (for buys) or SOL received (for sells).
    *   **Action (Frontend):** UI should clearly display the fee being charged before the user approves the transaction.
*   **Step F12.2: Address Remaining Bug Fixes:**
    *   **Goal:** Fix the proposal rejection race condition (using Step F6.9's `useRef` fix) and any other bugs identified during testing.
*   **Step F12.3: Auto-Logout/Bot Timeout:**
    *   **Goal:** Implement backend logic for session/bot timeouts.
    *   **Action (Backend):**
        *   **JWT Expiry:** Rely on JWT expiry (already configured) for session timeout. Frontend needs to handle 401 errors from `fetchAuthenticatedApi` by logging the user out (`handleLogout`).
        *   **Bot Timeout:** Add a background task or modify `monitor_accumulators` to check `BotState.last_changed`. If a bot is `RUNNING` but the timestamp is too old (e.g., > 24 hours or configurable), automatically set its status to `STOPPED` or `ERROR` in the DB.

---

This revised plan incorporates your feedback: seamless migrated token handling, separate key managers, MVP Telegram alerts, and adds dedicated phases for essential Security and Monitoring practices before tackling the lower-priority UI/UX refinements and monetization.

Does this updated plan look good to proceed with? We'll start with **Phase F8: Immediate Fixes**.



**Proposed Order:**

1.  **Phase F8: Immediate Fixes** (F8.1 Layout Error, F8.2 Overlapping Proposals) - *Critical*
2.  **Phase F9: Core Functionality** (F9.1 Bonding Curve Check, F9.2 Migrated Token Handling - *Can be complex, maybe defer Jupiter part*) - *High Priority*
3.  **Phase F11 (Partial): UI/UX Basics** (F11.1 Toasts, F11.2 Loading UX, F11.3 Unified Auth Button, F11.4 Remove Metrics) - *Medium Priority (Improves usability)*
4.  **Phase F10: Monitoring & Keys** (F10.1 Centralize, F10.3 Warnings, F10.4 Consolidate RPCs) - *Medium Priority (Essential for stability/maintenance)*
5.  **Phase F6 Leftovers:** Apply F6.9 (useRef fix for rejection) - *Medium Priority (Bug fix)*
6.  **Phase F11 (Further UI):** (F11.5 Ticker, F11.6 Bot Stats Card, F11.7 Holding Card Refine, F11.8 Recent Trades Refine, F11.9 History UX) - *Lower Priority (Polish)*
7.  **Phase F10 (Complex):** F10.2 Key Rotation for *all* services - *Lower Priority (Requires refactoring)*
8.  **Phase F12: Monetization & Post-MVP** (F12.1 Fee, F12.3 Timeouts) - *Lowest Priority for MVP*