-- Create sniper_bot_simulations table for tracking sniper bot simulation data
-- This table stores tokens detected for sniper bot simulation and their ROI outcomes

CREATE TABLE IF NOT EXISTS sniper_bot_simulations (
    id SERIAL PRIMARY KEY,
    mint_address VARCHAR NOT NULL,
    launch_timestamp TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL,
    signature VARCHAR,
    is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
    resolution_timestamp TIMESTAMP WITHOUT TIME ZONE,
    actual_max_roi DOUBLE PRECISION,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sniper_bot_simulations_mint_address ON sniper_bot_simulations(mint_address);
CREATE INDEX IF NOT EXISTS idx_sniper_bot_simulations_launch_timestamp ON sniper_bot_simulations(launch_timestamp);
CREATE INDEX IF NOT EXISTS idx_sniper_bot_simulations_is_resolved ON sniper_bot_simulations(is_resolved);
CREATE INDEX IF NOT EXISTS idx_sniper_bot_simulations_resolution_timestamp ON sniper_bot_simulations(resolution_timestamp);

-- Add comments for documentation
COMMENT ON TABLE sniper_bot_simulations IS 'Tracks tokens detected for sniper bot simulation and their ROI outcomes. Simulates buying every new token as the 10th transaction.';
COMMENT ON COLUMN sniper_bot_simulations.mint_address IS 'The token mint address';
COMMENT ON COLUMN sniper_bot_simulations.launch_timestamp IS 'When the token was first detected/launched';
COMMENT ON COLUMN sniper_bot_simulations.signature IS 'Transaction signature from the CSV file';
COMMENT ON COLUMN sniper_bot_simulations.is_resolved IS 'Whether the ROI has been calculated for this simulation';
COMMENT ON COLUMN sniper_bot_simulations.resolution_timestamp IS 'When the ROI was calculated';
COMMENT ON COLUMN sniper_bot_simulations.actual_max_roi IS 'The calculated maximum ROI (profit factor) achieved by the token';
COMMENT ON COLUMN sniper_bot_simulations.created_at IS 'When this record was created in the database';
