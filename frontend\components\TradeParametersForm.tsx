import { FormEvent, useState } from "react";
import { ConfigurationResponse } from "@/types/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Settings, Info, SaveIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/components/ui/card";
import SectionSubheading from '@/components/SectionSubheading';
import { cn } from "@/lib/utils";

interface TradeParametersFormProps {
  configData: ConfigurationResponse | null;
  isConfigLoading: boolean;
  isSavingConfig: boolean;
  configChanged: boolean;
  configSaveStatus: { type: 'success' | 'error'; message: string } | null;
  handleConfigSave: (event: FormEvent<HTMLFormElement>) => Promise<void>;
  setConfigChanged: (changed: boolean) => void;
  clearConfigSaveStatus: () => void;
}

export default function TradeParametersForm({
  configData,
  isConfigLoading,
  isSavingConfig,
  configChanged,
  configSaveStatus,
  handleConfigSave,
  setConfigChanged,
  clearConfigSaveStatus
}: TradeParametersFormProps) {
  const [priorityFeeInfoOpen, setPriorityFeeInfoOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"buy_sell" | "slippage_fees">("buy_sell");

  const handleTabChange = (tab: "buy_sell" | "slippage_fees") => {
    clearConfigSaveStatus();
    setActiveTab(tab);
  };

  return (
    <div className="w-full mb-6">
      <form onSubmit={handleConfigSave}>
        {/* Status Message - Only show if not currently switching tabs */}
        {configSaveStatus && !isSavingConfig && (
          <div className={`mb-4 px-3 py-2 rounded text-sm ${configSaveStatus.type === 'success' ? 'text-status-success border border-status-success/30' : 'text-[#E64C4C] border border-[#E64C4C]/30'}`}>
            {configSaveStatus.message}
          </div>
        )}

        {/* Priority Fee Info Modal */}
        <Dialog open={priorityFeeInfoOpen} onOpenChange={setPriorityFeeInfoOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Solana Priority Fees</DialogTitle>
            </DialogHeader>
            <div className="text-sm space-y-4 max-h-[70vh] overflow-y-auto">
              <p>
                Priority fees are optional fees you can add to your Solana transaction to make it process faster, especially during busy times (e.g., token launches or NFT mints). They help your transaction "jump the queue" by incentivizing validators to include it in the next block.
              </p>
              
              <h3 className="font-semibold">How Do Priority Fees Work?</h3>
              <p>
                The priority fee depends on two things:
                <ul className="list-disc pl-5 mt-2">
                  <li>Compute Units (CU): A measure of how much processing your transaction needs. A typical token swap (e.g., on Raydium or Jupiter) uses 200,000–600,000 CU.</li>
                  <li>Compute Unit Price: The amount you pay per CU, set in micro-lamports (1 micro-lamport = 0.000001 lamports, and 1 SOL = 1,000,000,000 lamports).</li>
                </ul>
              </p>
              
              <p>
                The formula is:<br />
                Priority Fee (in lamports) = Compute Units × Compute Unit Price (in micro-lamports).
              </p>
              
              <p>
                Every transaction also has a fixed base fee of 0.000005 SOL (5,000 lamports) per signature.
              </p>
              
              <h3 className="font-semibold">What Should I Set as the Compute Unit Price?</h3>
              <p>
                The compute unit price depends on network congestion:
                <ul className="list-disc pl-5 mt-2">
                  <li>Low Congestion (quiet network): 1,000–5,000 micro-lamports/CU is usually enough for fast confirmation.</li>
                  <li>Moderate Congestion (typical trading): 10,000–20,000 micro-lamports/CU ensures quick processing.</li>
                  <li>High Congestion (e.g., meme coin launches): 50,000–100,000 micro-lamports/CU is competitive. In extreme cases, up to 500,000 micro-lamports/CU may be needed, but higher values rarely add speed.</li>
                </ul>
              </p>
              
              <p>
                For safety, we cap the compute unit price at 1,000,000 micro-lamports/CU to prevent overpaying.
              </p>
              
              <h3 className="font-semibold">Example Calculation:</h3>
              <p>
                For a token swap using 200,000 CU with a compute unit price of 50,000 micro-lamports/CU (good for high congestion):
                <ul className="list-disc pl-5 mt-2">
                  <li>Priority Fee = 200,000 × 50,000 micro-lamports = 10,000 lamports = 0.00001 SOL.</li>
                  <li>Base Fee = 0.000005 SOL (5,000 lamports).</li>
                  <li>Total Fee = 0.000015 SOL (15,000 lamports), or about $0.0036 at $240/SOL.</li>
                </ul>
              </p>
              
              <h3 className="font-semibold">Tips:</h3>
              <ul className="list-disc pl-5">
                <li>Use lower prices (e.g., 5,000–20,000 micro-lamports/CU) during quiet times to save costs.</li>
                <li>Check the network congestion indicator (if available) to adjust your fee.</li>
                <li>The bot may suggest a dynamic fee based on recent network activity for optimal performance.</li>
              </ul>
              
              <p className="italic">
                By setting a smart priority fee, you can ensure fast transactions without overpaying!
              </p>
            </div>
          </DialogContent>
        </Dialog>

        {/* Trade Parameters Card with Tabs */}
        <Card className="w-full h-full flex flex-col glass-card glass-card-hover">
          <CardHeader className="p-0">
            <CardTitle>Trade Parameters</CardTitle>
          </CardHeader>

          {/* Tab Navigation */}
          <div className="border-b border-white/10 mb-5">
            <div className="flex">
              <button
                type="button"
                onClick={() => handleTabChange("buy_sell")}
                className={cn(
                  "subheading-text-style relative px-4 py-2 transition-all duration-200 flex-1 text-center flex items-center justify-center gap-2",
                  activeTab === "buy_sell"
                    ? "text-white"
                    : "text-white/30 hover:text-white"
                )}
              >
                <span className="text-status-warning">*</span>
                Buy & Sell 
                {activeTab === "buy_sell" && (
                  <div className="absolute bottom-0 left-0 w-full h-[1px] bg-white"></div>
                )}
              </button>
              <button
                type="button"
                onClick={() => handleTabChange("slippage_fees")}
                className={cn(
                  "subheading-text-style relative px-4 py-2 transition-all duration-200 flex-1 text-center",
                  activeTab === "slippage_fees"
                    ? "text-white"
                    : "text-white/30 hover:text-white"
                )}
              >
                Slippage & Fees
                {activeTab === "slippage_fees" && (
                  <div className="absolute bottom-0 left-0 w-full h-[1px] bg-white"></div>
                )}
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <CardContent className="p-0 flex-1 flex flex-col">
            {/* Buy & Sell Parameters Tab */}
            <div className={cn(
              "flex-1 flex flex-col justify-between",
              activeTab !== "buy_sell" && "hidden"
            )}>
              {/* Parameters Grid */}
              <div className="space-y-4 mb-6">
                {/* Buy Amount Card - Full Width */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-white text-xs mb-2">
                    BUY AMOUNT <span className="text-status-warning">*</span>
                  </div>
                  <div className="flex-1 flex items-center justify-center">
                    <Input
                      id="max_buy_sol"
                      name="max_buy_sol"
                      type="number"
                      step="0.001"
                      min="0"
                      placeholder="e.g., 0.1"
                      defaultValue={configData?.max_buy_sol?.toString() || ""}
                      onChange={() => setConfigChanged(true)}
                      className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary max-w-[200px]"
                      disabled={isConfigLoading}
                    />
                  </div>
                  <div className="text-foreground/40 text-xs mt-1 text-center">SOL</div>
                </div>

                {/* Take Profit and Stop Loss Row */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Take Profit Card */}
                  <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                    <div className="text-white text-xs mb-2">
                      TAKE PROFIT <span className="text-status-warning">*</span>
                    </div>
                    <div className="flex-1 flex items-center">
                      <Input
                        id="tp_percent"
                        name="tp_percent"
                        type="number"
                        step="1"
                        min="1"
                        placeholder="e.g., 50"
                        defaultValue={configData?.tp_percent?.toString() || ""}
                        onChange={() => setConfigChanged(true)}
                        className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary"
                        disabled={isConfigLoading}
                      />
                    </div>
                    <div className="text-foreground/40 text-xs mt-1">Percent</div>
                  </div>

                  {/* Stop Loss Card */}
                  <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                    <div className="text-white text-xs mb-2">
                      STOP LOSS <span className="text-status-warning">*</span>
                    </div>
                    <div className="flex-1 flex items-center">
                      <Input
                        id="sl_percent"
                        name="sl_percent"
                        type="number"
                        step="1"
                        min="1"
                        placeholder="e.g., 30"
                        defaultValue={configData?.sl_percent?.toString() || ""}
                        onChange={() => setConfigChanged(true)}
                        className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary"
                        disabled={isConfigLoading}
                      />
                    </div>
                    <div className="text-foreground/40 text-xs mt-1">Percent</div>
                  </div>
                </div>
              </div>

              {/* Footnote and Save Button - Inline - Pushed to bottom */}
              <div className="flex items-center justify-between pt-2 border-t border-border">
                <p className="text-xs text-muted-foreground">
                  <span className="text-status-warning">*</span> Indicates a required field
                </p>
                <Button
                  type="submit"
                  variant={configChanged ? "default" : "outline"}
                  size="lg"
                  className="ml-4"
                  disabled={isConfigLoading || isSavingConfig || !configChanged}
                >
                  {isSavingConfig ? (
                    <>
                      <div className="w-3 h-3 border-2 border-t-transparent border-current rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      {/* <SaveIcon /> */}
                      <span>Save Parameters</span>
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Slippage & Fees Tab */}
            <div className={cn(
              "flex-1 flex flex-col justify-between",
              activeTab !== "slippage_fees" && "hidden"
            )}>
              {/* Parameters Grid */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                {/* Buy Slippage Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-white text-xs mb-2">BUY SLIPPAGE</div>
                  <div className="flex-1 flex items-center">
                    <Input
                      id="buy_slippage"
                      name="buy_slippage"
                      type="number"
                      step="0.1"
                      min="0.1"
                      placeholder="e.g., 1.5"
                      defaultValue={configData?.buy_slippage_bps !== null && configData?.buy_slippage_bps !== undefined ? (configData.buy_slippage_bps / 100).toString() : ''}
                      onChange={() => setConfigChanged(true)}
                      className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary"
                      disabled={isConfigLoading}
                    />
                  </div>
                  <div className="text-foreground/40 text-xs mt-1">Percent</div>
                </div>

                {/* Sell Slippage Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-white text-xs mb-2">SELL SLIPPAGE</div>
                  <div className="flex-1 flex items-center">
                    <Input
                      id="sell_slippage"
                      name="sell_slippage"
                      type="number"
                      step="0.1"
                      min="0.1"
                      placeholder="e.g., 1.5"
                      defaultValue={configData?.sell_slippage_bps !== null && configData?.sell_slippage_bps !== undefined ? (configData.sell_slippage_bps / 100).toString() : ''}
                      onChange={() => setConfigChanged(true)}
                      className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary"
                      disabled={isConfigLoading}
                    />
                  </div>
                  <div className="text-foreground/40 text-xs mt-1">Percent</div>
                </div>

                {/* Priority Fee Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between col-span-2">
                  <div className="text-white text-xs mb-2 flex items-center gap-2">
                    PRIORITY FEE
                    <button
                      type="button"
                      onClick={() => setPriorityFeeInfoOpen(true)}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <Info className="h-3 w-3" />
                    </button>
                  </div>
                  <div className="flex-1 flex items-center justify-center">
                    <Input
                      id="priority_fee_sol"
                      name="priority_fee_sol"
                      type="number"
                      step="1"
                      min="0"
                      placeholder="e.g., 10000"
                      defaultValue={configData?.priority_fee_microlamports?.toString() || ""}
                      onChange={() => setConfigChanged(true)}
                      className="text-center text-lg font-medium bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-primary max-w-[200px]"
                      disabled={isConfigLoading}
                    />
                  </div>
                  <div className="text-foreground/40 text-xs mt-1 text-center">μLamports/CU</div>
                </div>
              </div>

              {/* Footnote and Save Button - Inline - Pushed to bottom */}
              <div className="flex items-center justify-between pt-2 border-t border-border">
                <p className="text-xs text-muted-foreground">
                  <span className="text-status-warning">*</span> Indicates a required field
                </p>
                <Button
                  type="submit"
                  variant={configChanged ? "default" : "outline"}
                  size="lg"
                  className="ml-4"
                  disabled={isConfigLoading || isSavingConfig || !configChanged}
                >
                  {isSavingConfig ? (
                    <>
                      <div className="w-3 h-3 border-2 border-t-transparent border-current rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      {/* <SaveIcon /> */}
                      <span>Save Parameters</span>
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Hidden inputs to ensure all form fields are always present in DOM */}
            <div className="hidden">
              {/* Hidden inputs for Buy & Sell tab when on Slippage & Fees tab */}
              {activeTab === "slippage_fees" && (
                <>
                  <Input
                    name="max_buy_sol"
                    type="number"
                    defaultValue={configData?.max_buy_sol?.toString() || ""}
                    tabIndex={-1}
                  />
                  <Input
                    name="tp_percent"
                    type="number"
                    defaultValue={configData?.tp_percent?.toString() || ""}
                    tabIndex={-1}
                  />
                  <Input
                    name="sl_percent"
                    type="number"
                    defaultValue={configData?.sl_percent?.toString() || ""}
                    tabIndex={-1}
                  />
                </>
              )}

              {/* Hidden inputs for Slippage & Fees tab when on Buy & Sell tab */}
              {activeTab === "buy_sell" && (
                <>
                  <Input
                    name="buy_slippage"
                    type="number"
                    defaultValue={configData?.buy_slippage_bps !== null && configData?.buy_slippage_bps !== undefined ? (configData.buy_slippage_bps / 100).toString() : ''}
                    tabIndex={-1}
                  />
                  <Input
                    name="sell_slippage"
                    type="number"
                    defaultValue={configData?.sell_slippage_bps !== null && configData?.sell_slippage_bps !== undefined ? (configData.sell_slippage_bps / 100).toString() : ''}
                    tabIndex={-1}
                  />
                  <Input
                    name="priority_fee_sol"
                    type="number"
                    defaultValue={configData?.priority_fee_microlamports?.toString() || ""}
                    tabIndex={-1}
                  />
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </form>

      <style jsx global>{`
        /* Ensure placeholders have reduced opacity */
        input::placeholder {
          color: rgba(255, 255, 255, 0.4) !important;
          opacity: 1;
        }
      `}</style>
    </div>
  );
} 