@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Vibrant blue colors matching your specification */
    --background: 235 85% 30%; /* Rich vibrant blue #120a8f */
    --foreground: 0 0% 100%; /* Pure white text */

    /* Card backgrounds - Rich blue matching the vibrant theme */
    --card-bg: 225 95% 35%; /* Rich blue for cards #0038a8 */
    --card-foreground: 0 0% 100%; /* White text */

    --popover-bg: 225 95% 35%; /* Same as cards */
    --popover-foreground: 0 0% 100%;

    /* Primary brand colors - Vibrant cyan-blue from reference */
    --primary-brand: 195 100% 50%; /* Vibrant cyan-blue #00d4ff */
    --primary-brand-foreground: 230 50% 8%; /* Dark background for contrast */

    --secondary-action: 195 100% 50%; /* Matching vibrant cyan-blue */
    --secondary-action-foreground: 0 0% 100%; /* White text */

    /* Accent colors - Vibrant cyan from reference dashboard */
    --accent-main: 195 100% 50%; /* Vibrant cyan #00d4ff */
    --accent-main-foreground: 230 50% 8%; /* Dark text for contrast */

    /* Interactive states */
    --interactive-hover: 195 100% 60%; /* Lighter cyan for hover */
    --interactive-hover-foreground: 0 0% 100%;

    /* Status colors - Vibrant colors for dark blue theme */
    --status-destructive: 0 85% 60%;     /* Vibrant red for errors */
    --status-destructive-foreground: 0 0% 100%;
    --status-success: 120 70% 50%;      /* Vibrant green for success */
    --status-success-foreground: 0 0% 100%;
    --status-warning: 35 90% 55%;       /* Vibrant orange for warnings */
    --status-warning-foreground: 0 0% 100%;

    /* Borders and inputs - EXACT match from reference */
    --border-subtle: 220 15% 30%;       /* Subtle borders */
    --input-bg: 220 20% 18%;            /* Input backgrounds */
    --input-border: 220 15% 35%;        /* Input borders */
    --input-ring: 195 100% 50%;         /* Cyan focus ring */
    --input-placeholder: 0 0% 50%;      /* Gray placeholder text */
    --muted-foreground: 0 0% 70%;       /* Secondary text */
    --border: 220 15% 30%;              /* Default border color */

    --radius: 0.75rem; /* Keep existing radius */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply antialiased;
  }
  body {
    @apply text-foreground overflow-x-hidden tracking-tighter;
    /* Expanded dark areas - more black extending towards center */
    background: linear-gradient(135deg, #000000 0%, #000000 20%, #120a8f 40%, #0038a8 50%, #120a8f 60%, #000000 80%, #000000 100%);
    min-height: 100vh;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tighter;
  }
  p, span, div, button, input, textarea, label {
    @apply tracking-tighter;
  }
}

@layer components {
  .gradient-text-blue {
    @apply bg-gradient-to-r from-blue-400 to-accent bg-clip-text text-transparent;
  }

  .gradient-text-primary-to-white {
    @apply bg-gradient-to-r from-primary to-white bg-clip-text !text-transparent;
  }
  
  .subheading-text-style {
    @apply text-sm font-medium text-white tracking-tighter; /* Base: size, weight, default color */
  }

  .neon-border-primary {
    @apply relative border border-primary/30; /* Subtle border without glow */
  }



  /* Slightly lighter cards - perfect balance */
  .glass-card {
    background: rgba(0, 0, 0, 0.65) !important; /* A bit lighter for perfect balance */
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important; /* Subtle white border */
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.7) !important; /* Good shadow depth */
  }

  .glass-card-hover {
    transition: all 0.3s ease !important;
  }

  .glass-card-hover:hover {
    background: rgba(18, 10, 143, 0.6) !important; /* Slightly lighter blue on hover #120a8f */
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 6px 25px 0 rgba(0, 0, 0, 0.8) !important;
    transform: translateY(-2px) !important;
  }

  /* ML vs Manual Filters Section Animations */
  @keyframes pulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(0.95);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Floating animations for background icons */
  @keyframes float-1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }

  @keyframes float-2 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-3deg); }
  }

  @keyframes float-3 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(7deg); }
  }

  @keyframes float-4 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-18px) rotate(-5deg); }
  }

  @keyframes title-glow {
    0% { filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.3)); }
    50% { filter: drop-shadow(0 0 30px rgba(239, 68, 68, 0.4)); }
    100% { filter: drop-shadow(0 0 20px rgba(52, 211, 153, 0.3)); }
  }

  @keyframes pulse-slow {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
  }

  .animate-float-1 {
    animation: float-1 6s ease-in-out infinite;
  }

  .animate-float-2 {
    animation: float-2 6s ease-in-out infinite 2s;
  }

  .animate-float-3 {
    animation: float-3 6s ease-in-out infinite 4s;
  }

  .animate-float-4 {
    animation: float-4 6s ease-in-out infinite 1s;
  }

  .animate-title-glow {
    animation: title-glow 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
  }

  .bg-radial-gradient {
    background: radial-gradient(circle, rgba(96, 165, 250, 0.1) 0%, transparent 70%);
  }

  /* Gradient shift animation for CTA border */
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animate-gradient-shift {
    background-size: 300% 300%;
    animation: gradient-shift 4s ease infinite;
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Landing page specific animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* Header slide animations */
@keyframes slide-down {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
}

.animate-slide-down {
  animation: slide-down 0.5s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out forwards;
}

/* Header transition effects */
.header-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-shadow-glow {
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(0, 212, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.4);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.6);
}


