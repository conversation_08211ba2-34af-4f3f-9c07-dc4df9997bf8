"use client";

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from './ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';

interface RiskLadderData {
  rug_pull_pct: number;
  small_gains_pct: number;
  good_profit_pct: number;
  big_gains_pct: number;
  to_the_moon_pct: number;
  pool_sample_size?: number;
  total_tokens?: number;
  last_updated_utc?: string;
  status?: string;
}

interface RiskLadderTableProps {
  title: string;
  subtitle: string;
  data: RiskLadderData | null;
  isLoading?: boolean;
  variant: 'sniper' | 'signals';
}

const RiskLadderTable: React.FC<RiskLadderTableProps> = ({
  title,
  subtitle,
  data,
  isLoading = false,
  variant
}) => {
  // Define risk ladder categories with smooth red-to-green gradient transition
  const riskCategories = [
    {
      label: 'Rug Pull (-100%)',
      key: 'rug_pull_pct' as keyof RiskLadderData,
      color: 'text-red-500',
      bgColor: 'bg-red-500/10',
      emoji: '💀'
    },
    {
      label: 'Small Gains (0-100%)',
      key: 'small_gains_pct' as keyof RiskLadderData,
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      emoji: '😐'
    },
    {
      label: 'Good Profit (100-200%)',
      key: 'good_profit_pct' as keyof RiskLadderData,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
      emoji: '✅'
    },
    {
      label: 'Big Gains (200-500%)',
      key: 'big_gains_pct' as keyof RiskLadderData,
      color: 'text-lime-400',
      bgColor: 'bg-lime-500/10',
      emoji: '💎'
    },
    {
      label: 'To The Moon (500%+)',
      key: 'to_the_moon_pct' as keyof RiskLadderData,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      emoji: '🚀'
    }
  ];

  // Calculate total count for display
  const totalCount = data?.pool_sample_size || data?.total_tokens || 0;

  // Get the appropriate color scheme based on variant
  const getVariantColors = () => {
    if (variant === 'sniper') {
      return {
        titleColor: 'text-red-400',
        borderColor: 'border-red-500/30',
        accentColor: 'text-red-300'
      };
    } else {
      return {
        titleColor: 'text-green-400',
        borderColor: 'border-green-500/30',
        accentColor: 'text-green-300'
      };
    }
  };

  const colors = getVariantColors();

  if (isLoading) {
    return (
      <Card className={`glass-card glass-card-hover ${colors.borderColor} border-2 relative overflow-hidden`}>
        {/* Top gradient border */}
        <div className={`absolute top-0 left-0 right-0 h-1 ${variant === 'sniper' ? 'bg-gradient-to-r from-red-500 via-orange-500 to-red-500' : 'bg-gradient-to-r from-green-500 via-emerald-400 to-green-500'}`}></div>

        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-full bg-gray-500 flex-shrink-0"></div>
            <CardTitle className={`${colors.titleColor} text-2xl leading-7 mb-0`}>
              {title}
            </CardTitle>
          </div>
          <p className="text-sm text-gray-400 font-light font-figtree tracking-tight text-left">
            {subtitle}
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex justify-between items-center p-3 rounded-lg bg-gray-800/20 animate-pulse">
                <div className="h-4 bg-gray-600 rounded w-32"></div>
                <div className="h-4 bg-gray-600 rounded w-16"></div>
                <div className="h-4 bg-gray-600 rounded w-12"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.status === 'no_data') {
    return (
      <Card className={`glass-card glass-card-hover ${colors.borderColor} border-2`}>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-gray-500"></div>
            <CardTitle className={`${colors.titleColor} text-lg`}>
              {title}
            </CardTitle>
          </div>
          <p className="text-sm text-gray-400 font-light font-figtree tracking-tight">
            {subtitle}
          </p>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-400 text-sm">No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`glass-card glass-card-hover ${colors.borderColor} border-2 relative overflow-hidden`}>
      {/* Top gradient border */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${variant === 'sniper' ? 'bg-gradient-to-r from-red-500 via-orange-500 to-red-500' : 'bg-gradient-to-r from-green-500 via-emerald-400 to-green-500'}`}></div>

      <CardHeader>
        <div className="flex items-center space-x-3">
          <div className={`w-12 h-12 rounded-full ${variant === 'sniper' ? 'bg-red-500' : 'bg-green-500'} flex-shrink-0`}></div>
          <CardTitle className={`${colors.titleColor} text-2xl leading-7 mb-0`}>
            {title}
          </CardTitle>
        </div>
        <p className="text-sm text-gray-400 font-light font-figtree tracking-tight mt-2 text-left">
          {subtitle}
        </p>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className="border-border/20">
              <TableHead className="text-gray-300 font-medium text-xs whitespace-nowrap w-auto">ROI Range</TableHead>
              <TableHead className="text-gray-300 font-medium text-xs text-right whitespace-nowrap w-16">Count</TableHead>
              <TableHead className="text-gray-300 font-medium text-xs text-right whitespace-nowrap w-16">Percent</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {riskCategories.map((category) => {
              const percentage = data[category.key] as number || 0;
              const count = Math.round((percentage / 100) * totalCount);
              
              return (
                <TableRow key={category.key} className="border-border/10 hover:bg-white/5">
                  <TableCell className="py-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{category.emoji}</span>
                      <span className={`text-sm font-medium ${category.color} whitespace-nowrap`}>
                        {category.label}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right py-2">
                    <span className="text-sm text-gray-300 whitespace-nowrap">{count}</span>
                  </TableCell>
                  <TableCell className="text-right py-2">
                    <span className={`text-sm font-medium ${category.color} whitespace-nowrap`}>
                      {percentage.toFixed(1)}%
                    </span>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        
        {/* Footer with summary stats */}
        <div className="mt-6 grid grid-cols-2 gap-4">
          <div className="text-center p-3 border border-green-500/20 rounded-lg">
            <div className="text-sm text-gray-400 mb-1">Profitable Trades</div>
            <div className="text-xl font-bold text-green-400">
              {((data.small_gains_pct || 0) +
                (data.good_profit_pct || 0) +
                (data.big_gains_pct || 0) +
                (data.to_the_moon_pct || 0)).toFixed(1)}%
            </div>
          </div>
          <div className="text-center p-3 border border-red-500/20 rounded-lg">
            <div className="text-sm text-gray-400 mb-1">Total Losses</div>
            <div className="text-xl font-bold text-red-400">
              {(data.rug_pull_pct || 0).toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Last updated footnote */}
        {data.last_updated_utc && (
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Last updated: {new Date(data.last_updated_utc).toLocaleString('en-US', {
                timeZone: 'UTC',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })} UTC
            </p>
          </div>
        )}

        {/* Legal Disclaimer */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            * Past performance does not guarantee future results. All trading involves risk of loss.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskLadderTable;
