import { useState } from 'react';
import { useAuth } from '@/components/auth-context';
import { BotStatusEnum, BotState } from '@/types/api';
import { fetchAuthenticatedApi } from '@/lib/api';
import { toast } from 'sonner';

/**
 * Hook for managing bot control state and functionality
 */
export function useBotControl(
  currentUiBotStatus: BotStatusEnum | null,
  setUiBotStatusOptimistically: (status: BotStatusEnum) => void,
  currentBotStateFromApi: BotState | null | undefined
) {
  const [isBotControlLoading, setIsBotControlLoading] = useState<boolean>(false);
  const { isAuthenticated } = useAuth();
  
  // Handle bot control click (start/stop)
  const handleBotControlClick = async () => {
    if (!currentBotStateFromApi || !isAuthenticated || !currentUiBotStatus) return;
    
    setIsBotControlLoading(true);
    
    // Determine target endpoint based on current status
    const targetEndpoint = currentUiBotStatus === BotStatusEnum.RUNNING 
      ? '/api/bot/stop' 
      : '/api/bot/start';
    
    try {
      const response = await fetchAuthenticatedApi<BotState>(targetEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.success && response.data) {
        // Update the UI status with the new status from the response
        setUiBotStatusOptimistically(response.data.status);
        console.log(`Bot successfully ${currentUiBotStatus === BotStatusEnum.RUNNING ? 'stopped' : 'started'}`);
      } else {
        // If API call fails, revert UI status back to the one in current bot state
        if (currentBotStateFromApi?.status) {
          setUiBotStatusOptimistically(currentBotStateFromApi.status);
        }
        console.error('Bot control API error:', response.error);
        toast.error(`Failed to ${currentUiBotStatus === BotStatusEnum.RUNNING ? 'stop' : 'start'} bot: ${response.error}`);
      }
    } catch (error) {
      // If exception occurs, revert UI status back to the one in current bot state
      if (currentBotStateFromApi?.status) {
        setUiBotStatusOptimistically(currentBotStateFromApi.status);
      }
      console.error('Bot control error:', error);
      toast.error(`Failed to ${currentUiBotStatus === BotStatusEnum.RUNNING ? 'stop' : 'start'} bot. Please try again.`);
    } finally {
      setIsBotControlLoading(false);
    }
  };

  return {
    isBotControlLoading,
    handleBotControlClick
  };
} 