"use client"

import { useEffect, useState, useRef } from "react"
import { useWallet, useConnection } from "@solana/wallet-adapter-react"
import { PublicKey } from "@solana/web3.js"
import { toast } from "sonner"
import { useWebSocketManager } from "@/hooks"
import { useDashboardManager } from "@/hooks"
import { useBotControl } from "@/hooks"
import { useTransactionProposal } from "@/hooks"
import { buildVersionedTx } from "@/lib/solanaUtils"
import { useAuth } from "@/components/auth-context"
import { useBotConfig } from '@/hooks'
import { useManualTrading } from '@/hooks'
import { useAppInfo } from '@/hooks'

// Components
import Header from '@/components/header'
import ServerDownModal from '@/components/server-down-modal'
import ProposalModal from '@/components/proposal-modal'
import DashboardMetricsGrid from '@/components/DashboardMetricsGrid'
import HoldingsSection from '@/components/holdings-section'
import RecentTradesCard from '@/components/recent-trades-card'
import TradeParametersForm from '@/components/TradeParametersForm'
import { BotStatusEnum } from "@/types/api"

// Define Token Metadata Program ID constant
const MPL_TOKEN_METADATA_PROGRAM_ID = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");

// Dashboard component
function Dashboard() {
  // State to manage the active view
  const [activeView, setActiveView] = useState<"open_positions" | "history">("open_positions")
  
  // Session timer state
  const [sessionElapsedTime, setSessionElapsedTime] = useState<string>("00:00")
  const sessionStartTimeRef = useRef<number | null>(null)
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null)
  
  // Get app info from our hook
  const { treasuryWalletAddress, isLoading: isLoadingAppInfo, error: appInfoError } = useAppInfo();
  
  // Get dashboard data from our new hook
  const {
    dashboardData,
    isDashboardLoading,
    dashboardError,
    uiBotStatus,
    setUiBotStatus,
    fetchData,
    livePrices,
    monitoringStatusMap
  } = useDashboardManager();
  
  // Configuration data states - replaced with useBotConfig hook
  const {
    configData,
    isConfigLoading,
    configError,
    isSavingConfig,
    configSaveStatus,
    configChanged,
    handleConfigSave,
    setConfigChanged,
    clearConfigSaveStatus
  } = useBotConfig();

  const { connection } = useConnection()
  const { publicKey, signTransaction, sendTransaction, wallet } = useWallet()
  const { isAuthenticated, isLoadingAuth, authUserWallet } = useAuth()

  // Bot control states from enhanced hook
  const {
    isBotControlLoading,
    handleBotControlClick
  } = useBotControl(
    uiBotStatus,
    setUiBotStatus,
    dashboardData?.bot_state
  );
  
  // Helper function to format time as mm:ss
  const formatElapsedTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Start the session timer
  const startSessionTimer = () => {
    // Clear any existing timer
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }
    
    // Set the start time
    sessionStartTimeRef.current = Date.now();
    
    // Update the timer immediately
    setSessionElapsedTime("00:00");
    
    // Set up the interval to update the timer
    timerIntervalRef.current = setInterval(() => {
      if (sessionStartTimeRef.current) {
        const elapsed = Date.now() - sessionStartTimeRef.current;
        setSessionElapsedTime(formatElapsedTime(elapsed));
      }
    }, 1000);
  };

  // Stop the session timer
  const stopSessionTimer = () => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }
    sessionStartTimeRef.current = null;
    setSessionElapsedTime("00:00");
  };

  // Handle timer based on bot status changes
  useEffect(() => {
    if (uiBotStatus === BotStatusEnum.RUNNING) {
      startSessionTimer();
    } else {
      stopSessionTimer();
    }
    
    // Cleanup on unmount
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, [uiBotStatus]);
  
  // Custom wrapper for handleBotControlClick to handle session timer
  const handleBotControlWithTimer = async () => {
    await handleBotControlClick();
  };

  // Transaction Proposal Modal states from hook
  const {
    currentProposal,
    isProposalModalOpen,
    isProcessingProposal,
    proposalStartTime,
    handleTransactionProposal,
    handleModalClose,
    clearCurrentProposal,
    handleCancelProposal,
    executeApprovedProposal,
    processNextSellProposal
  } = useTransactionProposal(
    publicKey,
    sendTransaction,
    connection,
    wallet?.adapter || null,
    fetchData,
    configData,
    treasuryWalletAddress ? treasuryWalletAddress.toString() : null,
    signTransaction
  );
  
  // Get manual trading state and handlers
  const {
    sellingMint,
    buyingMint: hookBuyingMint,
    handleManualSell,
    handlePartialSell,
    handleManualBuy
  } = useManualTrading(
    publicKey,
    sendTransaction,
    connection,
    wallet?.adapter || null,
    fetchData,
    configData,
    buildVersionedTx,
    treasuryWalletAddress ? treasuryWalletAddress.toString() : null
  );
  
  // WebSocket state from the new hook
  const { 
    wsStatus, 
    showServerDownModal, 
    connectionAttemptsCurrent, 
    isCheckingServer 
  } = useWebSocketManager(
    fetchData, 
    handleTransactionProposal,
    (serverInfo) => {
      console.log("Server Info from hook (page.tsx):", serverInfo);
    }
  );

  // Clear config save status when active view changes
  useEffect(() => {
    clearConfigSaveStatus();
  }, [activeView, clearConfigSaveStatus]);

  // Handle app info loading or error
  useEffect(() => {
    if (appInfoError) {
      console.error("Error loading app info:", appInfoError);
      toast.error("Could not load treasury wallet information");
    }
  }, [appInfoError, toast]);

  return (
    <div className="relative min-h-screen">
      {/* Server Down Modal */}
      <ServerDownModal
        isOpen={showServerDownModal}
        connectionAttempts={connectionAttemptsCurrent}
        isCheckingServer={isCheckingServer}
      />

      <Header
        wsStatus={wsStatus}
        activeView={activeView}
        setActiveView={setActiveView}
      />

      <div className="container mx-auto px-6 py-8">
        {/* Main Content */}
        <main className="w-full">
          {/* Trade Parameters and Bot Control Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Trade Parameters Form - Half width */}
            <TradeParametersForm
              configData={configData}
              isConfigLoading={isConfigLoading}
              isSavingConfig={isSavingConfig}
              configChanged={configChanged}
              configSaveStatus={configSaveStatus}
              handleConfigSave={handleConfigSave}
              setConfigChanged={setConfigChanged}
              clearConfigSaveStatus={clearConfigSaveStatus}
            />

            {/* Bot Control Card with Session Metrics - Half width */}
            <DashboardMetricsGrid
              uiBotStatus={uiBotStatus}
              isBotControlLoading={isBotControlLoading}
              handleBotControlClick={handleBotControlWithTimer}
              isAuthenticated={isAuthenticated}
              configData={configData}
              botSessionState={dashboardData?.bot_state ? {
                buy_count: dashboardData.bot_state.buy_count ?? undefined,
                sell_count: dashboardData.bot_state.sell_count ?? undefined,
                sol_invested: dashboardData.bot_state.sol_invested ?? undefined,
                pnl_percent: dashboardData.bot_state.pnl_percent ?? undefined,
                pnl_sol: dashboardData.bot_state.pnl_sol ?? undefined
              } : undefined}
              sessionElapsedTime={sessionElapsedTime}
              showOnlyBotControl={true}
            />
          </div>

          {/* Holdings Section - Always shown regardless of view */}
          <div className="space-y-6">
            <HoldingsSection
              holdings={dashboardData?.holdings || []}
              isLoading={isDashboardLoading || isLoadingAppInfo}
              livePrices={livePrices}
              sellingMint={sellingMint}
              onSell={handleManualSell}
              onPartialSell={handlePartialSell}
              onBuy={handleManualBuy}
              buyingMint={hookBuyingMint}
              monitoringStatus={monitoringStatusMap}
              disabled={!treasuryWalletAddress}
            />
            
            {/* Enhanced Recent Trades Card with Trade History Tab */}
            <RecentTradesCard 
              trades={dashboardData?.recent_trades}
              isLoading={isDashboardLoading}
              activeView={activeView}
              setActiveView={setActiveView}
            />
          </div>
        </main>
      </div>

      {/* Transaction Proposal Modal */}
      <ProposalModal
        isOpen={isProposalModalOpen}
        onClose={handleModalClose}
        proposal={currentProposal}
        isLoading={isProcessingProposal}
        onApprove={executeApprovedProposal}
        onReject={handleCancelProposal}
        startTime={proposalStartTime}
      />

    </div>
  );
}

export default Dashboard;
