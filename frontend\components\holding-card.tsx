"use client";

import React, { useState } from 'react';
import { HoldingItem } from '@/types/api';
import TokenChartWidget from './token-chart-widget';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { toast } from 'sonner';
import { TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface HoldingCardProps {
    holding: HoldingItem;
    livePrice: number | null;
    onSell: (holding: HoldingItem) => void;
    isSelling: boolean;
    onPartialSell: (holding: HoldingItem, percentage: number) => void;
    onBuy?: (holding: HoldingItem, solAmount: number) => void;
    isBuying?: boolean;
    disabled?: boolean;
}

const HoldingCard: React.FC<HoldingCardProps> = ({ holding, livePrice, onSell, isSelling, onPartialSell, onBuy, isBuying = false, disabled = false }) => {
    const [partialSellPercent, setPartialSellPercent] = useState('100');
    const [buyAmount, setBuyAmount] = useState('');

    // Calculate P&L
    let livePnlPercent: number | null = null;
    // Only use live price if monitoring is active
    const currentPrice = holding.monitoring_active 
        ? (livePrice ?? holding.current_price_sol) // Use live price, fallback to fetched DB price
        : null; // Force null if monitoring is inactive
    
    if (currentPrice !== null && holding.average_buy_price_sol > 1e-12) { // Avoid division by zero
        livePnlPercent = ((currentPrice / holding.average_buy_price_sol) - 1) * 100.0;
    }

    const widgetContainerId = `holding-chart-${holding.token_mint}`;

    const handleBuy = () => {
        const amount = parseFloat(buyAmount);
        if (isNaN(amount) || amount <= 0) {
            toast.error("Please enter a valid SOL amount greater than 0");
            return;
        }
        
        // Additional validation for reasonable SOL amount (e.g., not more than 1000 SOL)
        if (amount > 1000) {
            toast.error("Amount seems too large. Please enter a reasonable SOL amount");
            return;
        }
        
        if (onBuy) {
            onBuy(holding, amount);
        } else {
            toast.error("Buy functionality not yet implemented");
        }
    };

    const handleSellPercentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Only allow numeric values
        if (value === '' || /^\d+$/.test(value)) {
            setPartialSellPercent(value);
        }
    };

    const handleBuyAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Allow decimal numbers (with up to 9 decimal places for SOL)
        if (value === '' || /^\d*\.?\d{0,9}$/.test(value)) {
            setBuyAmount(value);
        }
    };

    const handleSellPercent = () => {
        const percentage = parseInt(partialSellPercent, 10);
        if (isNaN(percentage)) {
            toast.error("Please enter a valid percentage");
            return;
        }
        
        if (percentage < 1 || percentage > 100) {
            toast.error("Please enter a percentage between 1 and 100");
            return;
        }
        
        onPartialSell(holding, percentage > 99 ? 100 : percentage);
    };

    return (
        <Card className="overflow-hidden flex flex-col shadow-md glass-card">
            {/* Chart Area */}
            <CardContent className="p-0 flex-grow relative">
                <TokenChartWidget
                    mintAddress={holding.token_mint}
                    widgetContainerId={widgetContainerId}
                    className="w-full h-96 md:h-96"
                />
            </CardContent>

            {/* Data Section */}
            <div className="p-2 border-t border-border text-sm flex items-center justify-between">
                {/* Token Amount */}
                <div className="px-3 text-center">
                    <div className="flex items-center gap-1">
                        <span className="text-muted-foreground text-sm">Token Amount:</span>
                        <span className="font-mono text-foreground">{holding.amount_held.toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>
                    </div>
                </div>

                {/* Live P&L */}
                <div className="px-3 text-center">
                    <div className="flex items-center gap-1">
                        <span className="text-muted-foreground text-sm">Live P&L:</span>
                        {livePnlPercent !== null ? (
                            <span className={`font-mono flex items-center gap-1 ${
                                livePnlPercent >= 0 ? 'text-status-success' : 'text-status-error'
                            }`}>
                                {livePnlPercent >= 0 
                                    ? <TrendingUp className="h-3 w-3" /> 
                                    : <TrendingDown className="h-3 w-3" />}
                                {livePnlPercent >= 0 ? '+' : ''}{livePnlPercent.toFixed(1)}%
                            </span>
                        ) : (
                            <span className="font-mono text-muted-foreground italic">
                                N/A
                            </span>
                        )}
                        {!holding.monitoring_active && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <span className="ml-2 cursor-help">
                                            <AlertCircle className="h-4 w-4 text-status-warning inline-block" />
                                            <span className="text-status-warning">  Inactive token</span>
                                        </span>
                                    </TooltipTrigger>
                                    <TooltipContent side="top" className="text-xs max-w-xs">
                                        <p>
                                            Price monitoring is currently inactive for this token, likely due to stagnant price action.
                                            Automated sell proposals (e.g., TP/SL) will not trigger.
                                            Manual trading is still available.
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )}
                    </div>
                </div>
                
                {/* Price Info */}
                <div className="px-3 text-center">
                    <div className="flex items-center gap-1">
                        <span className="text-muted-foreground text-sm">Current Price:</span>
                        {currentPrice !== null ? (
                            <span className="font-mono text-foreground">{currentPrice.toFixed(13)} SOL</span>
                        ) : (
                            <span className="font-mono text-muted-foreground italic">
                                {holding.monitoring_active ? 'Loading...' : 'N/A'}
                            </span>
                        )}
                    </div>
                </div>
            </div>

            {/* Footer with Buy Option and Partial Sell */}
            <CardFooter className="p-4 border-t border-border flex flex-row items-center justify-between gap-4">
                {/* Buy Input and Button - Inline */}
                <div className="flex items-center gap-2">
                    <div className="relative w-[160px]">
                        <Input
                            id={`buyAmount-${holding.token_mint}`}
                            type="text"
                            inputMode="decimal"
                            value={buyAmount}
                            onChange={handleBuyAmountChange}
                            placeholder="Enter amount"
                            className="w-full pr-10"
                            disabled={isSelling || isBuying || disabled}
                            style={{ appearance: 'textfield' }} /* Remove increment arrows */
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none text-muted-foreground text-xs">
                            SOL
                        </div>
                    </div>
                    <Button
                        variant="neonPrimary"
                        size="lg"
                        className="w-[180px]"
                        disabled={isSelling || isBuying || disabled}
                        onClick={handleBuy}
                    >
                        {isBuying ? '...' : 'Buy'}
                    </Button>
                </div>

                {/* Empty middle space */}
                <div className="flex-grow"></div>

                {/* Sell Input and Button - Inline */}
                <div className="flex items-center gap-2">
                    <div className="relative w-[160px]">
                        <Input
                            id={`partialSellPercent-${holding.token_mint}`}
                            type="text"
                            inputMode="numeric"
                            min="1"
                            max="100"
                            value={partialSellPercent}
                            onChange={handleSellPercentChange}
                            placeholder="Enter percentage"
                            className="w-full pr-8"
                            disabled={isSelling || disabled}
                            style={{ appearance: 'textfield' }} /* Remove increment arrows */
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none text-muted-foreground text-xs">
                            %
                        </div>
                    </div>
                    <Button
                        variant="neonDestructive"
                        size="lg"
                        className="w-[180px]"
                        disabled={isSelling || disabled}
                        onClick={handleSellPercent}
                    >
                        {isSelling ? '...' : 'Sell'}
                    </Button>
                </div>
                
                {/* Treasury address warning if disabled */}
                {disabled && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="absolute top-3 left-3 flex items-center justify-center bg-[#000000] rounded-full p-1.5 border border-status-error/50">
                                    <AlertCircle className="w-4 h-4 text-status-error" />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                                <p className="text-xs">Trading disabled: Treasury wallet address not configured.</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
            </CardFooter>
        </Card>
    );
};

export default HoldingCard; 