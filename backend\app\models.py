# models.py
import joblib
import os
import sys

# Ensure parent directory is in sys.path for proper imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))) 
from config.logging_config import setup_logging
import logging
import json

# Initialize logging
setup_logging()

# Get the module-level logger
logger = logging.getLogger(__name__)

# Paths to the ml_pipeline artifacts directory
ARTIFACTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'artifacts')

# IMPORTANT: Specify which ml_pipeline run to use
# Example: 'run_20250626_135000' to use a specific run
# If empty, the latest run will be used
SPECIFIED_ML_PIPELINE_RUN = 'run_20250626_135000'


# Get model artifacts path based on the specified run or latest run
def get_artifacts_path():
    """
    Get the path to the specified ml_pipeline run or latest run if none specified.

    Returns:
        str: Path to the run directory containing all artifacts
    """
    if SPECIFIED_ML_PIPELINE_RUN and os.path.isdir(os.path.join(ARTIFACTS_DIR, SPECIFIED_ML_PIPELINE_RUN)):
        run_dir_name = SPECIFIED_ML_PIPELINE_RUN
        logger.info(f"Using specified ml_pipeline run: {SPECIFIED_ML_PIPELINE_RUN}")
    else:
        # If no specific run is specified, find the latest one
        run_dirs = [d for d in os.listdir(ARTIFACTS_DIR)
                    if os.path.isdir(os.path.join(ARTIFACTS_DIR, d))
                    and d.startswith('run_')]

        if not run_dirs:
            raise FileNotFoundError(f"No ml_pipeline run directories found in {ARTIFACTS_DIR}")

        # Sort by timestamp (directories are named run_YYYYMMDD_HHMMSS)
        run_dir_name = sorted(run_dirs, key=lambda x: x.split('_')[1:])[-1]
        logger.info(f"Using latest ml_pipeline run: {run_dir_name}")

    run_dir_path = os.path.join(ARTIFACTS_DIR, run_dir_name)

    if not os.path.exists(run_dir_path):
        raise FileNotFoundError(f"ML pipeline run directory not found at {run_dir_path}")

    logger.info(f"Found ml_pipeline run directory: {run_dir_path}")
    return run_dir_path


class ModelLoader:
    _xgboost_model = None
    _feature_list = None
    _model_path = None
    _scaler = None
    _optimal_threshold = 0.6  # Default threshold
    _has_calibrator = False
    _calibration_method = None
    _calibrator = None

    @staticmethod
    def load_model():
        if ModelLoader._xgboost_model is None:
            try:
                # Get the artifacts directory path
                artifacts_path = get_artifacts_path()
                ModelLoader._model_path = artifacts_path

                # Load run metadata
                metadata_path = os.path.join(artifacts_path, "run_metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        run_metadata = json.load(f)
                    best_model = run_metadata['models']['best_model']
                    logger.info(f"Best model identified from metadata: {best_model}")
                else:
                    # Default to xgboost if metadata not found
                    best_model = "xgboost"
                    logger.warning(f"Run metadata not found at {metadata_path}, defaulting to {best_model}")

                # Load the XGBoost model using joblib
                model_path = os.path.join(artifacts_path, "models", f"{best_model}.joblib")
                if os.path.exists(model_path):
                    ModelLoader._xgboost_model = joblib.load(model_path)
                    logger.info(f"XGBoost model loaded successfully from {model_path}")
                else:
                    raise FileNotFoundError(f"XGBoost model file not found at {model_path}")

                # Load feature list from selected_features.json
                features_path = os.path.join(artifacts_path, "preprocessing", "selected_features.json")
                if os.path.exists(features_path):
                    try:
                        with open(features_path, 'r') as f:
                            features_data = json.load(f)

                        if "feature_names" in features_data:
                            ModelLoader._feature_list = features_data["feature_names"]
                            logger.info(f"Loaded {len(ModelLoader._feature_list)} features from {features_path}")
                        else:
                            logger.error(f"No 'feature_names' key found in {features_path}")
                            raise KeyError("feature_names not found in selected_features.json")
                    except Exception as e:
                        logger.error(f"Could not load features from {features_path}: {e}")
                        raise
                else:
                    raise FileNotFoundError(f"Features file not found: {features_path}")

                # Load the scaler (if scaling was used)
                scaler_path = os.path.join(artifacts_path, "preprocessing", "feature_scaler.joblib")

                # Check preprocessing params to see if scaling was used
                preprocessing_params_path = os.path.join(artifacts_path, "preprocessing", "preprocessing_params.json")
                scaling_method = None
                if os.path.exists(preprocessing_params_path):
                    try:
                        with open(preprocessing_params_path, 'r') as f:
                            preprocessing_params = json.load(f)
                        scaling_method = preprocessing_params.get('scaling_method')
                    except Exception as e:
                        logger.warning(f"Could not read preprocessing params: {e}")

                if scaling_method is None:
                    # No scaling was used in training, so no scaler needed
                    ModelLoader._scaler = None
                    logger.info("No scaling method used in training - scaler set to None")
                elif os.path.exists(scaler_path):
                    try:
                        ModelLoader._scaler = joblib.load(scaler_path)
                        logger.info(f"Scaler loaded successfully from {scaler_path}")
                    except Exception as e:
                        error_msg = f"Failed to load scaler from {scaler_path}: {str(e)}"
                        logger.error(error_msg)
                        raise RuntimeError(error_msg)
                else:
                    error_msg = f"Scaling method '{scaling_method}' was used but no scaler found at {scaler_path}"
                    logger.error(error_msg)
                    raise FileNotFoundError(error_msg)

                # If feature list is still None, try to get it from the scaler
                if ModelLoader._feature_list is None and ModelLoader._scaler is not None:
                    if hasattr(ModelLoader._scaler, 'feature_names_in_'):
                        ModelLoader._feature_list = list(ModelLoader._scaler.feature_names_in_)
                        logger.info(f"Extracted {len(ModelLoader._feature_list)} features from scaler's feature_names_in_")
                    else:
                        logger.warning("Scaler does not have 'feature_names_in_'. Feature list remains unknown.")

                # Load preprocessing parameters (optional)
                preprocessing_params_path = os.path.join(artifacts_path, "preprocessing", "preprocessing_params.json")
                if os.path.exists(preprocessing_params_path):
                    try:
                        with open(preprocessing_params_path, 'r') as f:
                            _ = json.load(f)  # Load but don't store for now
                        logger.info(f"Loaded preprocessing parameters from {preprocessing_params_path}")
                    except Exception as e:
                        logger.warning(f"Could not load preprocessing parameters: {e}")

                # Set default values for ml_pipeline models (no calibrator support yet)
                ModelLoader._optimal_threshold = 0.6  # Default threshold
                ModelLoader._has_calibrator = False
                ModelLoader._calibration_method = None
                logger.info(f"Using default settings: threshold={ModelLoader._optimal_threshold}, "
                         f"has_calibrator={ModelLoader._has_calibrator}")

                # Note: ml_pipeline artifacts don't include calibrators yet
                # This can be extended in the future if calibration is added to ml_pipeline

            except Exception as e:
                logger.error(f"Error loading XGBoost model or related components: {str(e)}")
                raise
        else:
            logger.info("XGBoost model was already loaded.")

    @staticmethod
    def load_scaler():
        if ModelLoader._scaler is None:
            ModelLoader.load_model()

    @staticmethod
    def get_xgboost_model():
        """Get the XGBoost model instance."""
        if ModelLoader._xgboost_model is None:
            ModelLoader.load_model()
        return ModelLoader._xgboost_model
        
    @staticmethod
    def get_model():
        """Get the model instance (legacy method for backward compatibility)."""
        return ModelLoader.get_xgboost_model()

    @staticmethod
    def get_model_path():
        """Get the path to the loaded model artifacts directory."""
        if ModelLoader._model_path is None:
            ModelLoader.load_model()
        return ModelLoader._model_path

    @staticmethod
    def get_feature_list():
        """Get the list of features used by the model."""
        if ModelLoader._feature_list is None:
            ModelLoader.load_model()
        
        return ModelLoader._feature_list

    @staticmethod
    def get_scaler():
        """Get the scaler used by the model."""
        if ModelLoader._scaler is None:
            ModelLoader.load_model()
        return ModelLoader._scaler

    @staticmethod
    def get_thresholds():
        """Get prediction thresholds with class 0 calculated from class 1 threshold."""
        optimal_threshold = ModelLoader.get_optimal_threshold()
        return {
            1: optimal_threshold,
            0: 1 - optimal_threshold  # Automatically calculate class 0 threshold
        }
        
    @staticmethod
    def get_optimal_threshold():
        """Get the optimal prediction threshold."""
        if ModelLoader._xgboost_model is None:
            ModelLoader.load_model()
        return ModelLoader._optimal_threshold
        
    @staticmethod
    def get_calibrator():
        """Get the loaded probability calibrator instance."""
        if ModelLoader._calibrator is None and ModelLoader._has_calibrator: # Check _has_calibrator to avoid reloading if intentionally None
            ModelLoader.load_model() # Ensure model (and thus calibrator) loading is attempted
        return ModelLoader._calibrator
        
    @staticmethod
    def get_calibration_settings():
        """Get the calibration settings from model metadata."""
        if ModelLoader._xgboost_model is None:
            ModelLoader.load_model()
        return {
            'has_calibrator': ModelLoader._has_calibrator,
            'method': ModelLoader._calibration_method
        }
        
    @staticmethod
    def load_model_and_artifacts():
        """Load the model and all its associated artifacts (backward compatibility method)."""
        ModelLoader.load_model()  # This will load the model, scaler, features, metadata, and calibrator
        logger.info("ModelLoader.load_model_and_artifacts() completed successfully.")
        return True
