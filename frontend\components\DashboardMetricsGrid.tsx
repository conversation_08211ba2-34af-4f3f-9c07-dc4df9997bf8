import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from "@/components/ui/card"
import { BotStatusEnum, BotState, ConfigurationResponse } from "@/types/api"
import { AlertCircle, Play } from "lucide-react"
import Orb from "@/components/ui/orb"
import { AuthButton } from "@/components/auth-button"
import { useMemo } from "react"

interface DashboardMetricsGridProps {
  uiBotStatus: BotStatusEnum | null;
  isBotControlLoading: boolean;
  handleBotControlClick: () => void;
  isAuthenticated: boolean;
  configData: ConfigurationResponse | null;
  botSessionState: {
    buy_count?: number;
    sell_count?: number;
    sol_invested?: number;
    pnl_percent?: number;
    pnl_sol?: number;
  } | null | undefined;
  sessionElapsedTime: string;
  showOnlyBotControl?: boolean;
}

export default function DashboardMetricsGrid({
  uiBotStatus,
  isBotControlLoading,
  handleBotControlClick,
  isAuthenticated,
  configData,
  botSessionState,
  sessionElapsedTime,
  showOnlyBotControl = false
}: DashboardMetricsGridProps) {
  // Memoize color values to prevent recreation on every render
  const orbColors = useMemo(() => {
    // Always use the same blue colors regardless of bot status
    return {
      color1: [0.2, 0.6, 0.9] as [number, number, number], // Soft Blue (matching dashboard theme)
      color2: [0.1, 0.4, 0.7] as [number, number, number], // Darker blue variant
      color3: [0.05, 0.2, 0.5] as [number, number, number], // Even darker blue
    };
  }, []); // Remove uiBotStatus dependency

  if (showOnlyBotControl) {
    return (
      <div className="mb-6">
        {/* Bot Control Card with Session Metrics - Full width when shown alone */}
        <Card className="w-full h-full flex flex-col glass-card glass-card-hover">
          <CardHeader>
            <CardTitle>Bot Control</CardTitle>
          </CardHeader>

          <CardContent className="flex-1 flex">
            {/* Left Column - Bot Control */}
            <div className="flex flex-col items-center justify-center py-6 w-1/2 pr-6">
              <div className="relative w-52 h-52 mb-6">
                {/* Orb background - fills the container naturally */}
                <Orb
                  color1={orbColors.color1}
                  color2={orbColors.color2}
                  color3={orbColors.color3}
                  hoverIntensity={
                    uiBotStatus === BotStatusEnum.RUNNING
                      ? 1.0 // Active state intensity (as per user preference)
                      : 0.0 // Default intensity when stopped
                  }
                  rotateOnHover={false}
                  forceHoverState={uiBotStatus === BotStatusEnum.RUNNING}
                  disableMouseHover={true}
                />

                {/* Status text - absolutely positioned and centered */}
                <span className="absolute inset-0 flex items-center justify-center text-2xl font-bold text-white text-center leading-tight z-10">
                  {uiBotStatus === BotStatusEnum.RUNNING ? "ACTIVE" : "STOPPED"}
                </span>
              </div>

              {!isAuthenticated ? (
                // Show AuthButton when not authenticated
                <AuthButton />
              ) : (
                // Show regular bot control button when authenticated
                <Button
                  variant={
                    uiBotStatus === BotStatusEnum.RUNNING
                      ? 'destructive'
                      : 'default'
                  }
                  size="lg"
                  onClick={handleBotControlClick}
                  disabled={isBotControlLoading ||
                          (uiBotStatus !== BotStatusEnum.RUNNING &&
                          (!configData?.max_buy_sol || !configData?.tp_percent || !configData?.sl_percent))}
                  title={
                    (!configData?.max_buy_sol || !configData?.tp_percent || !configData?.sl_percent)
                      ? "Required trading parameters must be set before starting the bot"
                      : ""
                  }
                >
                  {isBotControlLoading ? (
                    // Processing state
                    <>
                      <div className="w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></div>
                      <span>Processing...</span>
                    </>
                  ) : uiBotStatus === BotStatusEnum.RUNNING ? (
                    // Stop Bot state
                    <>
                      <div className="relative w-3 h-3">
                        <div className="absolute inset-0 bg-white rounded-full animate-ping opacity-75"></div>
                        <div className="relative w-3 h-3 bg-white rounded-full"></div>
                      </div>
                      <span>Stop Bot</span>
                    </>
                  ) : (!configData?.max_buy_sol || !configData?.tp_percent || !configData?.sl_percent) ? (
                    <>
                      <AlertCircle className="w-4 h-4" />
                      <span>Start Bot</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span>Start Bot</span>
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Right Column - Session Overview */}
            <div className="w-1/2 pl-6 border-l border-border flex flex-col py-6">
              <div className="text-sm font-medium text-foreground/60 mb-6">SESSION OVERVIEW</div>

              {/* Metrics Grid - 2x2 layout with increased spacing */}
              <div className="grid grid-cols-2 gap-4 flex-1">
                {/* Duration Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-foreground/60 text-xs mb-2">DURATION</div>
                  <div className="text-3xl font-medium text-foreground">{sessionElapsedTime || "00:00"}</div>
                  <div className="text-foreground/40 text-xs mt-1">Active time</div>
                </div>

                {/* Trades Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-foreground/60 text-xs mb-2">TRADES</div>
                  <div className="text-3xl font-medium flex items-center gap-1">
                    <span className="text-foreground">{botSessionState?.buy_count || 0}</span>
                    <span className="text-foreground/60 text-xl">/</span>
                    <span className="text-foreground">{botSessionState?.sell_count || 0}</span>
                  </div>
                  <div className="text-foreground/40 text-xs mt-1">Buy / Sell</div>
                </div>

                {/* Invested Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-foreground/60 text-xs mb-2">INVESTED</div>
                  <div className="text-3xl font-medium text-invested">
                    {botSessionState?.sol_invested?.toFixed(2) || 0}
                  </div>
                  <div className="text-foreground/40 text-xs mt-1">SOL</div>
                </div>

                {/* P&L Card */}
                <div className="glass-card glass-card-hover rounded-lg p-4 flex flex-col justify-between">
                  <div className="text-foreground/60 text-xs mb-2">P&L</div>
                  <div className={`text-3xl font-medium ${
                    parseFloat(botSessionState?.pnl_percent?.toString() || '0') > 0
                      ? 'text-status-success'
                      : parseFloat(botSessionState?.pnl_percent?.toString() || '0') < 0
                        ? 'text-status-error'
                        : 'text-foreground'
                  }`}>
                    {botSessionState?.pnl_percent && parseFloat(botSessionState?.pnl_percent.toString()) > 0 ? '+' : ''}
                    {botSessionState?.pnl_percent?.toFixed(2) || 0}%
                  </div>
                  <div className="text-foreground/40 text-xs mt-1">Total return</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mb-6">
      {/* Session Metrics Card - Full width */}
      <Card className="flex flex-col h-full min-h-[180px] glass-card glass-card-hover">
        <CardHeader>
          <CardTitle>Session Metrics</CardTitle>
        </CardHeader>
        <CardContent className="flex-grow flex items-center justify-center py-4">
          <div className="w-full">
            {/* Header row for labels */}
            <div className="grid grid-cols-4 gap-4 mb-2">
              <div className="text-foreground/60 text-sm text-center">Session Duration</div>
              <div className="text-foreground/60 text-sm text-center">Trades (Buys/Sells)</div>
              <div className="text-foreground/60 text-sm text-center">Invested (SOL)</div>
              <div className="text-foreground/60 text-sm text-center">P&L (%)</div>
            </div>

            {/* Values row */}
            <div className="grid grid-cols-4 gap-4">
              {/* Session Duration */}
              <div className="flex justify-center items-center">
                <div className="text-6xl font-medium text-foreground">{sessionElapsedTime || "00:00"}</div>
              </div>

              {/* Trades Metrics */}
              <div className="flex justify-center items-center">
                <div className="text-6xl font-medium flex items-center gap-2">
                  <span className="text-foreground">{botSessionState?.buy_count || 0}</span>
                  <span className="text-foreground px-1">/</span>
                  <span className="text-foreground">{botSessionState?.sell_count || 0}</span>
                </div>
              </div>

              {/* Investment Metrics */}
              <div className="flex justify-center items-center">
                <div className="text-6xl font-medium text-invested">
                  {botSessionState?.sol_invested?.toFixed(2) || 0}
                </div>
              </div>

              {/* P&L Metrics */}
              <div className="flex justify-center items-center">
                <div className={`text-6xl font-medium ${
                  parseFloat(botSessionState?.pnl_percent?.toString() || '0') > 0
                    ? 'text-status-success'
                    : parseFloat(botSessionState?.pnl_percent?.toString() || '0') < 0
                      ? 'text-status-error'
                      : 'text-foreground'
                }`}>
                  {botSessionState?.pnl_percent && parseFloat(botSessionState?.pnl_percent.toString()) > 0 ? '+' : ''}
                  {botSessionState?.pnl_percent?.toFixed(2) || 0}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}