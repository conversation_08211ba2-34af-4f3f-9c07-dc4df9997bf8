"use client"

import { useCallback } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { Button } from "@/components/ui/button"
import { fetchSiwsChallenge, verifySiwsOnBackend } from "@/lib/api"
import { useAuth } from "@/components/auth-context"

export function AuthButton() {
  const { publicKey, signIn, connected, connecting, disconnecting } = useWallet()
  const { 
    isAuthenticated, 
    setIsAuthenticated, 
    setAuthUserWallet
  } = useAuth()
  
  const handleSignIn = useCallback(async () => {
    if (!publicKey || !signIn) {
      console.error("Wallet not connected or signIn not supported")
      return
    }

    try {
      console.log("Fetching SIWS challenge nonce...")
      const challenge = await fetchSiwsChallenge()
      if (!challenge?.nonce) return // Error already logged by fetch function

      // Get the current domain with protocol
      const domain = window.location.host
      const uri = `${window.location.protocol}//${domain}`
      
      // Construct a more complete SIWS message with all fields backend expects
      const signInInput = {
        domain,
        statement: "Sign in to SolBot Windsurf to manage your bot.",
        version: "1",
        chainId: "solana:mainnet",
        uri,
        nonce: challenge.nonce,
        issuedAt: new Date().toISOString()
      }

      console.log("Calling adapter.signIn() with input:", signInInput)
      // Trigger the sign-in process via the adapter
      const output = await signIn(signInInput)
      console.log("Received signIn output:", output)

      // Send to backend for verification
      const verificationResult = await verifySiwsOnBackend(signInInput, output)

      if (verificationResult && verificationResult.access_token) {
          // SUCCESS! Store the JWT securely
          localStorage.setItem('authToken', verificationResult.access_token)
          localStorage.setItem('walletAddress', verificationResult.walletAddress)
          console.log("Authentication successful! JWT stored.")
          // Update application state to reflect authentication
          setIsAuthenticated(true)
          setAuthUserWallet(verificationResult.walletAddress)
      } else {
          // Verification failed on backend
          console.error("Backend SIWS verification failed.")
          setIsAuthenticated(false)
          setAuthUserWallet(null)
      }
    } catch (error) {
      console.error("Sign-In process error:", error)
      setIsAuthenticated(false)
      setAuthUserWallet(null)
    }
  }, [publicKey, signIn, setIsAuthenticated, setAuthUserWallet])

  // Client-side only
  if (typeof window === "undefined") return null

  // If wallet isn't connected, this button shouldn't render (Header handles Connect)
  if (!connected) return null

  // If authenticated, this button shouldn't render (Header handles Sign Out)
  if (isAuthenticated) return null

  // Only render the Sign In button if connected but not authenticated
  return (
    <Button
      onClick={handleSignIn}
      variant="default"
      size="lg"
      disabled={connecting || disconnecting || !publicKey || !signIn}
      className=""
    >
      Sign In
    </Button>
  )
} 