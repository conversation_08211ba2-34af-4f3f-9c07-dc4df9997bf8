from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime
from app.db_models import BotState, User
from app.schemas.bot_schemas import BotStatusEnum
from app.crud.user_crud import user_crud
import logging

# Get the module-level logger
logger = logging.getLogger(__name__)

async def get_bot_state(db: AsyncSession, user_wallet: str) -> BotState | None:
    """
    Get the current bot state for a user.
    
    Args:
        db: AsyncSession - The database session.
        user_wallet: str - The wallet address of the user.
        
    Returns:
        BotState | None - The bot state object or None if not found.
    """
    query = select(BotState).where(BotState.user_wallet == user_wallet)
    result = await db.execute(query)
    return result.scalars().first()

async def set_bot_status(db: AsyncSession, user_wallet: str, status: BotStatusEnum) -> BotState:
    """
    Set the bot status for a user.
    
    Args:
        db: AsyncSession - The database session.
        user_wallet: str - The wallet address of the user.
        status: BotStatusEnum - The new status to set.
        
    Returns:
        BotState - The updated bot state object.
    """
    # Check if a bot state exists for this user
    state = await get_bot_state(db, user_wallet)
    
    if state:
        # Update existing state
        state.status = status
        state.last_changed = datetime.utcnow()
        
        # Set session_start_time based on the new status
        if status == BotStatusEnum.RUNNING:
            state.session_start_time = datetime.utcnow()
        elif status in [BotStatusEnum.STOPPED, BotStatusEnum.ERROR]:
            state.session_start_time = None
    else:
        # Check if user exists, if not create one
        await user_crud.get_or_create_user(db, user_wallet)
        
        # Create new bot state
        state = BotState(
            user_wallet=user_wallet,
            status=status,
            last_changed=datetime.utcnow(),
            session_start_time=datetime.utcnow() if status == BotStatusEnum.RUNNING else None
        )
        db.add(state)
    
    await db.commit()
    await db.refresh(state)
    
    return state 

async def get_bot_state_by_user(db: AsyncSession, user: User) -> BotState | None:
    """
    Get the current bot state for a user object.
    
    Args:
        db: AsyncSession - The database session.
        user: User - The user object.
        
    Returns:
        BotState | None - The bot state object or None if not found.
    """
    return await get_bot_state(db, user.wallet_address) 