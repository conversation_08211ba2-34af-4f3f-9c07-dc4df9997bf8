import { Connection, PublicKey, VersionedTransaction, Transaction, SystemProgram, TransactionInstruction } from '@solana/web3.js';
import { WalletContextState } from '@solana/wallet-adapter-react';
import { AnchorProvider } from '@coral-xyz/anchor';
import { PumpSdk, getBuyTokenAmountFromSolAmount, getSellSolAmountFromTokenAmount } from '@pump-fun/pump-sdk';
import { toast } from 'sonner';
import BN from 'bn.js';
import { buildVersionedTx } from './solanaUtils';
import { TRADE_FEE_PERCENTAGE, LAMPORTS_PER_SOL } from './constants';
import { getJupiterQuote, getJupiterSwapTransaction, SOL_MINT } from './jupiter';

interface ExecuteTradeArgs {
  connection: Connection;
  wallet: WalletContextState;
  mintAddress: string;
  tradeType: 'buy' | 'sell';
  solAmount?: number; // For buys, this is the TOTAL amount the user wants to spend (including fee)
  tokenAmount?: number; // For sells
  slippageBps: number;
  treasuryWalletAddress: string | null; // Your developer fee wallet
  priorityFeeMicroLamports?: number | null; // Optional priority fee
}

// Fee calculation helper functions (matching existing patterns)
interface BuyFeeCalculationResult {
  originalSolAmount: number;
  feeAmountSol: number;
  amountForTokenSol: number;
  feeLamports: number;
  solLamportsForToken: BN;
}

interface SellFeeCalculationResult {
  feeLamports: number;
  netSolOutputLamports: bigint;
  feeAmountSol: number;
  netSolReceived: number;
}

function calculateBuyFeeAndAmounts(originalSolAmount: number): BuyFeeCalculationResult {
  const feeAmountSol = originalSolAmount * TRADE_FEE_PERCENTAGE;
  const amountForTokenSol = originalSolAmount - feeAmountSol;
  const feeLamports = Math.floor(feeAmountSol * LAMPORTS_PER_SOL);
  const solLamportsForToken = new BN(Math.floor(amountForTokenSol * LAMPORTS_PER_SOL));

  return {
    originalSolAmount,
    feeAmountSol,
    amountForTokenSol,
    feeLamports,
    solLamportsForToken
  };
}

function calculateSellFeeAndAmounts(grossSolOutputLamports: bigint): SellFeeCalculationResult {
  const grossSolOutputNumber = Number(grossSolOutputLamports);
  const feeAmountSol = grossSolOutputNumber / LAMPORTS_PER_SOL * TRADE_FEE_PERCENTAGE;
  const feeLamports = Math.floor(grossSolOutputNumber * TRADE_FEE_PERCENTAGE);
  const netSolOutputLamports = grossSolOutputLamports - BigInt(feeLamports);
  const netSolReceived = Number(netSolOutputLamports) / LAMPORTS_PER_SOL;

  return {
    feeLamports,
    netSolOutputLamports,
    feeAmountSol,
    netSolReceived
  };
}

export async function executePumpFunTrade({
  connection,
  wallet,
  mintAddress,
  tradeType,
  solAmount,
  tokenAmount,
  slippageBps,
  treasuryWalletAddress,
  priorityFeeMicroLamports,
}: ExecuteTradeArgs): Promise<string | null> {
  if (!wallet.publicKey || !wallet.signTransaction) {
    toast.error('Wallet not connected or does not support signing.');
    return null;
  }
  if (!treasuryWalletAddress) {
    toast.error('Treasury wallet is not configured. Cannot proceed.');
    return null;
  }

  const treasuryWallet = new PublicKey(treasuryWalletAddress);
  const mint = new PublicKey(mintAddress);
  const sdk = new PumpSdk(connection);

  try {
    // Check if token is on an active bonding curve
    const bondingCurveAccount = await sdk.fetchBondingCurve(mint);
    if (!bondingCurveAccount) {
      toast.error(`Could not find bonding curve for token ${mintAddress}.`);
      return null;
    }

    const globalAccount = await sdk.fetchGlobal();
    let signature: string;

    if (!bondingCurveAccount.complete) {
      // --- Active Bonding Curve Logic (pump-sdk) ---
      console.log(`Token ${mintAddress} is on an active bonding curve. Using pump-sdk.`);

      if (tradeType === 'buy') {
        if (!solAmount) throw new Error('SOL amount is required for a buy trade.');

        // Calculate fee and amounts using helper function
        const { feeAmountSol, amountForTokenSol, feeLamports, solLamportsForToken } = calculateBuyFeeAndAmounts(solAmount);

        console.log(`[Pump.fun Buy Fee] Original: ${solAmount} SOL, Fee: ${feeAmountSol} SOL, For Trade: ${amountForTokenSol} SOL`);

        // Get expected token output using the new SDK function
        const expectedTokenOutputBN = getBuyTokenAmountFromSolAmount(globalAccount, bondingCurveAccount, solLamportsForToken, false);
        const minTokenOutputWithSlippageBN = expectedTokenOutputBN.mul(new BN(10000 - slippageBps)).div(new BN(10000));

        console.log(`Expected token output: ${expectedTokenOutputBN.toString()}, Min with slippage: ${minTokenOutputWithSlippageBN.toString()}`);

        // Get buy instructions using the new SDK API
        const bondingCurveAccountInfo = await connection.getAccountInfo(sdk.bondingCurvePda(mint));
        const buyInstructions = await sdk.buyInstructions(
          globalAccount,
          bondingCurveAccountInfo,
          bondingCurveAccount,
          mint,
          wallet.publicKey,
          minTokenOutputWithSlippageBN,
          solLamportsForToken,
          slippageBps / 100, // Convert basis points to percentage
          bondingCurveAccount.creator
        );

        // Add fee instruction at the beginning
        const feeInstruction = SystemProgram.transfer({
          fromPubkey: wallet.publicKey,
          toPubkey: treasuryWallet,
          lamports: feeLamports,
        });
        buyInstructions.unshift(feeInstruction);

        // Build and send transaction
        const legacyTransaction = new Transaction().add(...buyInstructions);
        const versionedTransaction = await buildVersionedTx(connection, wallet.publicKey, legacyTransaction, priorityFeeMicroLamports);
        const signedTransaction = await wallet.signTransaction(versionedTransaction);
        signature = await connection.sendTransaction(signedTransaction);

      } else { // 'sell'
        if (!tokenAmount) throw new Error('Token amount is required for a sell trade.');

        const tokenInfo = await connection.getParsedAccountInfo(mint);
        const decimals = (tokenInfo.value?.data as any)?.parsed?.info?.decimals ?? 6;
        const tokenAmountBaseUnits = new BN(tokenAmount * Math.pow(10, decimals));

        // Get expected SOL output using the new SDK function
        const grossSolOutputBN = getSellSolAmountFromTokenAmount(globalAccount, bondingCurveAccount, tokenAmountBaseUnits);

        // Calculate fee using helper function
        const { feeLamports, netSolOutputLamports, feeAmountSol, netSolReceived } = calculateSellFeeAndAmounts(grossSolOutputBN.toBigInt());
        console.log(`[Pump.fun Sell Fee] Expected Gross: ${grossSolOutputBN.toString()} L, Fee: ${feeLamports} L, Net: ${netSolOutputLamports} L`);

        // Apply slippage to the gross amount (before fee deduction)
        const minSolOutputWithSlippageBN = grossSolOutputBN.mul(new BN(10000 - slippageBps)).div(new BN(10000));

        // Get sell instructions using the new SDK API
        const bondingCurveAccountInfo = await connection.getAccountInfo(sdk.bondingCurvePda(mint));
        const sellInstructions = await sdk.sellInstructions(
          globalAccount,
          bondingCurveAccountInfo,
          mint,
          wallet.publicKey,
          tokenAmountBaseUnits,
          minSolOutputWithSlippageBN,
          slippageBps / 100 // Convert basis points to percentage
        );

        // Add fee instruction
        const feeInstruction = SystemProgram.transfer({
          fromPubkey: wallet.publicKey,
          toPubkey: treasuryWallet,
          lamports: feeLamports,
        });
        sellInstructions.push(feeInstruction);

        // Build and send transaction
        const legacyTransaction = new Transaction().add(...sellInstructions);
        const versionedTransaction = await buildVersionedTx(connection, wallet.publicKey, legacyTransaction, priorityFeeMicroLamports);
        const signedTransaction = await wallet.signTransaction(versionedTransaction);
        signature = await connection.sendTransaction(signedTransaction);
      }
    } else {
      // --- Migrated Token Logic (Jupiter) ---
      console.log(`Token ${mintAddress} has migrated to an AMM pool. Using Jupiter.`);

      if (tradeType === 'buy') {
        if (!solAmount) throw new Error('SOL amount is required for a buy trade.');

        // Calculate fee and amounts using helper function
        const { feeAmountSol, amountForTokenSol, feeLamports } = calculateBuyFeeAndAmounts(solAmount);

        console.log(`[Jupiter Buy Fee] Original: ${solAmount} SOL, Fee: ${feeAmountSol} SOL, For Trade: ${amountForTokenSol} SOL`);

        const inputMintStr = SOL_MINT;
        const outputMintStr = mintAddress;

        // Get Jupiter quote using amount after fee deduction
        const quoteResponse = await getJupiterQuote(inputMintStr, outputMintStr, Math.floor(amountForTokenSol * LAMPORTS_PER_SOL).toString(), slippageBps);
        if (!quoteResponse) {
          throw new Error("Failed to get Jupiter quote for buy.");
        }

        // Get swap instructions based on quote
        const swapApiResponse = await getJupiterSwapTransaction(
          quoteResponse,
          wallet.publicKey.toBase58(),
          treasuryWalletAddress,
          priorityFeeMicroLamports
        );
        if (!swapApiResponse?.swapTransaction) {
          throw new Error("Failed to get swap transaction from Jupiter API.");
        }

        // Deserialize and sign the transaction
        const swapTransactionBuf = Buffer.from(swapApiResponse.swapTransaction, 'base64');
        const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
        const signedTransaction = await wallet.signTransaction(transaction);
        signature = await connection.sendTransaction(signedTransaction);

      } else { // 'sell'
        if (!tokenAmount) throw new Error('Token amount is required for a sell trade.');

        const tokenInfo = await connection.getParsedAccountInfo(mint);
        const decimals = (tokenInfo.value?.data as any)?.parsed?.info?.decimals ?? 6;
        const tokenAmountLamports = Math.floor(tokenAmount * Math.pow(10, decimals));

        const inputMintStr = mintAddress;
        const outputMintStr = SOL_MINT;

        // Get Jupiter quote
        const quoteResponse = await getJupiterQuote(inputMintStr, outputMintStr, tokenAmountLamports.toString(), slippageBps);
        if (!quoteResponse) {
          throw new Error("Failed to get Jupiter quote for sell.");
        }

        // Get swap instructions based on quote
        const swapApiResponse = await getJupiterSwapTransaction(
          quoteResponse,
          wallet.publicKey.toBase58(),
          treasuryWalletAddress,
          priorityFeeMicroLamports
        );
        if (!swapApiResponse?.swapTransaction) {
          throw new Error("Failed to get swap transaction from Jupiter API.");
        }

        // Deserialize and sign the transaction
        const swapTransactionBuf = Buffer.from(swapApiResponse.swapTransaction, 'base64');
        const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
        const signedTransaction = await wallet.signTransaction(transaction);
        signature = await connection.sendTransaction(signedTransaction);
      }
    }

    console.log(`Transaction sent with signature: ${signature}`);
    toast.info('Transaction sent...', { description: `Signature: ${signature.slice(0, 10)}...` });

    // Confirm transaction
    const latestBlockhash = await connection.getLatestBlockhash();
    await connection.confirmTransaction({
      signature,
      blockhash: latestBlockhash.blockhash,
      lastValidBlockHeight: latestBlockhash.lastValidBlockHeight
    }, 'confirmed');

    toast.success('Transaction confirmed!');
    return signature;

  } catch (error: any) {
    console.error('Trade execution failed:', error);
    toast.error('Trade Failed', { description: error?.message || 'An unknown error occurred.' });
    return null;
  }
}

/**
 * Helper function to check if a token is on an active bonding curve
 * @param connection - Solana connection
 * @param wallet - Wallet context state
 * @param mintAddress - Token mint address
 * @returns Promise<boolean> - true if on active bonding curve, false if migrated
 */
export async function isTokenOnActiveBondingCurve(
  connection: Connection,
  wallet: WalletContextState,
  mintAddress: string
): Promise<boolean | null> {
  try {
    const mint = new PublicKey(mintAddress);
    const sdk = new PumpSdk(connection);

    const bondingCurveAccount = await sdk.fetchBondingCurve(mint);
    if (!bondingCurveAccount) {
      return null; // Token not found
    }

    return !bondingCurveAccount.complete;
  } catch (error) {
    console.error('Error checking bonding curve status:', error);
    return null;
  }
}
