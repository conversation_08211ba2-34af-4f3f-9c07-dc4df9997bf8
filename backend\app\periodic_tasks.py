import asyncio
import logging
import traceback
from sqlalchemy import select, update, and_
from datetime import datetime
from fastapi import FastAPI

from app.database import async_session_factory
from app.db_models import Holding

logger = logging.getLogger(__name__)

# Import configurable constants
from solbot.config import CLEANUP_INTERVAL_SECONDS, MAX_STAGNANT_CHECKS

async def cleanup_stale_holdings(app: FastAPI):
    """
    Periodic task to deactivate monitoring for holdings with stagnant prices.
    
    This task runs every CLEANUP_INTERVAL_SECONDS and looks for holdings where:
    - monitoring_active is True
    - stagnant_check_count is >= MAX_STAGNANT_CHECKS
    
    For these holdings, it sets monitoring_active to False to stop unnecessary price checks.
    """
    # Add startup debug messages
    logger.info(f"CLEANUP TASK: Starting stale holdings cleanup service (interval: {CLEANUP_INTERVAL_SECONDS}s, threshold: {MAX_STAGNANT_CHECKS} checks)")
        
    while True:
        try:
            logger.debug("CLEANUP TASK: Starting periodic cleanup of stale holdings")            
            
            try:
                async with async_session_factory() as db:
                    # Find holdings where monitoring is active but price hasn't changed for too many checks
                    query = select(Holding.id).where(
                        and_(
                            Holding.monitoring_active == True,
                            Holding.stagnant_check_count >= MAX_STAGNANT_CHECKS
                        )
                    )
                    
                    logger.debug(f"CLEANUP TASK: Executing query for stale holdings")
                    result = await db.execute(query)
                    stale_holding_ids = result.scalars().all()
                    
                    if stale_holding_ids:
                        # If we found any stale holdings, deactivate them
                        logger.debug(f"CLEANUP TASK: Found {len(stale_holding_ids)} stagnant holdings to deactivate")
                                                
                        # Prepare update statement to deactivate all stale holdings at once
                        update_stmt = update(Holding).where(
                            Holding.id.in_(stale_holding_ids)
                        ).values(
                            monitoring_active=False,
                            updated_at=datetime.utcnow()
                        )
                        
                        # Execute the update
                        await db.execute(update_stmt)
                        await db.commit()
                        
                        logger.debug(f"CLEANUP TASK: Successfully deactivated monitoring for {len(stale_holding_ids)} stagnant holdings")                        
                    else:
                        logger.debug("CLEANUP TASK: No stagnant holdings found that need deactivation")
                                                
            except Exception as db_err:
                err_trace = traceback.format_exc()
                logger.error(f"CLEANUP TASK: Database error during stale holdings cleanup: {db_err}\n{err_trace}")
                # Don't exit the loop on database error
            
            # Sleep until the next cleanup interval
            logger.debug(f"CLEANUP TASK: Stale holdings cleanup complete. Sleeping for {CLEANUP_INTERVAL_SECONDS}s...")            
            await asyncio.sleep(CLEANUP_INTERVAL_SECONDS)
            
        except asyncio.CancelledError:
            logger.info("CLEANUP TASK: Stale holdings cleanup service shutting down...")
            break  # Exit the loop cleanly on cancellation
        except Exception as e:
            err_trace = traceback.format_exc()
            logger.error(f"CLEANUP TASK: Unexpected error in stale holdings cleanup service: {e}\n{err_trace}")            
            # Sleep a bit to avoid tight loop on unexpected errors
            await asyncio.sleep(30)  # 30 seconds instead of 5 minutes to recover faster 