import { useState, useCallback, useRef, useEffect } from 'react'
import { PublicKey, Connection } from '@solana/web3.js'
import { WalletContextState } from '@solana/wallet-adapter-react'
import { Adapter as WalletAdapter } from '@solana/wallet-adapter-base'
import { toast } from "sonner"
import { executePumpFunTrade } from '@/lib/pumpfun'
import { fetchAuthenticatedApi } from "@/lib/api"
import { HoldingItem, ConfigurationResponse } from "@/types/api"

/**
 * Custom hook for manual trading operations
 */
export function useManualTrading(
  publicKey: PublicKey | null,
  sendTransaction: WalletContextState['sendTransaction'],
  connection: Connection,
  walletAdapter: WalletAdapter | null,
  fetchDataForRefresh: () => Promise<void>,
  currentConfigData: ConfigurationResponse | null,
  buildVersionedTxFunc: (connection: Connection, payer: PublicKey, tx: any, priorityFeeMicroLamports?: number | null) => Promise<any>,
  treasuryWalletAddress: string | null
) {
  // State to track selling and buying mints
  const [sellingMint, setSellingMintInternal] = useState<string | null>(null)
  const [buyingMint, setBuyingMintInternal] = useState<string | null>(null)
  
  // Treasury wallet address ref for consistent access
  const treasuryWalletAddressRef = useRef<string | null>(treasuryWalletAddress);
  
  // Update treasury wallet address ref when it changes
  useEffect(() => {
    treasuryWalletAddressRef.current = treasuryWalletAddress;
    if (treasuryWalletAddress) {
      console.log(`[Manual Trading] Treasury wallet address set: ${treasuryWalletAddress}`);
    }
  }, [treasuryWalletAddress]);

  // Implement handleManualSell function for Pump.fun bonding curve sell transaction
  const handleManualSell = useCallback(async (holding: HoldingItem) => {
    if (!publicKey || !sendTransaction || !connection || !walletAdapter) {
      console.error("Sell prerequisites missing (wallet/connection/adapter).");
      toast.error("Wallet not connected or connection error.");
      return;
    }
    if (sellingMint) return;

    setSellingMintInternal(holding.token_mint);
    console.log(`Initiating sell for ${holding.amount_held} of ${holding.token_mint}`);

    try {
      const signature = await executePumpFunTrade({
        connection,
        wallet: { publicKey, signTransaction, sendTransaction } as any,
        mintAddress: holding.token_mint,
        tradeType: 'sell',
        tokenAmount: holding.amount_held,
        slippageBps: currentConfigData?.sell_slippage_bps ?? 150,
        treasuryWalletAddress: treasuryWalletAddressRef.current,
      });

      if (signature) {
        toast.success("Sell successful!");
        setTimeout(() => fetchDataForRefresh(), 1500);
      }
    } catch (error) {
      toast.error(`Sell failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setSellingMintInternal(null);
    }
  }, [publicKey, sendTransaction, connection, walletAdapter, sellingMint, fetchDataForRefresh, currentConfigData]);

  // Implement partial sell (for partial position selling)
  const handlePartialSell = useCallback(async (
    holding: HoldingItem,
    percentToSell: number = 50 // Default to 50% if not specified
  ) => {
    if (!publicKey || !sendTransaction || !connection || !walletAdapter) {
      toast.error("Wallet not connected or connection error.");
      return;
    }
    if (sellingMint) return;

    // For now, just sell 100% through the handleManualSell function
    // Later, this can be expanded to handle partial sells
    handleManualSell(holding);
  }, [publicKey, sendTransaction, connection, walletAdapter, sellingMint, handleManualSell]);

  // Implement handleManualBuy function for buying tokens
  const handleManualBuy = useCallback(async (holding: HoldingItem, solAmount: number) => {
    if (!publicKey || !connection || !sendTransaction || !walletAdapter) {
      console.error("Buy prerequisites missing (wallet/connection/adapter).");
      toast.error("Wallet not connected or connection error.");
      return;
    }
    if (buyingMint) return;

    setBuyingMintInternal(holding.token_mint);
    console.log(`Initiating buy for ${holding.token_mint} with ${solAmount} SOL`);

    try {
      const signature = await executePumpFunTrade({
        connection,
        wallet: { publicKey, signTransaction, sendTransaction } as any,
        mintAddress: holding.token_mint,
        tradeType: 'buy',
        solAmount: solAmount,
        slippageBps: currentConfigData?.buy_slippage_bps ?? 150,
        treasuryWalletAddress: treasuryWalletAddressRef.current,
      });

      if (signature) {
        toast.success("Buy successful!");
        setTimeout(() => fetchDataForRefresh(), 1500);
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      toast.error(`Buy failed: ${errorMsg}`);
    } finally {
      setBuyingMintInternal(null);
    }
  }, [publicKey, connection, sendTransaction, walletAdapter, buyingMint, fetchDataForRefresh, currentConfigData]);

  // Return public API from the hook
  return {
    sellingMint,
    buyingMint,
    handleManualSell,
    handlePartialSell,
    handleManualBuy
  };
} 