import os
import logging
from typing import List

# Configure logging
logger = logging.getLogger(__name__)

# MVP Bypass Toggle for BUY Signals
# Set this environment variable to "True" to force BUY signals for testing.
# Defaults to False (use actual model predictions).
FORCE_BUY_SIGNAL_MVP_BYPASS_STR = os.getenv('FORCE_BUY_SIGNAL_MVP_BYPASS', 'False')
FORCE_BUY_SIGNAL_MVP_BYPASS = FORCE_BUY_SIGNAL_MVP_BYPASS_STR.lower() in ('true', '1', 't', 'yes')

if FORCE_BUY_SIGNAL_MVP_BYPASS:
    logger.warning("ATTENTION: FORCE_BUY_SIGNAL_MVP_BYPASS is ENABLED. All predictions will be forced BUY signals.")
else:
    logger.info("FORCE_BUY_SIGNAL_MVP_BYPASS is DISABLED. Using actual ML model predictions.")

# File paths - using relative paths from backend directory
_backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Parent of solbot is backend
_data_dir = os.path.join(_backend_dir, 'data')
EARLIEST_TRANSACTIONS_FILE = os.path.join(_data_dir, 'earliest_transactions.json')
MERGED_TRANSACTIONS_FILE = os.path.join(_data_dir, 'merged_transactions_df.csv')
MERGED_TRANSACTIONS_FILE_AGG = os.path.join(_data_dir, 'merged_transactions_df_agg.csv')

# Define whether to use feature aggregation
USE_FEATURE_AGGREGATION = False  # Can be toggled True/False

# Define whether to preserve transaction-level data in merged_transactions_df.csv
PRESERVE_TRANSACTION_LEVEL_DATA = True  # When True, all transaction-level data will be preserved

# Define column patterns for transaction-level data
TRANSACTION_LEVEL_COLUMN_PATTERNS = ['price_', 'sol_volume_', 'timestamp_', 'new_buyer_', 'wallet_sold_']

# Shyft API Configuration
SHYFT_API_BASE_URL = os.getenv('SHYFT_API_BASE_URL')
if not SHYFT_API_BASE_URL:
    logger.critical("FATAL ERROR: SHYFT_API_BASE_URL environment variable not set.")
    raise ValueError("SHYFT_API_BASE_URL must be set")

api_keys_str = os.getenv('SHYFT_API_KEYS')
if not api_keys_str:
    logger.critical("FATAL ERROR: SHYFT_API_KEYS environment variable not set.")
    raise ValueError("SHYFT_API_KEYS must be set (comma-separated)")
api_keys = [key.strip() for key in api_keys_str.split(',') if key.strip()]
if not api_keys:
    logger.critical("FATAL ERROR: SHYFT_API_KEYS environment variable is empty or contains no valid keys.")
    raise ValueError("SHYFT_API_KEYS must contain at least one key")
logger.info(f"Loaded {len(api_keys)} Shyft API keys from environment.")

# Shyft RPC Endpoints Configuration
SHYFT_RPC_HISTORY_ENDPOINT_URL = os.getenv('SHYFT_RPC_HISTORY_ENDPOINT_URL')
if not SHYFT_RPC_HISTORY_ENDPOINT_URL:
    logger.critical("FATAL ERROR: SHYFT_RPC_HISTORY_ENDPOINT_URL environment variable not set.")
    raise ValueError("SHYFT_RPC_HISTORY_ENDPOINT_URL must be set")

SHYFT_RPC_STATE_ENDPOINT_URL = os.getenv('SHYFT_RPC_STATE_ENDPOINT_URL')
if not SHYFT_RPC_STATE_ENDPOINT_URL:
    logger.critical("FATAL ERROR: SHYFT_RPC_STATE_ENDPOINT_URL environment variable not set.")
    raise ValueError("SHYFT_RPC_STATE_ENDPOINT_URL must be set")

# Shyft WebSocket Configuration
# WebSocket URIs for token activity monitoring
SHYFT_WS_URI_3 = os.getenv('SHYFT_WS_URI_3')
if not SHYFT_WS_URI_3:
    logger.critical("FATAL ERROR: SHYFT_WS_URI_3 environment variable not set.")
    raise ValueError("SHYFT_WS_URI_3 must be set")

SHYFT_WS_URI_4 = os.getenv('SHYFT_WS_URI_4')
if not SHYFT_WS_URI_4:
    logger.critical("FATAL ERROR: SHYFT_WS_URI_4 environment variable not set.")
    raise ValueError("SHYFT_WS_URI_4 must be set")

# WebSocket URIs for new token listener
SHYFT_WS_URI_1 = os.getenv('SHYFT_WS_URI_1')
if not SHYFT_WS_URI_1:
    logger.critical("FATAL ERROR: SHYFT_WS_URI_1 environment variable not set.")
    raise ValueError("SHYFT_WS_URI_1 must be set")

SHYFT_WS_URI_2 = os.getenv('SHYFT_WS_URI_2')
if not SHYFT_WS_URI_2:
    logger.critical("FATAL ERROR: SHYFT_WS_URI_2 environment variable not set.")
    raise ValueError("SHYFT_WS_URI_2 must be set")

# Moralis Configuration
# Support both single key (backward compatibility) and multiple keys (new rotation feature)
MORALIS_API_KEY = os.getenv('MORALIS_API_KEY')  # Legacy single key
MORALIS_API_KEYS_STR = os.getenv('MORALIS_API_KEYS')  # New multiple keys (comma-separated)

# Parse Moralis API keys
moralis_api_keys = []
if MORALIS_API_KEYS_STR:
    # New multi-key approach
    moralis_api_keys = [key.strip() for key in MORALIS_API_KEYS_STR.split(',') if key.strip()]
    logger.info(f"Loaded {len(moralis_api_keys)} Moralis API keys for rotation")
elif MORALIS_API_KEY:
    # Backward compatibility - single key
    moralis_api_keys = [MORALIS_API_KEY.strip()]
    logger.info("Using single Moralis API key (consider adding MORALIS_API_KEYS for rotation)")
else:
    logger.warning("No Moralis API keys configured. Price fetching will fail.")

# Export for use by other modules
MORALIS_API_KEYS = moralis_api_keys

MORALIS_ENDPOINT_URL_TEMPLATE = os.getenv('MORALIS_ENDPOINT_URL_TEMPLATE')
if not MORALIS_ENDPOINT_URL_TEMPLATE:
    # Make this non-fatal, log a warning
    logger.warning("MORALIS_ENDPOINT_URL_TEMPLATE environment variable not set. Price fetching via Moralis will fail.")
    MORALIS_ENDPOINT_URL_TEMPLATE = None # Explicitly set to None if not found

# Moralis batch API configuration
MORALIS_BATCH_SIZE = int(os.getenv('MORALIS_BATCH_SIZE', '100'))  # Default to 100 tokens per batch
MORALIS_BATCH_ENDPOINT = "https://solana-gateway.moralis.io/token/mainnet/prices"

# Price monitoring frequency configuration
PRICE_CHECK_INTERVAL_SECONDS = int(os.getenv('PRICE_CHECK_INTERVAL_SECONDS', '30'))  # Default 30 seconds
logger.info(f"Price monitoring interval set to: {PRICE_CHECK_INTERVAL_SECONDS} seconds")

# Portfolio monitoring and cleanup configuration
CLEANUP_INTERVAL_SECONDS = int(os.getenv('CLEANUP_INTERVAL_SECONDS', '60'))  # Default 60 seconds
MAX_STAGNANT_CHECKS = int(os.getenv('MAX_STAGNANT_CHECKS', '50'))  # Default 50 checks
SYSTEM_MONITOR_INTERVAL_SECONDS = int(os.getenv('SYSTEM_MONITOR_INTERVAL_SECONDS', '1'))  # Default 1 second
METRICS_MONITOR_INTERVAL_SECONDS = int(os.getenv('METRICS_MONITOR_INTERVAL_SECONDS', '600'))  # Default 10 minutes

logger.info(f"Portfolio cleanup interval: {CLEANUP_INTERVAL_SECONDS} seconds")
logger.info(f"Max stagnant checks before deactivation: {MAX_STAGNANT_CHECKS}")
logger.info(f"System monitoring interval: {SYSTEM_MONITOR_INTERVAL_SECONDS} seconds")
logger.info(f"Metrics reporting interval: {METRICS_MONITOR_INTERVAL_SECONDS} seconds")

# JWT Configuration
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')
if not JWT_SECRET_KEY:
    logger.critical("FATAL ERROR: JWT_SECRET_KEY environment variable not set.")
    raise ValueError("JWT_SECRET_KEY must be set for application security")

JWT_ALGORITHM = os.getenv('JWT_ALGORITHM', 'HS256') # Provide default

jwt_expire_minutes_str = os.getenv('JWT_ACCESS_TOKEN_EXPIRE_MINUTES', '1440') # Default 24 hours
try:
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(jwt_expire_minutes_str)
except (ValueError, TypeError):
    logger.warning(f"Invalid JWT_ACCESS_TOKEN_EXPIRE_MINUTES value '{jwt_expire_minutes_str}'. Using default 1440.")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 1440

# Treasury wallet configuration
TREASURY_WALLET_ADDRESS = os.getenv('TREASURY_WALLET_ADDRESS')
if not TREASURY_WALLET_ADDRESS:
    logger.critical("FATAL ERROR: TREASURY_WALLET_ADDRESS environment variable not set.")
    raise ValueError("TREASURY_WALLET_ADDRESS must be set for fee collection.")
logger.info(f"Treasury wallet address loaded: {TREASURY_WALLET_ADDRESS}")

# Database Configuration
DATABASE_URL = os.getenv('DATABASE_URL')
if not DATABASE_URL:
    logger.critical("FATAL ERROR: DATABASE_URL environment variable not set.")
    raise ValueError("DATABASE_URL must be set")

# Import and initialize APIKeyManager
from .utilities import APIKeyManager
api_key_manager = APIKeyManager(api_keys)

# Initialize the Moralis API key manager (if keys are available)
moralis_api_key_manager = None
if MORALIS_API_KEYS:
    moralis_api_key_manager = APIKeyManager(MORALIS_API_KEYS)
    logger.info(f"Initialized Moralis API key manager with {len(MORALIS_API_KEYS)} keys")
else:
    logger.warning("No Moralis API keys available - price monitoring will be limited")

# TRANSACTIONS FETCHING WEBSOCKET ENDPOINTS
TRANSACTIONS_FETCHING_WS_URIS: List[str] = [
    SHYFT_WS_URI_3,   # Primary endpoint
    SHYFT_WS_URI_4,   # Second endpoint 
]

# Updated DESIRED_COLUMNS to include features for 100 transactions
DESIRED_COLUMNS = [
    'mint_address',
    'dev_bought_amount',
    'total_duration',
    'sol_volume_mean',
    'sol_volume_max',
    'sol_volume_min',
    'sol_volume_std',
    'sol_volume_median',
    'price_mean',
    'price_max',
    'price_min',
    'price_std',
    'price_median',
    'time_diff_mean',
    'time_diff_max',
    'time_diff_min',
    'time_diff_std',
    'time_diff_median',
    'sum_swapped_in_sol',
    'sum_swapped_out_sol',
    'num_swapped_in_sol',
    'num_swapped_out_sol',
    'sol_volume_sum',
    'sum_swapped_in_out_ratio_sol',
    'num_swapped_in_out_ratio_sol',
    'new_buyer_sum',
    'wallet_sold_sum',
    'holders',
    # Sum and count features over increments of 10
    *[f'sum_swapped_in_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sum_swapped_out_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_in_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_out_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sol_volume_sum_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sum_swapped_in_out_ratio_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_in_out_ratio_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'new_buyer_sum_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'wallet_sold_sum_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'holders_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'price_roi_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'max_roi_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sma_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'ema_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'max_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'min_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'tr_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'atr_{i}_{i+9}' for i in range(1, 100, 10)],
]

# Dynamically add holder columns up to HOLDERS_LIMIT
HOLDERS_LIMIT = 1
for i in range(1, HOLDERS_LIMIT + 1):
    DESIRED_COLUMNS.extend([
        f'holder_{i}_token_balance',
        f'holder_{i}_perc_of_total_supply',
        f'holder_{i}_sol_balance'
    ])

# Define DESIRED_COLUMNS_H2O
DESIRED_COLUMNS_H2O = [
    'mint_address', 'total_duration', 'sol_volume_mean', 'sol_volume_max', 'sol_volume_min',
    'sol_volume_std', 'sol_volume_median', 'price_mean', 'price_max', 'price_min', 'price_std',
    'price_median', 'time_diff_mean', 'time_diff_max', 'time_diff_std', 'time_diff_median',
    'sum_swapped_in_sol', 'sum_swapped_out_sol', 'num_swapped_in_sol', 'num_swapped_out_sol',
    'sol_volume_sum', 'sum_swapped_in_out_ratio_sol', 'num_swapped_in_out_ratio_sol',
    *[f'sum_swapped_in_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sum_swapped_out_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_in_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_out_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sol_volume_sum_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sum_swapped_in_out_ratio_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'num_swapped_in_out_ratio_sol_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'price_roi_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'max_roi_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'sma_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'ema_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'max_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'min_price_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'tr_{i}_{i+9}' for i in range(1, 100, 10)],
    *[f'atr_{i}_{i+9}' for i in range(1, 100, 10)],
    'holder_1_token_balance', 'holder_1_perc_of_total_supply', 'holder_1_sol_balance'
]

# Live Signals Risk Ladder Configuration
LIVE_SIGNALS_ROI_DELAY_HOURS = int(os.getenv('LIVE_SIGNALS_ROI_DELAY_HOURS', '3'))  # Default 3 hours
PROPOSAL_RESOLUTION_DELAY_SECONDS = LIVE_SIGNALS_ROI_DELAY_HOURS * 3600  # Convert to seconds for backward compatibility
logger.info(f"Live signals ROI delay set to: {LIVE_SIGNALS_ROI_DELAY_HOURS} hours ({PROPOSAL_RESOLUTION_DELAY_SECONDS} seconds)")

# Risk Ladder configuration
RISK_LADDER_POOL_SIZE = int(os.getenv('RISK_LADDER_POOL_SIZE', '100')) # Default pool size of 100
logger.info(f"Risk Ladder dynamic pool size set to: {RISK_LADDER_POOL_SIZE}")

# Define default path for the live stats JSON file relative to the backend/data directory
LIVE_RISK_LADDER_STATS_FILE_PATH = os.getenv('LIVE_RISK_LADDER_STATS_FILE_PATH', os.path.join(_data_dir, 'live_risk_ladder_stats.json'))
logger.info(f"Live Risk Ladder stats will be stored at: {LIVE_RISK_LADDER_STATS_FILE_PATH}")

# CSV Logging Configuration for New Token Listener
# Toggle to enable/disable CSV logging of detected mint addresses
CSV_LOGGING_ENABLED_STR = os.getenv('CSV_LOGGING_ENABLED', 'False')
CSV_LOGGING_ENABLED = CSV_LOGGING_ENABLED_STR.lower() in ('true', '1', 't', 'yes')

# Define default path for the CSV file to log detected mint addresses
CSV_MINT_ADDRESSES_FILE_PATH = os.getenv('CSV_MINT_ADDRESSES_FILE_PATH', os.path.join(_data_dir, 'detected_mint_addresses.csv'))

if CSV_LOGGING_ENABLED:
    logger.info(f"CSV logging is ENABLED. Detected mint addresses will be logged to: {CSV_MINT_ADDRESSES_FILE_PATH}")
else:
    logger.info("CSV logging is DISABLED. Set CSV_LOGGING_ENABLED=True to enable mint address logging.")

# Sniper Bot Simulation Risk Ladder Configuration
SNIPER_BOT_ANALYSIS_PERIOD_HOURS = int(os.getenv('SNIPER_BOT_ANALYSIS_PERIOD_HOURS', '12'))  # Default 12 hours
SNIPER_BOT_ROI_DELAY_HOURS = int(os.getenv('SNIPER_BOT_ROI_DELAY_HOURS', '3'))  # Default 3 hours

# Sniper Bot Batch Processing Configuration (Cost Optimization)
SNIPER_BOT_BATCH_SIZE = int(os.getenv('SNIPER_BOT_BATCH_SIZE', '300'))  # Default 300 tokens per batch
SNIPER_BOT_DAILY_RESOLUTION_HOUR = int(os.getenv('SNIPER_BOT_DAILY_RESOLUTION_HOUR', '2'))  # Default 2 AM
SNIPER_BOT_AUTO_RESOLUTION_ENABLED_STR = os.getenv('SNIPER_BOT_AUTO_RESOLUTION_ENABLED', 'True')
SNIPER_BOT_AUTO_RESOLUTION_ENABLED = SNIPER_BOT_AUTO_RESOLUTION_ENABLED_STR.lower() in ('true', '1', 't', 'yes')

RISK_LADDER_UPDATE_INTERVAL_MINUTES = int(os.getenv('RISK_LADDER_UPDATE_INTERVAL_MINUTES', '5'))  # Default 5 minutes

logger.info(f"Sniper Bot Analysis Period: {SNIPER_BOT_ANALYSIS_PERIOD_HOURS} hours")
logger.info(f"Sniper Bot ROI Delay: {SNIPER_BOT_ROI_DELAY_HOURS} hours")
logger.info(f"Sniper Bot Batch Size: {SNIPER_BOT_BATCH_SIZE} tokens")
logger.info(f"Sniper Bot Daily Resolution Hour: {SNIPER_BOT_DAILY_RESOLUTION_HOUR}:00")
logger.info(f"Sniper Bot Auto Resolution: {'ENABLED' if SNIPER_BOT_AUTO_RESOLUTION_ENABLED else 'DISABLED'}")
logger.info(f"Risk Ladder Update Interval: {RISK_LADDER_UPDATE_INTERVAL_MINUTES} minutes")