'use client'

import React, { ReactNode } from 'react'
import { WalletContextProvider } from "@/components/wallet-provider"
import { AuthProvider } from "@/components/auth-context"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "sonner"
import { ErrorBoundary } from './error-boundary'

// This component combines all client providers in a single non-SSR component
export default function ClientSideApp({
  children,
}: {
  children: ReactNode
}) {
  return (
    <ErrorBoundary>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} disableTransitionOnChange forcedTheme="dark">
        <WalletContextProvider>
          <AuthProvider>
            <div className="relative min-h-screen">
              {/* Main Content */}
              <main className="min-h-screen relative z-0">
                {children}
              </main>
            </div>
            <Toaster position="top-right" richColors closeButton />
          </AuthProvider>
        </WalletContextProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
} 