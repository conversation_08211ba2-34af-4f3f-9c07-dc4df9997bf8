import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON>Key } from "@solana/web3.js";

interface AppInfoResponse {
  treasuryWalletAddress: string;
}

interface AppInfoState {
  treasuryWalletAddress: PublicKey | null;
  isLoading: boolean;
  error: Error | null;
}

// Helper function to check if a string is valid base58
function isValidBase58(value: string): boolean {
  const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
  return base58Regex.test(value);
}

export function useAppInfo() {
  const [state, setState] = useState<AppInfoState>({
    treasuryWalletAddress: null,
    isLoading: true,
    error: null
  });

  useEffect(() => {
    const fetchAppInfo = async () => {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
        const response = await fetch(`${apiBaseUrl}/api/config/app-info`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Failed to fetch app information, status:", response.status, "Response:", errorText);
          throw new Error(`Failed to fetch app information. Status: ${response.status} - ${errorText}`);
        }
        
        const data: AppInfoResponse = await response.json();
        console.log("[AppInfo] Raw data from /api/config/app-info:", data);
        
        if (data.treasuryWalletAddress) {
          // Validate the wallet address before creating a PublicKey
          if (!isValidBase58(data.treasuryWalletAddress)) {
            throw new Error(`Invalid treasury wallet address: "${data.treasuryWalletAddress}" is not a valid base58 string`);
          }
          
          setState({
            treasuryWalletAddress: new PublicKey(data.treasuryWalletAddress),
            isLoading: false,
            error: null
          });
          console.log('[AppInfo] Treasury Wallet Address loaded:', data.treasuryWalletAddress);
        } else {
          throw new Error(`Treasury wallet address not found in app info response. Received: ${JSON.stringify(data)}`);
        }
      } catch (error) {
        console.error('Error fetching app info:', error);
        let errorMessage = "Unknown error fetching app info";
        if (error instanceof Error) {
          errorMessage = error.message;
        }
        setState({
          treasuryWalletAddress: null,
          isLoading: false,
          error: new Error(errorMessage)
        });
      }
    };

    fetchAppInfo();
  }, []);

  return state;
} 