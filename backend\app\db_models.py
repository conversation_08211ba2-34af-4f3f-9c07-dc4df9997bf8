from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum as PyEnum, UniqueConstraint, Boolean
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime, timezone
from app.schemas.bot_schemas import BotStatusEnum

# Define the declarative base
Base = declarative_base()

class User(Base):
    """
    SQLAlchemy model for the users table.
    
    Represents a user in the system, identified by their wallet address.
    """
    __tablename__ = 'users'
    
    wallet_address = Column(String, primary_key=True, index=True)
    is_admin = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Define relationship to configurations
    configurations = relationship("Configuration", back_populates="owner", cascade="all, delete-orphan")
    
    # Define relationship to bot state
    bot_state = relationship("BotState", back_populates="owner", uselist=False, cascade="all, delete-orphan")
    
    # Define relationships to holdings and trades
    holdings = relationship("Holding", back_populates="owner", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="owner", cascade="all, delete-orphan")

class Configuration(Base):
    """
    SQLAlchemy model for the configurations table.
    
    Stores user-specific configuration settings for trading.
    """
    __tablename__ = 'configurations'
    
    id = Column(Integer, primary_key=True, index=True)
    user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    
    # Trading parameters
    max_buy_sol = Column(Float, nullable=True)
    tp_percent = Column(Float, nullable=True)
    sl_percent = Column(Float, nullable=True)
    buy_slippage_bps = Column(Integer, default=150, nullable=False)
    sell_slippage_bps = Column(Integer, default=150, nullable=False)
    priority_fee_microlamports = Column(Integer, nullable=True, default=None)
    
    # Token screening parameters
    min_unique_wallets = Column(Integer, nullable=True)
    min_total_volume = Column(Float, nullable=True)
    max_holder1_percent = Column(Float, nullable=True)
    
    # Tracking timestamps
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Define relationship to user
    owner = relationship("User", back_populates="configurations")

class BotState(Base):
    """
    SQLAlchemy model for the bot_states table.
    
    Stores the current status of the bot for each user.
    """
    __tablename__ = 'bot_states'
    
    id = Column(Integer, primary_key=True, index=True)
    user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, unique=True, index=True)
    status = Column(PyEnum(BotStatusEnum), default=BotStatusEnum.STOPPED, nullable=False)
    last_changed = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    session_start_time = Column(DateTime, nullable=True)
    
    # Define relationship to user
    owner = relationship("User", back_populates="bot_state")

class Holding(Base):
    """
    SQLAlchemy model for the holdings table.
    
    Represents a user's token holdings with average buy price and amount.
    """
    __tablename__ = 'holdings'
    
    id = Column(Integer, primary_key=True, index=True)
    user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    token_mint = Column(String, nullable=False, index=True)
    amount_held = Column(Float, nullable=False)
    average_buy_price_sol = Column(Float, nullable=False)
    last_acquired_at = Column(DateTime, default=datetime.utcnow)
    current_price_sol = Column(Float, nullable=True)  # For storing latest price from monitor (Phase 6)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    monitoring_active = Column(Boolean, default=True, nullable=False, index=True)
    stagnant_check_count = Column(Integer, default=0, nullable=False)
    last_successful_price_fetch = Column(DateTime, nullable=True)  # Tracks when price was last successfully fetched
    
    # Relationship to user
    owner = relationship("User", back_populates="holdings")
    
    # Constraint to ensure only one holding record per user per token
    __table_args__ = (UniqueConstraint('user_wallet', 'token_mint', name='uq_user_token_holding'),)

class TradeTypeEnum(str, PyEnum):
    """
    Enum defining the types of trades that can be made.
    """
    BUY = 'BUY'
    SELL = 'SELL'

class Trade(Base):
    """
    SQLAlchemy model for the trades table.
    
    Represents a buy or sell trade made by a user.
    """
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True, index=True)
    user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    trade_type = Column(String, nullable=False)
    token_mint = Column(String, nullable=False, index=True)
    amount_token = Column(Float, nullable=False)
    price_sol = Column(Float, nullable=False)  # Avg price for this specific trade
    total_sol = Column(Float, nullable=False)  # Cost for buy, proceeds for sell
    pnl_sol = Column(Float, nullable=True)  # Calculated only for SELL trades
    tx_signature = Column(String, nullable=True, unique=True, index=True)  # Optional
    fee_amount_sol = Column(Float, nullable=True)
    fee_destination_wallet = Column(String, nullable=True)
    
    # Relationship to user
    owner = relationship("User", back_populates="trades")

class LiveModelProposals(Base):
    """
    SQLAlchemy model for the live_model_proposals table.
    
    Tracks model-generated trading proposals and their outcomes for evaluation.
    """
    __tablename__ = 'live_model_proposals'

    id = Column(Integer, primary_key=True, index=True)
    mint_address = Column(String, nullable=False, index=True)
    proposal_timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    model_version = Column(String, nullable=True)  # To track which model version made the proposal

    is_resolved = Column(Boolean, default=False, nullable=False, index=True)
    resolution_timestamp = Column(DateTime, nullable=True)
    actual_max_roi = Column(Float, nullable=True)  # Stores the calculated max_roi (profit factor)

    # Optional: Add a relationship to the User model if you want to link proposals to users
    # This would require 'user_wallet' as a ForeignKey. For now, let's keep it simpler
    # as the primary goal is a global pool of model's past BUY proposals.
    # If we add user_wallet, we'd need:
    # user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    # owner = relationship("User") # Add backref in User model if needed

    def __repr__(self):
        return f"<LiveModelProposal(id={self.id}, mint='{self.mint_address}', proposed_at='{self.proposal_timestamp}', resolved={self.is_resolved})>"


class SniperBotSimulation(Base):
    """
    SQLAlchemy model for the sniper_bot_simulations table.

    Tracks tokens detected for sniper bot simulation and their ROI outcomes.
    This simulates buying every new token as the 10th transaction.
    """
    __tablename__ = 'sniper_bot_simulations'

    id = Column(Integer, primary_key=True, index=True)
    mint_address = Column(String, nullable=False, index=True)
    launch_timestamp = Column(DateTime, nullable=False, index=True)  # When token was first detected
    signature = Column(String, nullable=True)  # Transaction signature from CSV

    is_resolved = Column(Boolean, default=False, nullable=False, index=True)
    resolution_timestamp = Column(DateTime, nullable=True)
    actual_max_roi = Column(Float, nullable=True)  # Stores the calculated max_roi (profit factor)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<SniperBotSimulation(id={self.id}, mint='{self.mint_address}', launched_at='{self.launch_timestamp}', resolved={self.is_resolved})>"