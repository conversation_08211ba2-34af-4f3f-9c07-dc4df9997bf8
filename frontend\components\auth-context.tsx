"use client"

import { createContext, useState, useContext, ReactNode, useEffect, useRef, useCallback } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { validateToken } from "@/lib/api"

// Define the AuthContext type
interface AuthContextType {
  isAuthenticated: boolean
  setIsAuthenticated: (value: boolean) => void
  authUserWallet: string | null
  setAuthUserWallet: (value: string | null) => void
  isLoadingAuth: boolean
  handleLogout: () => void
}

// Create the Auth Context with default values
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  setIsAuthenticated: () => {},
  authUserWallet: null,
  setAuthUserWallet: () => {},
  isLoadingAuth: false,
  handleLogout: () => {}
})

// Custom hook to use the Auth Context
export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

// Auth Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [authUserWallet, setAuthUserWallet] = useState<string | null>(null)
  const [isLoadingAuth, setIsLoadingAuth] = useState(true)
  const { connected, publicKey } = useWallet()
  const previousConnected = useRef(connected); // Track previous connection state

  // Logout function
  const handleLogout = useCallback(() => {
    console.log("[AuthContext] handleLogout called."); // Add log
    localStorage.removeItem('authToken');
    localStorage.removeItem('walletAddress');
    setIsAuthenticated(false);
    setAuthUserWallet(null);
  }, []); // Empty dependency array: function doesn't depend on component state/props

  // Check for existing auth on mount and validate token
  useEffect(() => {
    async function checkAndValidateAuth() {
      console.log("[AuthContext] Initializing: Checking localStorage...");
      const token = localStorage.getItem('authToken');
      const walletAddress = localStorage.getItem('walletAddress');

      if (token && walletAddress) {
        console.log("[AuthContext] Found token and wallet in localStorage. Validating token...");
        
        // Validate token is still valid
        const isValid = await validateToken();
        
        if (isValid) {
          console.log("[AuthContext] Token validation successful. Setting auth state.");
          setIsAuthenticated(true);
          setAuthUserWallet(walletAddress);
        } else {
          console.log("[AuthContext] Token validation failed. Clearing stale auth data.");
          localStorage.removeItem('authToken');
          localStorage.removeItem('walletAddress');
          setIsAuthenticated(false);
          setAuthUserWallet(null);
        }
      } else {
        console.log("[AuthContext] No token/wallet found in localStorage.");
        setIsAuthenticated(false);
        setAuthUserWallet(null);
      }
      
      // Crucially, set loading to false *after* the initial check
      console.log("[AuthContext Initial Load] Setting isLoadingAuth to false.");
      setIsLoadingAuth(false);
      console.log("[AuthContext] Initial auth check complete. isLoadingAuth set to false.");
    }
    
    checkAndValidateAuth();
  }, []);

  // React to wallet connection changes
  useEffect(() => {
    console.log(`[AuthContext Wallet Effect] Current isLoadingAuth: ${isLoadingAuth}`);
    // Wait until initial auth check is done
    if (isLoadingAuth) {
      console.log("[AuthContext] Wallet Effect: Skipping, auth still loading.");
      return;
    }
    console.log(`[AuthContext] Wallet Effect: connected=${connected}, prevConnected=${previousConnected.current}, isAuthenticated=${isAuthenticated}, publicKey=${publicKey?.toBase58()}, authUserWallet=${authUserWallet}`);

    // CRITICAL: Explicit disconnect detection
    if (previousConnected.current && !connected) {
      console.log("[AuthContext] WALLET DISCONNECT DETECTED! Previous state was connected, now disconnected.");
      if (isAuthenticated) {
        console.log("[AuthContext] User was authenticated, logging out due to wallet disconnect.");
        handleLogout();
        return; // Exit early after handling disconnect
      }
    }

    if (connected && publicKey) {
      // Wallet is connected. Check if it matches the authenticated wallet (if any).
      console.log("[AuthContext] Wallet connected state: address=" + publicKey.toBase58());
      if (isAuthenticated && authUserWallet && publicKey.toBase58() !== authUserWallet) {
        console.warn("[AuthContext] Wallet mismatch detected! Logging out previous session.");
        handleLogout(); // Log out because the connected wallet doesn't match the stored auth
      } else if (!isAuthenticated && localStorage.getItem('authToken') && localStorage.getItem('walletAddress') !== publicKey.toBase58()) {
        console.warn("[AuthContext] Connected wallet differs from localStorage wallet. Clearing old token.");
        handleLogout();
      } else if (isAuthenticated && authUserWallet === null) {
        console.warn("[AuthContext] State inconsistency: Authenticated but authUserWallet is null. Setting wallet.");
        setAuthUserWallet(publicKey.toBase58()); // Try to recover state
      } else if (isAuthenticated && authUserWallet !== publicKey.toBase58()) {
        // Added check: If authenticated state says true, but the wallet address doesn't match the now connected one
        console.warn("[AuthContext] State inconsistency: Authenticated wallet differs from connected wallet. Logging out.");
        handleLogout();
      }
    } else {
      // This handles cases where the component mounts with wallet already disconnected
      console.log("[AuthContext] Wallet is not connected.");
      if (isAuthenticated) {
        // This is a safety check to ensure we don't have authenticated state without wallet connection
        console.log("[AuthContext] Still authenticated without wallet connection. This may be a state inconsistency.");
        if (previousConnected.current) {
          console.log("[AuthContext] Wallet was previously connected. Logging out.");
          handleLogout();
        }
      }
    }

    // Update previous connected state *after* processing the current state
    previousConnected.current = connected;

  // Add handleLogout to dependencies
  }, [connected, publicKey, isAuthenticated, authUserWallet, isLoadingAuth, handleLogout]);

  return (
    <AuthContext.Provider 
      value={{
        isAuthenticated,
        setIsAuthenticated,
        authUserWallet,
        setAuthUserWallet,
        isLoadingAuth,
        handleLogout
      }}
    >
      {children}
    </AuthContext.Provider>
  )
} 