# __init__.py

from fastapi import FastAPI, WebSocket, Request, HTTPException, Body, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from config.logging_config import setup_logging, setup_predictions_logger
import logging
import asyncio
import os  # Added import for path operations
from app.models import ModelLoader
import itertools
from solbot.websocket import web_socket_listener, process_completion_queue
from solbot.processing import process_signatures
from solbot.monitoring import log_mint_states, monitor_accumulators, run_full_system_inventory
from solbot.utilities import start_config_watcher
from solbot.batch_processor import process_realtime_batches
from solbot.config import api_key_manager
from app.states import MintState  # Ensure MintState is defined in app.states
from app.lock_manager import MintAddressLockManager
import aiohttp  # Import aiohttp for HTTP requests
import time  # Import time for performance measurement
import uuid   # Import uuid for nonce generation
from typing import Dict, Optional, Any, List
from contextlib import asynccontextmanager
import fakeredis
from datetime import datetime, timedelta
from sqlalchemy import select
from app.db_models import BotState
from app.schemas.bot_schemas import BotStatusEnum
from app.database import async_session_factory
import uuid  # Add this import for generating server version ID

# Define a custom exception for rate limiting
from fastapi import HTTPException, status

# Custom exception for rate limiting
class RateLimitExceeded(HTTPException):
    def __init__(self, detail: str = "Rate limit exceeded"):
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)

# Simple in-memory rate limiter to use as fallback
class InMemoryRateLimiter:
    def __init__(self):
        self.requests = {}
        self._lock = asyncio.Lock()
    
    async def init(self):
        # No initialization needed
        pass
    
    async def is_rate_limited(self, key: str, limit: int, window: int) -> bool:
        async with self._lock:
            now = datetime.now()
            if key not in self.requests:
                self.requests[key] = []
            
            # Remove expired timestamps
            self.requests[key] = [ts for ts in self.requests[key] if ts > now - timedelta(seconds=window)]
            
            # Check if limit exceeded
            if len(self.requests[key]) >= limit:
                return True
            
            # Add current timestamp
            self.requests[key].append(now)
            return False

# Import monitor_new_tokens from its module
from listener.new_token_listener_pumpfun import monitor_new_tokens

# Import price_monitor service
from app.price_monitor import run_price_monitor

# Import database components
from app.db_models import Base
from app.database import async_engine

# Import the bot_manager
from app.bot_manager import bot_manager

# Import periodic tasks
from app.periodic_tasks import cleanup_stale_holdings

# Import proposal resolver service
from app.proposal_resolver_service import resolve_pending_proposals_task

# Import Risk Ladder Stats service
from app.risk_ladder_stats_service import calculate_and_store_risk_ladder_stats_task

# Create database tables
async def create_db_and_tables():
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logging.getLogger(__name__).info("Database tables created successfully")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger = logging.getLogger(__name__)  # Use existing logger instead of creating new one
    logger.debug("Startup event triggered. Scheduling background tasks.")

    # Initialize aiohttp ClientSession and add to app state
    app.state.http_session = aiohttp.ClientSession()
    logger.debug("aiohttp ClientSession initialized and added to app state.")

    # Create database tables
    try:
        await create_db_and_tables()
        logger.debug("Database tables created successfully.")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")
        # Don't suppress the error - let it propagate to fail startup
        raise

    # Add bot_manager to app state
    app.state.bot_manager = bot_manager
    logger.debug("Bot manager added to app state.")
    
    # Reset all bots to STOPPED state on startup
    try:
        async with async_session_factory() as db:
            # Find all bots that are in RUNNING state
            reset_query = select(BotState).where(BotState.status == BotStatusEnum.RUNNING)
            result = await db.execute(reset_query)
            running_bots = result.scalars().all()
            
            # Reset them to STOPPED
            if running_bots:
                for bot_state in running_bots:
                    bot_state.status = BotStatusEnum.STOPPED
                    bot_state.last_changed = datetime.utcnow()
                    bot_state.session_start_time = None
                    logger.info(f"Resetting bot state to STOPPED for {bot_state.user_wallet} during server startup")
                
                await db.commit()
                logger.info(f"Reset {len(running_bots)} bots from RUNNING to STOPPED state during startup")
            else:
                logger.info("No bots in RUNNING state found during startup")
    except Exception as e:
        logger.error(f"Error resetting bot states during startup: {e}")

    # Initialize Rate Limiter
    try:
        # Create in-memory rate limiter and store in app state
        app.state.in_memory_limiter = InMemoryRateLimiter()
        await app.state.in_memory_limiter.init()
        logger.info("In-memory rate limiter initialized")
        
        # Skip FastAPILimiter initialization to avoid Redis errors
        # FastAPILimiter will be handled by our custom middleware
        
    except Exception as limiter_exc:
        logger.error(f"Failed to initialize rate limiter: {limiter_exc}", exc_info=True)

    # Start WebSocket listener
    asyncio.create_task(web_socket_listener(app))

    # Start state logger
    asyncio.create_task(log_mint_states(app))

    # Start monitor_accumulators as a background task
    asyncio.create_task(monitor_accumulators(app))
    logger.debug("monitor_accumulators task scheduled.")
    
    # Start completion queue processor
    asyncio.create_task(process_completion_queue(app))
    logger.debug("process_completion_queue task scheduled.")
    
    # Start system inventory check task
    asyncio.create_task(run_full_system_inventory(app))
    logger.debug("System inventory check task scheduled.")

    # Start realtime batch processor
    asyncio.create_task(process_realtime_batches(app))
    logger.debug("process_realtime_batches task scheduled.")

    # Get the event loop
    loop = asyncio.get_running_loop()
    
    # Get the directory containing __init__.py (which is backend/app)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Go up one level (to backend) then into config
    config_dir_path = os.path.normpath(os.path.join(current_dir, '..', 'config'))
    config_file_path = os.path.join(config_dir_path, 'config.ini')
    
    logger.info(f"Using config directory path: {config_dir_path}")
    logger.info(f"Expected config file location: {config_file_path}")

    # Start monitor_new_tokens as a background task
    asyncio.create_task(monitor_new_tokens(app))
    logger.debug("monitor_new_tokens task scheduled.")

    # Start config watcher as a background task - pass the directory not the file
    asyncio.create_task(start_config_watcher(api_key_manager, config_dir_path, loop))
    logger.debug(f"Config watcher task scheduled for directory: {config_dir_path}")
    
    # Start Price Monitor Service
    asyncio.create_task(run_price_monitor(app))
    logger.debug("Price Monitor Service task scheduled.")

    # Start periodic cleanup of stale holdings
    cleanup_task = asyncio.create_task(cleanup_stale_holdings(app))
    app.state.cleanup_task_ref = cleanup_task
    logger.info("Started periodic stale holding cleanup task.")
    
    # Start proposal resolver service
    asyncio.create_task(resolve_pending_proposals_task(app))
    logger.debug("ProposalResolverService task scheduled.")

    # Start CSV monitor service for sniper bot simulations
    from app.csv_monitor_service import start_csv_monitor_service
    asyncio.create_task(start_csv_monitor_service(app))
    logger.debug("CSVMonitorService task scheduled.")

    # Start sniper bot batch scheduler (replaces continuous resolver)
    from app.sniper_bot_scheduler import start_sniper_bot_scheduler
    asyncio.create_task(start_sniper_bot_scheduler(app))
    logger.debug("SniperBotScheduler task scheduled.")

    # Start sniper bot stats service
    from app.sniper_bot_stats_service import start_sniper_bot_stats_service
    asyncio.create_task(start_sniper_bot_stats_service(app))
    logger.debug("SniperBotStatsService task scheduled.")
    
    # Start Risk Ladder Stats service
    asyncio.create_task(calculate_and_store_risk_ladder_stats_task(app))
    logger.debug("RiskLadderStatsCalculatorService task scheduled.")

    yield  # Server is running

    # Shutdown
    logger.debug("Shutdown event triggered. Stopping all active bots...")
    
    # Stop all active bots and mark them as STOPPED in the database
    try:
        bots_stopped = await app.state.bot_manager.stop_all_bots()
        logger.info(f"Stopped {bots_stopped} bots during server shutdown")
    except Exception as e:
        logger.error(f"Error stopping bots during shutdown: {e}")
    
    # Cancel cleanup task
    if hasattr(app.state, 'cleanup_task_ref'):
        app.state.cleanup_task_ref.cancel()
        try:
            await app.state.cleanup_task_ref
        except asyncio.CancelledError:
            logger.debug("Stale holdings cleanup task cancelled.")
    
    # Note: Proposal resolver task will be cancelled automatically when the server shuts down
    
    if app.state.http_session:
        await app.state.http_session.close()
        logger.debug("aiohttp ClientSession closed.")


class CustomFastAPI(FastAPI):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize application state
        self.state.mint_addresses = []
        self.state.callback_id = None
        self.state.lock = asyncio.Lock()
        self.state.mint_lock_manager = MintAddressLockManager()  # Initialize lock manager
        self.state.processed_addresses = set()
        self.state.subscription_queue = asyncio.Queue()
        self.state.signatures_queue = asyncio.Queue()
        
        # Server version ID - unique for each server restart
        self.state.server_version_id = str(uuid.uuid4())
        
        # SIWS nonce handling
        self.state.siws_nonces: Dict[str, float] = {}  # nonce -> expiry_timestamp
        self.state.siws_nonce_lock = asyncio.Lock()
        
        # WebSocket connection management
        self.state.active_connections: Dict[str, WebSocket] = {}  # user_wallet -> WebSocket
        self.state.connections_lock = asyncio.Lock()  # Lock for accessing the dict
        
        # Add high-priority completion queue for Phase 1 implementation
        self.state.completion_queue = asyncio.PriorityQueue()  # Priority queue for completion tasks
        
        self.state.accumulators = {}
        self.state.mint_states = {}  # Track state of each mint address
        self.state.mint_states_lock = asyncio.Lock()  # Ensure thread-safe access
        self.state.jsonrpc_id_counter = itertools.count(1)  # Unique JSON-RPC IDs

        # Price tracking for direct detection
        self.state.price_counters = {}  # Track price counts per mint address in websocket handler
        self.state.price_counters_lock = asyncio.Lock()  # Lock for thread-safe access to price counters
        self.state.detection_times = {}  # Track when 100th price was detected for metrics
        
        # Track when addresses became active
        self.state.mint_activation_times = {}  # Track when mint addresses become active

        # Additional state variables
        self.state.unsubscribe_queue = asyncio.Queue()
        self.state.request_id_to_mint_address = {}
        self.state.subscription_id_to_mint_address = {}
        self.state.subscription_ids = {}
        self.state.subscribed_addresses = set()
        # Locks for thread-safe access
        self.state.subscriptions_lock = asyncio.Lock()
        self.state.accumulators_lock = asyncio.Lock()  # Lock for thread-safe access to accumulators
        self.state.jsonrpc_id_counter_lock = asyncio.Lock()
        self.state.request_id_to_type = {}
        self.state.unsubscription_events = {}

        # Add these three dictionaries to store data per mint for explicit task management:
        self.state.mint_heaps = {}
        self.state.mint_tasks = {}
        self.state.pending_signatures = {}

        # Realtime signatures handling
        self.state.pending_realtime_signatures = {}  # Store lists of signatures keyed by mint_address
        self.state.pending_realtime_signatures_lock = asyncio.Lock()  # Lock for thread-safe access

        # Performance measurement variables
        self.state.concurrency_semaphore = asyncio.Semaphore(20)  # Adjust as needed
        self.state.total_mint_addresses_processed = 0
        self.state.counter_lock = asyncio.Lock()
        self.state.last_checkpoint_time = time.monotonic()

        # Add an aiohttp session to the state
        self.state.http_session = None  # Will be initialized on startup

        self.state.last_processed_index = 0

        # Log the initialization
        logging.getLogger(__name__).debug(
            "CustomFastAPI instance created with all state variables initialized."
        )
        logging.getLogger(__name__).debug(
            "Initialized pending_realtime_signatures dictionary and lock."
        )


def create_app():
    logger = setup_logging()
    logger.debug("Starting application setup.")
    app = CustomFastAPI(lifespan=lifespan)
    logger.debug("CustomFastAPI instance initialized.")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000"],  # Frontend URL
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    logger.debug("CORS middleware configured.")

    # Initialize XGBoost model
    ModelLoader.load_model_and_artifacts() 
    logger.debug("XGBoost model and artifacts loading initiated.")

    from app.routes import configure_routes

    configure_routes(app)
    logger.debug("Routes configured.")

    # Set up predictions logger
    setup_predictions_logger()

    return app
