'use client'

import React, { ReactNode, Suspense } from 'react'
import { ErrorBoundary } from './error-boundary'
import ClientProviders from './client-providers'

export default function DynamicClientProviders({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin-slow inline-block w-10 h-10 border-4 border-primary border-t-transparent rounded-full mb-4"></div>
            <p className="text-muted-foreground">Loading application...</p>
          </div>
        </div>
      }>
        <ClientProviders>
          {children}
        </ClientProviders>
      </Suspense>
    </ErrorBoundary>
  )
} 