import logging
import re
import time
import datetime
import base64
import async<PERSON>
import os
from typing import Op<PERSON>
from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from solbot.config import JWT_SECRET_KEY, JWT_ALGORITHM, JWT_ACCESS_TOKEN_EXPIRE_MINUTES

from solders.pubkey import Pubkey
from solders.signature import Signature
from nacl.signing import Verify<PERSON><PERSON>
from nacl.exceptions import BadSignatureError

# Setup logger
logger = logging.getLogger(__name__)

# JWT Settings - Import from centralized config
SECRET_KEY = JWT_SECRET_KEY
ALGORITHM = JWT_ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = JWT_ACCESS_TOKEN_EXPIRE_MINUTES

# Verify SECRET_KEY is set
if not SECRET_KEY:
    logger.critical("FATAL ERROR: JWT_SECRET_KEY not set in config.ini!")
    raise ValueError("JWT_SECRET_KEY must be set for application security.")

def create_access_token(subject: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a new JWT access token for the given wallet address.
    
    Args:
        subject: The wallet address to create a token for (usually wallet_address)
        expires_delta: Optional expiry time delta, defaults to ACCESS_TOKEN_EXPIRE_MINUTES
        
    Returns:
        JWT access token as string
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def parse_siws_message(message: str) -> dict | None:
    # Simplified regex pattern to reliably extract all fields
    pattern = re.compile(
        r"^(?P<domain>[^\s]+) wants you to sign in with your Solana account:\n"
        r"(?P<address>[a-zA-Z0-9]{32,44})\n\n"
        r"(?P<statement>[^\n]+)[.]\n\n"
        r"URI: (?P<uri>[^\n]+)\n"
        r"Version: (?P<version>[^\n]+)\n"
        r"Chain ID: (?P<chain_id>[^\n]+)\n"
        r"Nonce: (?P<nonce>[^\n]+)\n"
        r"Issued At: (?P<issued_at>[^\n]+)(?:\n"
        r"(?:Expiration Time: (?P<expiration_time>[^\n]+)\n)?)?"
        r"(?:(?:Not Before: (?P<not_before>[^\n]+)\n)?)?"
        r"(?:(?:Request ID: (?P<request_id>[^\n]+)\n)?)?"
        r"(?:Resources:\n(?P<resources>(?:- [^\n]+\n?)+))?$", 
        re.MULTILINE
    )
    
    logger.info(f"[SIWS] Attempting to parse message: {message}")
    match = pattern.match(message)
    if not match:
        logger.warning("[SIWS] Message did not match expected pattern.")
        # Try a fallback approach by manually parsing key-value pairs
        fallback_data = {}
        
        # Extract domain and address from first two lines
        lines = message.split('\n')
        if len(lines) >= 2:
            domain_match = re.match(r"^([^\s]+) wants you to sign in with your Solana account:", lines[0])
            if domain_match:
                fallback_data['domain'] = domain_match.group(1)
            
            address_match = re.match(r"^([a-zA-Z0-9]{32,44})$", lines[1])
            if address_match:
                fallback_data['address'] = address_match.group(1)
        
        # Look for statement on line 3
        if len(lines) >= 4 and lines[2] == "":  # Empty line after address
            fallback_data['statement'] = lines[3].rstrip('.')
        
        # Extract other fields by looking for key: value patterns
        for line in lines:
            for key in ['URI', 'Version', 'Chain ID', 'Nonce', 'Issued At', 'Expiration Time', 'Not Before', 'Request ID']:
                if line.startswith(f"{key}: "):
                    # Convert keys to lowercase with underscores for consistency with regex approach
                    dict_key = key.lower().replace(' ', '_')
                    fallback_data[dict_key] = line[len(key)+2:]  # +2 for ": "
        
        if 'domain' in fallback_data and 'address' in fallback_data:
            logger.info(f"[SIWS] Used fallback parsing, extracted data: {fallback_data}")
            return fallback_data
        return None
    
    data = match.groupdict()
    # Further process resources if needed
    if data.get('resources'):
        data['resources'] = [res.strip()[2:] for res in data['resources'].strip().split('\n')]
    
    logger.info(f"[SIWS] Successfully parsed message using regex pattern")
    return data

async def verify_siws_message(
    message: str,
    signature_b64: str,
    expected_address: str,
    app_state  # Pass app state containing nonce store and lock
    ) -> bool:
    """Verifies a SIWS message and signature."""
    try:
        # Log input parameters
        logger.info(f"[SIWS] ==== SIWS Verification Started ====")
        logger.info(f"[SIWS] Input parameters:")
        logger.info(f"[SIWS] message: {message}")
        logger.info(f"[SIWS] signature_b64: {signature_b64}")
        logger.info(f"[SIWS] expected_address: {expected_address}")
        
        # 1. Parse the message
        parsed_data = parse_siws_message(message)
        if not parsed_data:
            logger.error("[SIWS] Message parsing failed - could not match expected pattern")
            return False
        logger.info(f"[SIWS] Parsed SIWS data: {parsed_data}")

        # 2. Check Domain, Address, URI, ChainID etc. against expectations
        # (Add checks based on your security requirements, e.g., check domain/URI)
        if parsed_data.get('address') != expected_address:
            logger.warning(f"[SIWS] Verification failed: Address mismatch. Expected: {expected_address}, Got: {parsed_data.get('address')}")
            return False
        
        logger.info(f"[SIWS] Address check passed: {expected_address}")

        # 3. Check Nonce (Existence and Expiry) - Use lock
        nonce = parsed_data.get('nonce')
        if not nonce:
             logger.warning("[SIWS] Verification failed: Nonce missing in message.")
             return False
        
        logger.info(f"[SIWS] Nonce found in message: {nonce}")

        nonce_valid = False
        async with app_state.siws_nonce_lock:
            logger.info(f"[SIWS] Current nonces in store: {app_state.siws_nonces}")
            nonce_expiry = app_state.siws_nonces.get(nonce)
            
            if nonce_expiry is not None:
                logger.info(f"[SIWS] Nonce '{nonce}' found in store with expiry: {nonce_expiry}")
                current_time = time.time()
                logger.info(f"[SIWS] Current time: {current_time}")
                
                if current_time <= nonce_expiry:
                    # Nonce is valid and not expired, consume it
                    del app_state.siws_nonces[nonce]
                    nonce_valid = True
                    logger.info(f"[SIWS] Nonce '{nonce}' validated and consumed. Remaining nonces: {app_state.siws_nonces}")
                else:
                    logger.warning(f"[SIWS] Verification failed: Nonce '{nonce}' expired. Expiry: {nonce_expiry}, Current: {current_time}")
                    # Optionally remove expired nonce here too
                    app_state.siws_nonces.pop(nonce, None)
                    logger.info(f"[SIWS] Expired nonce removed. Remaining nonces: {app_state.siws_nonces}")
            else:
                logger.warning(f"[SIWS] Verification failed: Nonce '{nonce}' not found or already used.")
                logger.info(f"[SIWS] Available nonces: {list(app_state.siws_nonces.keys())}")

        if not nonce_valid:
            logger.error("[SIWS] Nonce validation failed - returning False")
            return False

        # 4. Check Timestamps (issuedAt, expirationTime, notBefore)
        issued_at_str = parsed_data.get('issued_at')
        if issued_at_str:
            try:
                # Remove 'Z' suffix if present and parse
                if issued_at_str.endswith('Z'):
                    issued_at_str = issued_at_str[:-1]
                
                issued_at = datetime.fromisoformat(issued_at_str)
                current_time = datetime.utcnow()
                
                # Allow a 10-minute tolerance in either direction
                tolerance = timedelta(minutes=10)
                min_time = current_time - tolerance
                max_time = current_time + tolerance
                
                logger.info(f"[SIWS] Timestamp check: issuedAt={issued_at}, current={current_time}")
                logger.info(f"[SIWS] Valid range: {min_time} to {max_time}")
                
                if issued_at < min_time or issued_at > max_time:
                    logger.warning(f"[SIWS] Verification failed: issuedAt timestamp {issued_at} outside acceptable range")
                    return False
                
                logger.info("[SIWS] Timestamp check passed")
            except ValueError as e:
                logger.warning(f"[SIWS] Verification failed: Invalid issuedAt format: {issued_at_str}. Error: {e}")
                return False
        else:
            logger.warning("[SIWS] No issuedAt timestamp found in message - skipping timestamp verification")

        # 5. Verify Signature
        try:
            # Create pubkey from the wallet address
            pubkey = Pubkey.from_string(expected_address)
            logger.info(f"[SIWS] Created Pubkey object: {pubkey}")
            
            # Decode the base64 signature
            signature_bytes = base64.b64decode(signature_b64)
            logger.info(f"[SIWS] Decoded signature bytes length: {len(signature_bytes)}")
            
            # Ensure only the 64-byte signature is used
            if len(signature_bytes) != 64:
                logger.error(f"[SIWS] Invalid signature length: {len(signature_bytes)}")
                return False
                
            # Create signature object
            signature_obj = Signature(signature_bytes)
            logger.info(f"[SIWS] Created Signature object: {signature_obj}")

            # Create verify key from pubkey
            verify_key = VerifyKey(bytes(pubkey))
            logger.info(f"[SIWS] Created VerifyKey object: {verify_key}")
            
            # Get message bytes
            signed_message_bytes = message.encode('utf-8')
            logger.info(f"[SIWS] Message bytes length: {len(signed_message_bytes)}")
            logger.info(f"[SIWS] First 50 bytes of message: {signed_message_bytes[:50]}")
            
            try:
                # Verify the signature directly with nacl's verify method
                verify_key.verify(signed_message_bytes, signature_bytes)
                logger.info(f"[SIWS] Signature verified successfully for {expected_address}")
                logger.info(f"[SIWS] ==== SIWS Verification Completed Successfully ====")
                return True
            except BadSignatureError as sig_err:
                logger.error(f"[SIWS] Signature validation failed specifically: {sig_err}")
                return False
                
        except Exception as e:
            logger.error(f"[SIWS] Error during signature verification: {type(e).__name__}: {e}")
            return False

    except BadSignatureError:
        logger.warning(f"[SIWS] Signature verification failed (BadSignatureError) for {expected_address}")
        return False
    except ImportError:
         logger.error("[SIWS] PyNaCl not installed. Cannot verify SIWS signature.")
         return False
    except ValueError as e:  # E.g., invalid base64 or pubkey string
        logger.warning(f"[SIWS] Value error during verification: {e}")
        return False
    except Exception as e:
        logger.exception(f"[SIWS] Unexpected error during verification: {e}")
        return False 