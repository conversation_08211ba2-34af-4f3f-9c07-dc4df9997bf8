import React from "react"
import { AlertTriangle, Loader2 } from "lucide-react"

interface ServerDownModalProps {
  isOpen: boolean
  connectionAttempts: number
  maxAttempts?: number
  isCheckingServer?: boolean
}

const ServerDownModal: React.FC<ServerDownModalProps> = ({
  isOpen,
  connectionAttempts,
  maxAttempts = 5, // Default value, but not really used for limiting reconnections
  isCheckingServer = false
}) => {
  if (!isOpen) return null;
  
  // Determine status text based on checking status
  const getStatusText = () => {
    if (isCheckingServer) {
      return "Checking if server is back online...";
    }
    
    return "Reconnecting...";
  };
  
  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="glass border-[0.5px] border-white/10 rounded-xl shadow-glass w-full max-w-md p-6 m-4">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="p-3 bg-yellow-500/20 text-yellow-500 rounded-full">
            <AlertTriangle className="h-8 w-8" />
          </div>
          
          <h2 className="text-xl font-semibold">Server Connection Lost</h2>
          
          <p className="text-white/60">
            The bot server is currently unreachable. Automatic reconnection is in progress. 
            This could be due to scheduled maintenance or temporary server issues.
          </p>
          
          <div className="flex items-center justify-center w-full gap-2 pt-2">
            <Loader2 className="h-5 w-5 animate-spin text-accent" />
            <span className="text-accent">
              {getStatusText()}
            </span>
          </div>
          
          <p className="text-white/40 text-sm">
            The application will automatically resume once the connection is restored.
          </p>
        </div>
      </div>
    </div>
  )
}

export default ServerDownModal 