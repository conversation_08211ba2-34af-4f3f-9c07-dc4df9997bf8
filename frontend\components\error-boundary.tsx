'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null
    }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by ErrorBoundary:', error)
    console.error('Component stack:', errorInfo.componentStack)
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return this.props.fallback || (
        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive">
          <h2 className="text-lg font-bold mb-2">Something went wrong</h2>
          <details className="text-sm">
            <summary>Error details</summary>
            <pre className="mt-2 p-2 bg-black/20 rounded overflow-x-auto">
              {this.state.error?.message || 'Unknown error'}
            </pre>
          </details>
          <button 
            onClick={() => {
              // Clear error and retry
              this.setState({ hasError: false, error: null })
              // Force reload the page to clear any bad state
              window.location.reload()
            }}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm"
          >
            Retry/Reload
          </button>
        </div>
      )
    }

    return this.props.children
  }
} 