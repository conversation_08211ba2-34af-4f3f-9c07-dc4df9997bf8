// frontend/types/api.ts

// Based on backend BotStatusEnum (can be imported if shared, or redefined)
export enum BotStatusEnum {
    STOPPED = 'Stopped',
    RUNNING = 'Running',
    ERROR = 'Error'
}

// Based on backend BotStateResponse
export interface BotState { // Renamed slightly for clarity on frontend
    user_wallet: string;
    status: BotStatusEnum;
    last_changed: string; // Use string for date initially, can parse later
    // Session stats
    buy_count?: number | null;
    sell_count?: number | null;
    sol_invested?: number | null;
    sol_received?: number | null;
    pnl_sol?: number | null;
    pnl_percent?: number | null;
}

// Based on backend HoldingItem (app/schemas/dashboard_schemas.py)
export interface HoldingItem {
    token_mint: string;
    amount_held: number;
    average_buy_price_sol: number;
    current_price_sol: number | null; // Matches backend nullable=True
    live_pnl_percent: number | null; // Calculated on frontend, but good to type
    last_acquired_at: string; // Use string for date
    updated_at: string; // Use string for date
    monitoring_active: boolean; // Flag indicating if price monitoring is active
    stagnant_check_count: number; // Counter for how many checks showed no price change
    // Note: 'id' might be useful if you need a key for list rendering
    id?: number; // Optional ID if backend model includes it
}

// Based on backend TradeItem (app/schemas/trade_schemas.py)
export interface TradeItem {
    id: number;
    timestamp: string; // Use string for date
    trade_type: 'BUY' | 'SELL'; // Literal types matching the Enum values
    token_mint: string;
    amount_token: number;
    price_sol: number;
    total_sol: number;
    pnl_sol: number | null;
    tx_signature: string | null;
}

// Based on backend DashboardDataResponse
export interface DashboardDataResponse {
    bot_state: BotState;
    holdings: HoldingItem[];
    recent_trades: TradeItem[];
}

// Based on backend ConfigurationResponse
export interface ConfigurationResponse {
    max_buy_sol: number | null;
    tp_percent: number | null;
    sl_percent: number | null;
    min_unique_wallets: number | null;
    min_total_volume: number | null;
    max_holder1_percent: number | null;
    buy_slippage_bps: number | null;
    sell_slippage_bps: number | null;
    priority_fee_microlamports: number | null;
    user_wallet: string;
    updated_at: string; // Use string for date
}

// Transaction proposal data structure from bot
export interface TransactionProposalData {
    type: 'buy' | 'sell'; // Type of proposal
    user_wallet: string; // Wallet this proposal is for
    mint_address: string;
    sol_amount?: number; // For BUY proposals (amount of SOL to spend)
    token_amount?: number; // For SELL proposals (amount of tokens to sell)
    slippage_bps?: number; // Slippage tolerance in basis points (e.g., 1500 for 1.5%)
    reason?: string; // Optional reason (e.g., 'TP', 'SL', 'BOT_SIGNAL')
}

// WebSocket message wrapper
export interface WebSocketMessage {
    message_type?: string; // e.g., 'transaction_proposal', 'status_update', etc.
    type?: string;         // Alternative format for message type (e.g., 'server_info')
    data?: any;           // Data payload, structure depends on message_type
    server_version_id?: string; // For server_info messages
    message?: string;     // Optional message text
}

// FastAPI validation error item
export interface ValidationErrorItem {
    loc: (string | number)[];
    msg: string;
    type: string;
}

// FastAPI error response with detail field
export interface FastAPIErrorResponse {
    detail: ValidationErrorItem[];
}

// Custom error response with message
export interface ErrorWithMessage {
    message: string;
}

// Generic API response wrapper
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string | ValidationErrorItem[] | FastAPIErrorResponse | ErrorWithMessage | unknown;
} 