import React from 'react';
import "./globals.css";
import { Figtree } from 'next/font/google';
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from 'sonner';

const figtree = Figtree({
  subsets: ['latin'],
  variable: '--font-figtree',
  display: 'swap',
  fallback: ['system-ui', 'sans-serif'],
});

// This is the Root Layout for the marketing/landing pages
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${figtree.variable} dark`} style={{"colorScheme": "dark"}}>
      <body>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} disableTransitionOnChange forcedTheme="dark">
          {children}
          <Toaster position="top-right" richColors closeButton />
        </ThemeProvider>
      </body>
    </html>
  );
}
