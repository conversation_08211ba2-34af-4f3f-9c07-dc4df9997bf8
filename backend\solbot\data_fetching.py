import json
import asyncio
import logging
import os
from aiohttp import ClientTimeout
from .config import api_key_manager, HOLDERS_LIMIT, SHYFT_API_BASE_URL, SHYFT_RPC_HISTORY_ENDPOINT_URL
from .utilities import APIKeyManager
import contextlib
import aiohttp
import time

logger = logging.getLogger(__name__)

class RateLimitException(Exception):
    pass

class CreditExhaustedException(Exception):
    pass

async def fetch_transaction_detail(app, signature):
    url = f"{SHYFT_API_BASE_URL}/transaction/parse_selected"
    max_retries = 5 #len(api_keys) * 2
    initial_retry_delay = 1.2
    max_retry_delay = 1.2
    empty_result_retries = 10
    empty_result_delay = 2.0

    for attempt in range(max_retries):
        last_failure_reason = "Unknown" # Initialize at the start of the loop
        current_retry_delay = min(initial_retry_delay * (1.5 ** attempt), max_retry_delay)
        try:
            api_key, limiter = await api_key_manager.get_api_key_and_limiter()
            headers = {'Content-Type': 'application/json', 'x-api-key': api_key.strip()}
            payload = json.dumps({
                "network": "mainnet-beta",
                "transaction_signatures": [signature],
                "enable_raw": True,
                "enable_events": False
            })
            async with limiter:
                timeout = ClientTimeout(total=15)
                async with app.state.http_session.post(url, headers=headers, data=payload, timeout=timeout) as response:
                    text = await response.text()
                    logger.debug(f"API Key: {api_key}, Response Status: {response.status}, Response Body: {text}")
                    try:
                        response_json = json.loads(text)
                    except json.JSONDecodeError:
                        response_json = {}

                    if response.status == 200:
                        data = response_json
                        result = data.get('result', [])
                        if not result:
                            logger.debug(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Empty result for {signature} with {api_key}. Retrying fetch after {empty_result_delay}s (retries left: {empty_result_retries}).")
                            last_failure_reason = "Empty result from API" # Set the reason
                            if empty_result_retries > 0:
                                empty_result_retries -= 1
                                logger.debug(f"Waiting {empty_result_delay}s for transaction {signature}. Retries left: {empty_result_retries}")
                                await asyncio.sleep(empty_result_delay)
                                continue
                            last_failure_reason = "Persistent empty result" # Update reason
                            return None

                        tx = result[0]
                        if tx:
                            logger.debug(f"Full transaction data for {signature}: {json.dumps(tx, indent=2)}")
                            slot = tx.get('slot')
                            block_time = tx.get('blockTime')
                            if slot is None:
                                raw_data = tx.get('raw', {})
                                slot = raw_data.get('slot')
                                if slot is not None:
                                    try:
                                        slot = int(slot)
                                    except ValueError:
                                        logger.warning(f"Invalid 'slot' value: {slot}. Skipping transaction.")
                                        return None
                                else:
                                    logger.warning(f"Transaction {signature} missing 'slot' and 'raw.slot'.")
                                    return None
                            else:
                                try:
                                    slot = int(slot)
                                except ValueError:
                                    logger.warning(f"Invalid 'slot' value: {slot}. Skipping transaction.")
                                    return None

                            if block_time is None:
                                raw_data = tx.get('raw', {})
                                block_time = raw_data.get('blockTime')
                                if block_time is not None:
                                    try:
                                        block_time = int(block_time)
                                    except ValueError:
                                        logger.warning(f"Invalid 'blockTime' value: {block_time}. Using timestamp.")
                                        block_time = tx.get('timestamp', 0)
                                else:
                                    logger.warning(f"Transaction {signature} missing 'blockTime' and 'raw.blockTime'.")
                                    block_time = tx.get('timestamp', 0)
                            else:
                                try:
                                    block_time = int(block_time)
                                except ValueError:
                                    logger.warning(f"Invalid 'blockTime' value: {block_time}. Using timestamp.")
                                    block_time = tx.get('timestamp', 0)

                            transaction = {
                                'signature': signature,
                                'timestamp': tx.get('timestamp'),
                                'type': tx.get('type'),
                                'status': tx.get('status'),
                                'actions': tx.get('actions', []),
                                'sender': tx.get('sender'),
                                'recipient': tx.get('recipient'),
                                'fee': tx.get('fee'),
                                'slot': slot,
                                'blockTime': block_time,
                                'raw_data': tx.get('raw')
                            }

                            if not transaction['actions']:
                                logger.warning(f"Transaction {signature} has no actions.")
                                if empty_result_retries > 0:
                                    empty_result_retries -= 1
                                    logger.info(f"Waiting {empty_result_delay}s for actions. Retries left: {empty_result_retries}")
                                    await asyncio.sleep(empty_result_delay)
                                    continue
                                return None

                            logger.debug(f"Extracted Slot: {transaction['slot']}, blockTime: {transaction['blockTime']}")
                            return transaction

                    # --- Start Enhanced Credit Exhaustion Check ---
                    is_exhausted = False
                    exhaustion_trigger = "None"

                    # Check 1: Specific JSON error code (Prioritize this if Shyft documents specific codes)
                    # Example: Assume 'CREDIT_LIMIT_REACHED' is the specific code from Shyft docs
                    known_exhaustion_code = "CREDIT_LIMIT_REACHED" # Replace with actual code if known
                    if 'error_code' in response_json:
                        error_code = response_json.get('error_code', '').lower()
                        if any(indicator in error_code for indicator in ["credits_exhausted", "quota_exceeded"]):
                            is_exhausted = True
                            exhaustion_trigger = f"JSON error_code: {response_json.get('error_code')}"

                    # Check 2: More specific text indicators (Only if Check 1 didn't trigger)
                    # Be more careful with broad terms like "quota exceeded" which might apply to rate limits.
                    # Focus on terms explicitly implying billing/credits.
                    if not is_exhausted:
                        specific_text_indicators = [
                            "credits exhausted",
                            "credits depleted",
                            "insufficient credits",
                            "credit limit reached",
                            "quota exceeded",
                            "upgrade your plan",
                            "upgrade plan to continue",
                            "please upgrade plan",
                            "api limit exceeded"
                        ]
                        # Check for *exact phrases* or very specific contexts if possible
                        response_lower = text.lower()
                        for indicator in specific_text_indicators:
                            if indicator in response_lower: # Consider using more specific matching if needed
                                is_exhausted = True
                                exhaustion_trigger = f"Text indicator: '{indicator}'"
                                break # Stop checking once found

                    # If exhaustion was detected by either method:
                    if is_exhausted:
                        # Log the actual response that triggered the exhaustion flag
                        logger.info(f"[FETCH_DETAIL_EXHAUSTION_RESPONSE] Exhaustion suspected for {api_key}. Trigger: {exhaustion_trigger}. Raw Response JSON: {response_json}. Raw Response Text: {text}")
                        
                        logger.warning(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Credit exhaustion detected for {api_key}. Trigger: {exhaustion_trigger}. Marking unavailable with extended cooldown.")
                        last_failure_reason = f"Credit exhaustion detected ({exhaustion_trigger}) for key {api_key}" # Set the reason
                        await api_key_manager.mark_api_key_unavailable(api_key, is_credit_exhausted=True)
                        raise CreditExhaustedException(f"API key {api_key} credits exhausted based on trigger: {exhaustion_trigger}")
                    # --- End Enhanced Credit Exhaustion Check ---
                    elif response.status == 429:
                        # Try to get retry-after header, default to current_retry_delay if not present
                        retry_after = float(response.headers.get('retry-after', current_retry_delay))
                        logger.warning(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Rate limit (429) hit for {signature} with {api_key}. Marking key unavailable and retrying after {retry_after}s...")
                        last_failure_reason = f"Rate limit (429) with key {api_key}" # Set the reason
                        # Ensure the key is marked unavailable *before* sleeping
                        await api_key_manager.mark_api_key_unavailable(api_key)
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        logger.error(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Bad HTTP response fetching {signature} with {api_key}. Status: {response.status}. Response: {text}. Retrying after {current_retry_delay:.1f}s...")
                        last_failure_reason = f"HTTP Status {response.status}" # Set the reason
                        raise Exception(f"Bad response with status {response.status}: {text}")

        except asyncio.TimeoutError:
            logger.warning(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Timeout fetching {signature} with {api_key}. Retrying after {current_retry_delay:.1f}s...")
            last_failure_reason = "Timeout" # Set the reason
            await asyncio.sleep(current_retry_delay)
        except (RateLimitException, CreditExhaustedException) as e:
            log_reason = "Rate Limit" if isinstance(e, RateLimitException) else "Credit Exhaustion"
            logger.warning(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Caught {log_reason} for {api_key} processing {signature}.")
            if isinstance(e, CreditExhaustedException):
                 logger.warning(f"--> Triggered by: {e}") # Log the trigger message from the exception
                 logger.warning(f"--> Breaking attempt loop for {api_key} due to exhaustion.")
                 last_failure_reason = f"Credit exhaustion with key {api_key}" # Set the reason
                 break # Break the inner loop to stop retrying with this exhausted key
            else:
                 # It's a RateLimitException, wait before retrying (potentially with the same key if cooldown expires)
                 logger.warning(f"--> Retrying after {current_retry_delay:.1f}s...")
                 last_failure_reason = f"Rate limit with key {api_key}" # Set the reason
                 await asyncio.sleep(current_retry_delay)
        except Exception as e:
            if isinstance(e, aiohttp.ClientError):
                logger.error(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Network/Client error fetching {signature} with {api_key}. Error: {type(e).__name__}: {e}. Retrying after {current_retry_delay:.1f}s...")
                last_failure_reason = f"Network/Client error: {type(e).__name__}" # Set the reason
            else:
                logger.error(f"[FETCH_DETAIL_RETRY][{attempt+1}/{max_retries}] Unknown error fetching {signature} with {api_key}. Error: {type(e).__name__}: {e}. Retrying after {current_retry_delay:.1f}s...", exc_info=True)
                last_failure_reason = f"Unknown error: {type(e).__name__}" # Set the reason
            await asyncio.sleep(current_retry_delay)

    # If the loop finished without returning a transaction, log failure and return None
    logger.error(f"[FETCH_DETAIL_FAILURE] Failed to fetch details for signature {signature} after {max_retries} attempts. Last failure reason: '{last_failure_reason}'. Skipping this signature.")
    return None

async def fetch_holders(mint_address):
    from app.holders import fetch_holders_data
    try:
        holders_data = await fetch_holders_data(mint_address)
        
        # The holders_data from app.holders is already a flat list of values:
        # [token_balance_1, perc_total_supply_1, sol_balance_1, ...]
        # If it's empty or None, create a list of zeros
        if not holders_data:
            return [0] * (HOLDERS_LIMIT * 3)
            
        # Make sure it's the right length
        if len(holders_data) < HOLDERS_LIMIT * 3:
            # Pad with zeros if needed
            holders_data.extend([0] * (HOLDERS_LIMIT * 3 - len(holders_data)))
        
        return holders_data
    except Exception as e:
        logger.error(f"Error fetching holders for {mint_address}: {e}")
        # Return default zeros if anything fails
        return [0] * (HOLDERS_LIMIT * 3)

async def fetch_signatures_for_address(app, mint_address, limit=1000, until_signature=None, get_oldest=True):
    """Fetch transaction signatures for an address.
    
    Important notes about Solana pagination parameters:
    - until_signature: Returns signatures up to and including this one
    - When get_oldest=True, we'll use before_signature pagination to get to the very first transactions
    
    By default, returns newest signatures first (descending by time).
    
    Args:
        app: The FastAPI app instance
        mint_address: Address to fetch signatures for
        limit: Maximum number of signatures to fetch per batch
        until_signature: Optional, returns signatures until (and including) this one
        get_oldest: If True, continuously paginate to get to the oldest signatures
        
    Returns:
        List of transaction signatures (strings)
    """
    logger.debug(f"Fetching signatures for {mint_address} using RPC method")
    
    max_retries = 10
    initial_retry_delay = 1.2
    max_retry_delay = 5.0
    all_signatures = []
    
    # ZERO TOLERANCE: Allow larger limits by using pagination to fetch ALL signatures
    # We'll handle the 1000 limit per batch internally through pagination
    max_per_batch = 1000  # RPC API limit per individual request
    
    # Use existing session from app if available, otherwise create a new one
    session = getattr(app.state, 'http_session', None) if app else None
    if not session:
        timeout = ClientTimeout(total=5)
        session = app.state.http_session if app and hasattr(app.state, 'http_session') else aiohttp.ClientSession(
            headers={"Content-Type": "application/json"}, 
            timeout=timeout
        )
    
    # For initial batch fetching, we'll use either until_signature or no pagination parameter
    current_pagination_param = {"until": until_signature} if until_signature else {}
    
    # When get_oldest=True, we'll use before_signature for pagination to get older transactions
    # When get_oldest=False, we'll just get the current batch without pagination
    pagination_method = "before" if get_oldest else None
    have_more_signatures = True
    
    # Keep track of unique signatures to avoid duplicates
    processed_signatures = set()
    
    while have_more_signatures:
        for attempt in range(max_retries):
            current_retry_delay = min(initial_retry_delay * (1.5 ** attempt), max_retry_delay)
            try:
                # Get a limiter from the API key manager if available
                limiter = None
                if api_key_manager:
                    _, limiter = await api_key_manager.get_api_key_and_limiter()
                
                # Create RPC request payload with proper pagination params
                params = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getSignaturesForAddress",
                    "params": [
                        mint_address,
                        {
                            "limit": limit,
                            "commitment": "confirmed",  # Use finalized commitment for guaranteed finality
                            **current_pagination_param
                        }
                    ]
                }
                
                # Log the request parameters for debugging
                logger.debug(f"Fetching signatures with params: {params['params'][1]}")
                
                # Execute the request with a limiter if available
                async_context = limiter if limiter else contextlib.nullcontext()
                async with async_context:
                    async with session.post(SHYFT_RPC_HISTORY_ENDPOINT_URL, json=params) as response:
                        text = await response.text()
                        
                        # Handle rate limiting
                        if response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', '5'))
                            logger.warning(f"Rate limited. Waiting {retry_after}s before retry.")
                            await asyncio.sleep(retry_after)
                            continue
                        
                        try:
                            response_json = json.loads(text)
                        except json.JSONDecodeError:
                            logger.error(f"JSON decode error: {text}")
                            await asyncio.sleep(current_retry_delay)
                            continue
                        
                        # Check for errors in the RPC response
                        if "error" in response_json:
                            error_msg = response_json.get("error", {})
                            if isinstance(error_msg, dict):
                                error_msg = error_msg.get("message", str(error_msg))
                            
                            # Handle rate limiting in error message
                            if "try again later" in str(error_msg).lower():
                                logger.warning(f"Rate limit hit: '{error_msg}'. Retrying in {current_retry_delay} seconds...")
                                await asyncio.sleep(current_retry_delay)
                                continue
                            else:
                                logger.error(f"RPC API error: {error_msg}")
                                await asyncio.sleep(current_retry_delay)
                                continue
                        
                        # Process successful response
                        if "result" in response_json:
                            signatures = [tx["signature"] for tx in response_json["result"]]
                            
                            # If we got no signatures, we're done
                            if not signatures:
                                logger.warning(f"No signatures found for {mint_address} in this batch")
                                have_more_signatures = False
                                break
                            
                            # Filter out any signatures we've already processed to avoid duplicates
                            new_signatures = [sig for sig in signatures if sig not in processed_signatures]
                            processed_signatures.update(new_signatures)
                            
                            # Add new signatures to our result list
                            all_signatures.extend(new_signatures)
                            logger.debug(f"Found {len(new_signatures)} new signatures for {mint_address} (total: {len(all_signatures)})")
                            
                            # If we got fewer signatures than the limit or we're not trying to get the oldest,
                            # we can stop pagination
                            if len(signatures) < limit or not get_oldest:
                                logger.debug(f"Fetched all available signatures for {mint_address} (total: {len(all_signatures)})")
                                have_more_signatures = False
                                break
                            
                            # If we're trying to get the oldest signatures and got a full batch,
                            # we need to paginate further using before_signature
                            if get_oldest:
                                # Use the oldest signature in this batch as the before_signature for the next batch
                                oldest_signature = signatures[-1]  # Last is oldest since Solana returns newest first
                                current_pagination_param = {"before": oldest_signature}
                                logger.debug(f"Paginating to older signatures before: {oldest_signature}")
                            
                            # We successfully processed this batch, break out of the retry loop
                            break
                        else:
                            logger.warning(f"Unexpected response format: {response_json}")
                            await asyncio.sleep(current_retry_delay)
                            continue
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout fetching signatures for {mint_address}. Attempt {attempt+1}/{max_retries}")
                await asyncio.sleep(current_retry_delay)
                continue
            except Exception as e:
                logger.error(f"Error fetching signatures for {mint_address}: {e}")
                await asyncio.sleep(current_retry_delay)
                continue
        
        # If we exhausted all retries without success, break the pagination loop
        else:
            logger.error(f"Failed to fetch signatures for {mint_address} after {max_retries} retries")
            have_more_signatures = False
    
    logger.debug(f"Completed fetching signatures for {mint_address}. Total: {len(all_signatures)}")
    return all_signatures

async def fetch_transaction_batch(app, signatures, batch_size=100):
    """Fetch transaction details for a batch of signatures.
    
    Args:
        app: The FastAPI app instance
        signatures: List of transaction signatures to fetch
        batch_size: Maximum number of signatures to process in a single API call (default: 100)
        
    Returns:
        List of transaction objects
    """
    url = f"{SHYFT_API_BASE_URL}/transaction/parse_selected"
    max_retries = 5  # Increased from 3 to 5 for better reliability
    initial_retry_delay = 1.0
    
    # Extract signature strings from signature objects if needed
    signature_strings = []
    for sig in signatures:
        if isinstance(sig, dict) and 'signature' in sig:
            signature_strings.append(sig['signature'])
        else:
            signature_strings.append(sig)
    
    # Process signatures in smaller batches to avoid validation errors
    all_results = []
    for i in range(0, len(signature_strings), batch_size):
        batch = signature_strings[i:i+batch_size]
        logger.debug(f"Processing batch of {len(batch)} signatures ({i+1}-{min(i+batch_size, len(signature_strings))} of {len(signature_strings)})...")
        
        for attempt in range(max_retries):
            try:
                # Get a fresh API key for each attempt to maximize chances of success
                api_key, limiter = await api_key_manager.get_api_key_and_limiter()
                headers = {'Content-Type': 'application/json', 'x-api-key': api_key.strip()}
                
                payload = json.dumps({
                    "network": "mainnet-beta",
                    "transaction_signatures": batch,
                    "enable_raw": True,
                    "enable_events": False
                })
                
                async with limiter:
                    timeout = ClientTimeout(total=5)
                    async with app.state.http_session.post(url, headers=headers, data=payload, timeout=timeout) as response:
                        text = await response.text()
                        
                        # Handle rate limiting and credit exhaustion
                        if response.status == 429:
                            # Rate limiting - mark key temporarily unavailable
                            retry_after = int(response.headers.get('Retry-After', '5'))
                            logger.warning(f"Rate limit (429) hit for batch with {api_key}. Marking key temporarily unavailable and retrying after {retry_after}s...")
                            await api_key_manager.mark_api_key_unavailable(api_key)
                            await asyncio.sleep(retry_after)
                            continue
                        else:
                            # Check for credit exhaustion in non-429 responses
                            is_exhausted = False
                            exhaustion_indicators = [
                                "credits exhausted", "credits depleted", "insufficient credits",
                                "credit limit reached", "quota exceeded", "upgrade your plan",
                                "upgrade plan to continue", "please upgrade plan", "api limit exceeded"
                            ]
                            response_lower = text.lower()
                            for indicator in exhaustion_indicators:
                                if indicator in response_lower:
                                    is_exhausted = True
                                    logger.warning(f"Credits exhausted for {api_key} in batch processing (indicator: '{indicator}'). Marking key unavailable with extended cooldown.")
                                    await api_key_manager.mark_api_key_unavailable(api_key, is_credit_exhausted=True)
                                    break

                            if is_exhausted:
                                continue
                        
                        try:
                            response_json = json.loads(text)
                        except json.JSONDecodeError:
                            logger.error(f"JSON decode error: {text}")
                            await asyncio.sleep(initial_retry_delay * (attempt + 1))
                            continue

                        if response.status == 200:
                            result = response_json.get('result', [])
                            all_results.extend(result)
                            break  # Successfully processed this batch, move to next batch
                        else:
                            logger.warning(f"Failed to fetch transaction batch: Status {response.status}, Response: {text}")
                            if response.status == 400 and batch_size > 5 and "Validation failed" in text:
                                # If we hit a validation error, try with a smaller batch size for this batch
                                logger.warning(f"Validation error with batch size {batch_size}, retrying with smaller batch")
                                smaller_batch_results = await fetch_transaction_batch(app, batch, batch_size=max(5, batch_size // 2))
                                all_results.extend(smaller_batch_results)
                                break  # Move to next batch after handling with smaller batch size
                            else:
                                # For other errors, retry after delay
                                await asyncio.sleep(initial_retry_delay * (attempt + 1))
                            
            except asyncio.TimeoutError:
                logger.warning(f"Timeout fetching transaction batch. Attempt {attempt+1}/{max_retries}")
                await asyncio.sleep(initial_retry_delay * (attempt + 1))
            except Exception as e:
                logger.error(f"Error fetching transaction batch: {e}")
                await asyncio.sleep(initial_retry_delay * (attempt + 1))
        else:
            # Loop completed without a successful processing (no 'break' was called)
            logger.error(f"Failed to fetch batch of transactions after {max_retries} attempts")
            
    logger.debug(f"Fetched {len(all_results)} transactions from {len(signature_strings)} signatures")
    return all_results

async def fetch_chronological_transactions(app, mint_address, limit=2000, until_signature=None, required_prices=100):
    """Fetch the transactions for a token in chronological order (oldest first) until we have enough prices.
    
    Process flow:
    1. Fetch ALL signatures from newest to oldest using pagination
    2. Reverse them to get oldest first
    3. Fetch transactions for these signatures in chronological order
    4. Process transactions through the price extraction logic to count prices
    5. Continue fetching until we have the required number of prices
    
    Args:
        app: The FastAPI app instance
        mint_address: The mint address to fetch transactions for
        limit: Maximum number of signatures to fetch in one request (default: 2000)
        until_signature: Optional signature to start fetching after (for pagination)
        required_prices: Number of prices we need to collect (default: 100)
        
    Returns:
        Tuple of (list of transaction objects in chronological order, boolean indicating if all signatures were processed)
    """
    logger.debug(f"Fetching chronological transactions for {mint_address} to collect {required_prices} prices")
    from .accumulator import initialize_accumulator, process_transaction_for_accumulator, finalize_accumulator
    
    # Step 1: Get signatures for the mint address
    # The Solana RPC API has a maximum limit of 1000
    max_api_limit = 1000
    
    # Cap our fetch limit
    fetch_limit = min(limit, max_api_limit)
    
    # Initialize a price accumulator to track how many prices we've collected
    price_accumulator = initialize_accumulator()
    
    # Track all transactions we've processed
    all_transactions = []
    all_processed = False
    
    # Set to keep track of processed signatures
    processed_signatures = set()
    
    # Fetch initial signatures - get_oldest=True will paginate to get ALL signatures back to the first one
    initial_signatures = await fetch_signatures_for_address(app, mint_address, limit=fetch_limit, until_signature=until_signature, get_oldest=True)
    
    if not initial_signatures:
        logger.warning(f"No signatures found for {mint_address}")
        return [], True  # Return empty list and True for allProcessed
    
    # Step 2: Reverse signatures to get oldest first (since getSignaturesForAddress returns newest first)
    reversed_signatures = initial_signatures[::-1]
    logger.debug(f"Found {len(initial_signatures)} signatures for {mint_address}, processing oldest first")
    
    # Use a smaller batch size for more reliable processing
    batch_size = 100  # Process in larger batches to reduce API overhead
    
    # Process signatures in order from oldest to newest, until we have the required prices
    remaining_signatures = reversed_signatures.copy()
    current_newest_signature = None
    
    while remaining_signatures:
        # Take the next batch of oldest signatures
        current_batch = remaining_signatures[:batch_size]
        remaining_signatures = remaining_signatures[batch_size:]
        
        logger.debug(f"Processing batch of {len(current_batch)} signatures for {mint_address}, remaining in current set: {len(remaining_signatures)}")
        
        # Mark signatures as processed
        processed_signatures.update(sig['signature'] for sig in current_batch if 'signature' in sig)
        
        # Keep track of newest signature for pagination
        if current_batch and 'signature' in current_batch[-1]:
            current_newest_signature = current_batch[-1]['signature']
            
        # Fetch transactions for current batch
        transactions = await fetch_transaction_batch(app, current_batch)
        
        if transactions:
            # Add transactions to our collection
            all_transactions.extend(transactions)
            logger.debug(f"Added {len(transactions)} transactions, total: {len(all_transactions)}")
            
            # Process each transaction through accumulator to count prices
            initial_price_count = len(price_accumulator.get('prices', []))
            for tx in transactions:
                process_transaction_for_accumulator(tx, mint_address, price_accumulator)
            
            # Check if we have enough prices without finalizing the accumulator
            current_price_count = len(price_accumulator.get('prices', []))
            
            logger.debug(f"Total prices extracted: {current_price_count}/{required_prices}")
            
            if current_price_count >= required_prices:
                logger.debug(f"Reached target of {required_prices} prices from {len(all_transactions)} transactions")
                # Only finalize once at the end when we've collected enough prices
                finalize_accumulator(price_accumulator, mint_address)
                all_processed = True
                break
        else:
            logger.warning(f"No transactions retrieved for this batch of {len(current_batch)} signatures")
        
        # If we've processed all signatures in the current set but still don't have enough prices,
        # fetch more signatures using pagination
        if not remaining_signatures and not all_processed and current_newest_signature:
            logger.debug(f"Processed all signatures in current set but still need more prices. Fetching more signatures...")
            
            # Fetch next batch of signatures using until_signature for pagination
            more_signatures = await fetch_signatures_for_address(app, mint_address, limit=fetch_limit, until_signature=current_newest_signature)
            
            if not more_signatures:
                logger.warning(f"No more signatures available for {mint_address}")
                break
                
            # Filter out signatures we've already processed
            more_signatures = [sig for sig in more_signatures if sig['signature'] not in processed_signatures]
            
            if not more_signatures:
                logger.warning(f"No new signatures found for {mint_address}")
                break
                
            # Reverse to get oldest first and add to remaining signatures
            more_signatures.reverse()
            remaining_signatures.extend(more_signatures)
            logger.debug(f"Added {len(more_signatures)} more signatures to process")
            
            # Update newest signature for next pagination if needed
            if more_signatures and 'signature' in more_signatures[-1]:
                current_newest_signature = more_signatures[-1]['signature']
    
    # All transactions are now in chronological order (oldest first)
    logger.debug(f"Retrieved {len(all_transactions)} transactions for {mint_address} yielding {len(price_accumulator.get('prices', []))} prices")
    
    # Double-check the chronological ordering by logging a few timestamps
    if len(all_transactions) > 1:
        try:
            first_tx = all_transactions[0]
            last_tx = all_transactions[-1]
            first_time = first_tx.get('blockTime', first_tx.get('timestamp'))
            last_time = last_tx.get('blockTime', last_tx.get('timestamp'))
            first_sig = first_tx.get('signature', 'N/A')[:10]
            last_sig = last_tx.get('signature', 'N/A')[:10]
            
            if first_time and last_time:
                logger.debug(f"Chronological check: First tx {first_sig} time: {first_time}, Last tx {last_sig} time: {last_time}")
                if first_time <= last_time:
                    logger.debug("Transactions are in correct chronological order (oldest -> newest)")
                else:
                    logger.warning("Transactions may not be in correct order - first tx is newer than last tx!")
        except Exception as e:
            logger.error(f"Error checking chronological order: {e}")
    
    return all_transactions, all_processed

async def test_fetch_chronological(mint_address, required_prices=100):
    """Test function to verify chronological ordering of transactions and price collection"""
    import asyncio
    from fastapi import FastAPI
    import aiohttp
    from datetime import datetime
    
    app = FastAPI()
    app.state.http_session = aiohttp.ClientSession()
    
    try:
        # Fetch transactions in chronological order until we have enough prices
        print(f"Testing chronological fetching for {mint_address} to collect {required_prices} prices")
        transactions, processed_count = await fetch_chronological_transactions(app, mint_address, limit=100, required_prices=required_prices)
        
        # Get accumulator details
        from .accumulator import initialize_accumulator, process_transaction_for_accumulator, finalize_accumulator
        test_accumulator = initialize_accumulator()
        for tx in transactions:
            process_transaction_for_accumulator(tx, mint_address, test_accumulator)
        finalize_accumulator(test_accumulator, mint_address)
        
        # Verify ordering and price count
        if transactions and len(transactions) > 0:
            timestamps = [tx.get('blockTime', 0) for tx in transactions]
            price_count = len(test_accumulator.get('prices', []))
            
            # Print first 5 and last 5 transactions with timestamps
            print(f"Found {len(transactions)} transactions yielding {price_count} prices")
            print("First 5 transactions:")
            for i, tx in enumerate(transactions[:5]):
                dt = datetime.fromtimestamp(tx.get('blockTime', 0)).strftime('%Y-%m-%d %H:%M:%S')
                sig = tx.get('transaction', {}).get('signatures', ['unknown'])[0][:10]
                print(f"  {i+1}. {dt} - Sig: {sig}...")
                
            if len(transactions) > 5:
                print("Last 5 transactions:")
                for i, tx in enumerate(transactions[-5:]):
                    dt = datetime.fromtimestamp(tx.get('blockTime', 0)).strftime('%Y-%m-%d %H:%M:%S')
                    sig = tx.get('transaction', {}).get('signatures', ['unknown'])[0][:10]
                    print(f"  {len(transactions)-4+i}. {dt} - Sig: {sig}...")
            
            # Check if timestamps are in order (ascending)
            is_ordered = all(timestamps[i] <= timestamps[i+1] for i in range(len(timestamps)-1))
            print(f"Transactions in chronological order: {is_ordered}")
            print(f"Collected {price_count} prices from {len(transactions)} transactions")
            print(f"Target met: {price_count >= required_prices}")
            
            # Show first few prices
            prices = test_accumulator.get('prices', [])
            if prices:
                print(f"\nFirst 5 prices:")
                for i, price in enumerate(prices[:5]):
                    print(f"  {i+1}. {price}")
        else:
            print("No transactions found or empty list returned")
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await app.state.http_session.close()

# Command-line execution
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "test_fetch_chronological" and len(sys.argv) > 2:
            mint_address = sys.argv[2]
            required_prices = 100
            if len(sys.argv) > 3:
                required_prices = int(sys.argv[3])
            asyncio.run(test_fetch_chronological(mint_address, required_prices))
        else:
            print("Usage: python -m solbot.data_fetching test_fetch_chronological <mint_address> [required_prices]")
    else:
        print("Usage: python -m solbot.data_fetching <command> <args>")