import { useEffect, useState, useCallback, useMemo } from "react";
import { useAuth } from "@/components/auth-context";
import { fetchAuthenticatedApi } from "@/lib/api";
import { DashboardDataResponse, BotStatusEnum } from "@/types/api";

export function useDashboardManager() {
  // Dashboard data states
  const [dashboardData, setDashboardData] = useState<DashboardDataResponse | null>(null);
  const [isDashboardLoading, setIsDashboardLoading] = useState(true);
  const [dashboardError, setDashboardError] = useState<string | null>(null);
  
  // Bot status state
  const [uiBotStatus, setUiBotStatus] = useState<BotStatusEnum | null>(null);
  
  // Live prices and monitoring status
  const [livePrices, setLivePrices] = useState<Record<string, number | null>>({});
  const [monitoringStatusMap, setMonitoringStatusMap] = useState<Record<string, boolean>>({});
  
  const { isAuthenticated, isLoadingAuth, handleLogout } = useAuth();
  
  // Define fetchData callback
  const fetchData = useCallback(async () => {
    console.log(`[fetchData Start] Called. isAuthenticated: ${isAuthenticated}, isLoadingAuth: ${isLoadingAuth}`);

    if (!isAuthenticated || isLoadingAuth) {
      console.log("[fetchData] Skipping fetch: Not authenticated or auth still loading.");
      setIsDashboardLoading(false); // Explicitly set loading to false if skipping
      return;
    }

    console.log("[fetchData] Proceeding with fetch..."); // Log when proceeding
    setIsDashboardLoading(true); // Set loading ONLY when actually fetching
    setDashboardError(null);

    try {
      const response = await fetchAuthenticatedApi<DashboardDataResponse>('/api/dashboard/data');
      console.log("Dashboard API response in fetchData:", response);

      if (response.success && response.data) {
        console.log("[fetchData Success] Received data:", JSON.stringify(response.data, null, 2));
        setDashboardData({ ...response.data }); // Use spread syntax to create a shallow copy
        console.log("[fetchData Success] Called setDashboardData with new object reference");
        if (response.data.bot_state?.status) {
          console.log("[fetchData Success] Bot State from API:", JSON.stringify(response.data.bot_state, null, 2));
          setUiBotStatus(response.data.bot_state.status);
        }
      } else {
        // Check for 401 Unauthorized error message
        if (typeof response.error === 'string' && 
           (response.error.includes('validate credentials') || 
            response.error.includes('Authentication token') || 
            response.error === '401 Unauthorized')) {
          console.log("[fetchData] Received auth error, logging out:", response.error);
          handleLogout();
          return;
        }
        setDashboardError(typeof response.error === 'string' ? response.error : 'Failed to load dashboard data');
      }
    } catch (error) {
      setDashboardError('An unexpected error occurred during fetch.');
      console.error('Dashboard data fetch error in fetchData:', error);
    } finally {
      setIsDashboardLoading(false);
    }
  }, [isAuthenticated, isLoadingAuth, handleLogout]);
  
  // Define fetchLivePrices callback
  const fetchLivePrices = useCallback(async () => {
    if (!isAuthenticated) return; // Only fetch if authenticated

    try {
      // Assume endpoint returns: { success: true, data: { mint1: price1, mint2: price2, ... } }
      const response = await fetchAuthenticatedApi<Record<string, number | null>>('/api/dashboard/prices/live');

      if (response.success && response.data) {
        setLivePrices(response.data);
      } else {
        // Check for 401 Unauthorized error message
        if (typeof response.error === 'string' && 
           (response.error.includes('validate credentials') || 
            response.error.includes('Authentication token') || 
            response.error === '401 Unauthorized')) {
          console.log("[fetchLivePrices] Received auth error, logging out:", response.error);
          handleLogout();
          return;
        }
        // Don't show error for price fetch failures, just log it
        console.warn('[Live Prices] Failed to fetch live prices:', response.error);
        // Keep stale prices rather than clearing them
      }
    } catch (error) {
      console.error('[Live Prices] Error fetching live prices:', error);
    }
  }, [isAuthenticated, handleLogout]);

  // Define fetchMonitoringStatus callback
  const fetchMonitoringStatus = useCallback(async () => {
    if (!isAuthenticated) return; // Only fetch if authenticated

    try {
      const response = await fetchAuthenticatedApi<Record<string, boolean>>('/api/dashboard/monitoring-status');

      if (response.success && response.data) {
        // Only update the monitoring status map
        setMonitoringStatusMap(response.data);
      } else {
        // Check for 401 Unauthorized error message
        if (typeof response.error === 'string' && 
           (response.error.includes('validate credentials') || 
            response.error.includes('Authentication token') || 
            response.error === '401 Unauthorized')) {
          console.log("[fetchMonitoringStatus] Received auth error, logging out:", response.error);
          handleLogout();
          return;
        }
        console.warn('[Monitoring Status] Failed to fetch monitoring status:', response.error);
      }
    } catch (error) {
      console.error('[Monitoring Status] Error fetching monitoring status:', error);
    }
  }, [isAuthenticated, handleLogout]);
  
  // Fetch dashboard data on authentication change
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  // Setup periodic refresh for live prices and monitoring status
  useEffect(() => {
    let priceIntervalId: NodeJS.Timeout | null = null;
    let monitoringStatusIntervalId: NodeJS.Timeout | null = null;

    if (isAuthenticated) {
      console.log("[Dashboard Refresh] Setting up initial data fetch and periodic price fetch.");
      // Initial fetch of prices and monitoring status
      fetchLivePrices();
      fetchMonitoringStatus();

      // Fetch live prices more frequently
      priceIntervalId = setInterval(() => {
        fetchLivePrices();
      }, 7500); // Refresh prices every 7.5 seconds
      
      // Only fetch monitoring status updates periodically
      monitoringStatusIntervalId = setInterval(() => {
        console.log("[Dashboard Refresh] Fetching monitoring status updates.");
        fetchMonitoringStatus();
      }, 30000); // Check monitoring status every 30 seconds
    } else {
      console.log("[Dashboard Refresh] User not authenticated, periodic fetch not started.");
    }

    // Cleanup function
    return () => {
      if (priceIntervalId) {
        console.log("[Dashboard Refresh] Clearing periodic price fetch interval.");
        clearInterval(priceIntervalId);
      }
      if (monitoringStatusIntervalId) {
        console.log("[Dashboard Refresh] Clearing periodic monitoring status fetch interval.");
        clearInterval(monitoringStatusIntervalId);
      }
    };
  }, [isAuthenticated, fetchLivePrices, fetchMonitoringStatus]);

  return useMemo(() => ({
    dashboardData,
    isDashboardLoading,
    dashboardError,
    uiBotStatus,
    setUiBotStatus,
    fetchData,
    livePrices,
    monitoringStatusMap
  }), [
    dashboardData,
    isDashboardLoading,
    dashboardError,
    uiBotStatus,
    fetchData,
    livePrices,
    monitoringStatusMap
  ]);
} 