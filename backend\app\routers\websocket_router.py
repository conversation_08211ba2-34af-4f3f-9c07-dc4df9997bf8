from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Request, Query, status
import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db_session, async_session_factory
from app.db_models import User
from app.dependencies import get_current_user
from app.crud.bot_state_crud import set_bot_status
from app.schemas.bot_schemas import BotStatusEnum
from app.bot_manager import bot_manager
import json

logger = logging.getLogger(__name__)
router = APIRouter(tags=["websockets"])

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str | None = Query(None), db: AsyncSession = Depends(get_db_session)):
    user: User | None = None
    user_wallet: str | None = None
    
    try:
        if not token:
            logger.warning("WebSocket connection attempt without token")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
            
        # Call dependency function directly with token and db session
        user = await get_current_user(token=token, db=db)
        user_wallet = user.wallet_address
        logger.info(f"WebSocket authenticated for user: {user_wallet}")
    except HTTPException as http_exc:
        logger.warning(f"WebSocket authentication failed: {http_exc.detail}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    except Exception as auth_err:
        logger.error(f"Unexpected WebSocket auth error: {auth_err}", exc_info=True)
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
        return

    # If auth successful, user and user_wallet are set
    await websocket.accept()
    logger.info(f"WebSocket accepted for authenticated user: {user_wallet}")

    # Store the connection
    app = websocket.app
    async with app.state.connections_lock:
        # Optional: Disconnect previous connection if exists for this user
        # existing_ws = app.state.active_connections.get(user_wallet)
        # if existing_ws:
        #     try: await existing_ws.close(code=1000)
        #     except: pass # Ignore errors closing old socket
        app.state.active_connections[user_wallet] = websocket
        logger.info(f"Stored active WebSocket connection for user: {user_wallet}. Total: {len(app.state.active_connections)}")
    
    # Send server version ID immediately after connection is established
    try:
        server_info = {
            "type": "server_info",
            "server_version_id": app.state.server_version_id,
            "message": "Server connection established"
        }
        await websocket.send_text(json.dumps(server_info))
        logger.debug(f"Sent server version info to {user_wallet}: {app.state.server_version_id}")
    except Exception as e:
        logger.error(f"Error sending server info to {user_wallet}: {e}")

    try:
        # Keep the connection alive, listening for potential messages from client (optional)
        while True:
            # You can receive messages here if needed, e.g., ping/pong or commands
            # For now, just keep alive or handle disconnect
            data = await websocket.receive_text()
            logger.debug(f"Received message from {user_wallet}: {data}")
            
            # Process received messages
            try:
                message = json.loads(data)
                if message.get('type') == 'user_disconnect':
                    logger.info(f"Received explicit disconnect from user {user_wallet}: {message.get('message', '')}")
                    
                    # Stop the bot immediately
                    try:
                        # Update database state to STOPPED
                        async with async_session_factory() as db:
                            await set_bot_status(db, user_wallet, BotStatusEnum.STOPPED)
                            logger.info(f"Updated bot state to STOPPED for {user_wallet} after explicit disconnect")
                        
                        # Stop the bot task
                        await bot_manager.stop_bot(user_wallet)
                        logger.info(f"Stopped bot for {user_wallet} after explicit disconnect")
                    except Exception as e:
                        logger.error(f"Error stopping bot for {user_wallet} after explicit disconnect: {e}")
                    
                    # Close the connection with clean closure code
                    await websocket.close(code=1000, reason="User requested disconnect")
                    return
            except json.JSONDecodeError:
                logger.warning(f"Received invalid JSON from {user_wallet}: {data}")
            except Exception as e:
                logger.error(f"Error processing message from {user_wallet}: {e}")
            
            # Example echo: await websocket.send_text(f"Echo: {data}")
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user: {user_wallet}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_wallet}: {e}", exc_info=True)
    finally:
        # Remove the connection on disconnect/error
        async with app.state.connections_lock:
            # Check if the stored connection is the one that disconnected
            stored_ws = app.state.active_connections.get(user_wallet)
            if stored_ws is websocket:
                app.state.active_connections.pop(user_wallet, None)
                logger.info(f"Removed WebSocket connection for user: {user_wallet}. Total: {len(app.state.active_connections)}")
            else:
                logger.info(f"WebSocket for {user_wallet} already replaced or removed.")
        
        # Stop the user's bot when their WebSocket connection is closed
        try:
            # First update the database state to STOPPED
            async with async_session_factory() as db:
                await set_bot_status(db, user_wallet, BotStatusEnum.STOPPED)
                logger.info(f"Updated bot state to STOPPED for {user_wallet} after WebSocket disconnect")
            
            # Then stop the bot task if it's running
            await bot_manager.stop_bot(user_wallet)
            logger.info(f"Stopped bot for {user_wallet} after WebSocket disconnect")
        except Exception as e:
            logger.error(f"Error stopping bot for {user_wallet} after WebSocket disconnect: {e}") 