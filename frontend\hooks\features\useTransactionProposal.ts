import { useState, useRef, useEffect, useCallback } from 'react'
import { PublicKey, Connection } from '@solana/web3.js'
import { WalletContextState } from '@solana/wallet-adapter-react'
import { Adapter as WalletAdapter } from '@solana/wallet-adapter-base'
import { TransactionProposalData, ConfigurationResponse } from '@/types/api'
import { toast } from "sonner"
import { fetchAuthenticatedApi } from "@/lib/api"
import { executePumpFunTrade } from '@/lib/pumpfun'

export function useTransactionProposal(
  publicKey: PublicKey | null,
  sendTransaction: WalletContextState['sendTransaction'],
  connection: Connection,
  walletAdapter: WalletAdapter | null,
  fetchDataForRefresh: () => Promise<void>,
  currentConfigData: ConfigurationResponse | null,
  treasuryWalletAddress: string | null,
  signTransaction?: WalletContextState['signTransaction']
) {
  // State definitions
  const [currentProposal, setCurrentProposal] = useState<TransactionProposalData | null>(null);
  const [isProposalModalOpen, setIsProposalModalOpen] = useState(false);
  const [isProcessingProposal, setIsProcessingProposal] = useState(false);
  const [proposalStartTime, setProposalStartTime] = useState<number | null>(null);
  const [rejectedProposals, setRejectedProposals] = useState<Record<string, number>>({});
  // New state for sell proposal queue
  const [sellProposalQueue, setSellProposalQueue] = useState<TransactionProposalData[]>([]);
  
  // Refs for websocket handler access
  const currentProposalRef = useRef<TransactionProposalData | null>(null);
  const isProposalModalOpenRef = useRef<boolean>(false);
  const isProcessingProposalRef = useRef<boolean>(false);
  const rejectedProposalsRef = useRef<Record<string, number>>({});
  // New ref for sell proposal queue
  const sellProposalQueueRef = useRef<TransactionProposalData[]>([]);
  
  // Treasury wallet address ref for consistent access
  const treasuryWalletAddressRef = useRef<string | null>(treasuryWalletAddress);
  
  // Update treasury wallet address ref when it changes
  useEffect(() => {
    treasuryWalletAddressRef.current = treasuryWalletAddress;
    if (treasuryWalletAddress) {
      console.log(`[Proposal] Treasury wallet address set: ${treasuryWalletAddress}`);
    }
  }, [treasuryWalletAddress]);
  
  // Synchronize refs with state whenever they change
  useEffect(() => {
    currentProposalRef.current = currentProposal;
  }, [currentProposal]);
  
  useEffect(() => {
    isProposalModalOpenRef.current = isProposalModalOpen;
  }, [isProposalModalOpen]);
  
  useEffect(() => {
    isProcessingProposalRef.current = isProcessingProposal;
  }, [isProcessingProposal]);

  useEffect(() => {
    rejectedProposalsRef.current = rejectedProposals;
  }, [rejectedProposals]);
  
  // New effect to sync sell proposal queue ref
  useEffect(() => {
    sellProposalQueueRef.current = sellProposalQueue;
  }, [sellProposalQueue]);

  // Set proposal start time when a new proposal becomes active
  useEffect(() => {
    if (isProposalModalOpen && currentProposal && proposalStartTime === null) {
      setProposalStartTime(Date.now());
    } else if (!isProposalModalOpen && !currentProposal) {
      setProposalStartTime(null);
    }
  }, [isProposalModalOpen, currentProposal, proposalStartTime]);
  
  // Helper function to process the next sell proposal in queue
  const processNextSellProposal = useCallback(() => {
    if (sellProposalQueueRef.current.length > 0) {
      console.log(`[Queue] Processing next sell proposal from queue. Queue length: ${sellProposalQueueRef.current.length}`);
      const nextProposal = sellProposalQueueRef.current[0];
      // Remove the processed proposal from the queue
      setSellProposalQueue(prev => prev.slice(1));
      // Process the next proposal
      setTimeout(() => {
        // Use the handleTransactionProposal directly since we're in the same scope
        handleTransactionProposal(nextProposal);
      }, 100);
    }
  }, []);

  // Handle transaction proposal - moved from page.tsx
  const handleTransactionProposal = useCallback((proposalData: TransactionProposalData) => {
    // Add console logging for debugging
    console.log("[Proposal Debug] handleTransactionProposal called with:", proposalData);
    console.log("[Proposal Debug] Current state:", {
      isProposalModalOpen: isProposalModalOpenRef.current,
      isProcessingProposal: isProcessingProposalRef.current,
      hasCurrentProposal: currentProposalRef.current !== null
    });
    
    // Double-check if another proposal is already being shown or processed - use refs now
    if (isProposalModalOpenRef.current || isProcessingProposalRef.current || currentProposalRef.current) {
      console.warn(`[Proposal] Ignoring new proposal for ${proposalData.mint_address} because another proposal is already active or being processed.`);
      
      // For debugging - log the currently active proposal that's blocking the new one
      if (currentProposalRef.current) {
        console.log(`[Proposal Conflict] Current proposal: ${currentProposalRef.current.type} for ${currentProposalRef.current.mint_address}`);
      }
      
      console.log(`[Proposal Conflict] State: Modal open=${isProposalModalOpenRef.current}, Processing=${isProcessingProposalRef.current}, Has proposal=${currentProposalRef.current !== null}`);
      
      // NEW: If the incoming proposal is a 'sell' type, queue it
      if (proposalData.type === 'sell') {
        console.log(`[Proposal] New proposal is a SELL, adding to queue: ${proposalData.mint_address}`);
        setSellProposalQueue(prevQueue => [...prevQueue, proposalData]);
      }
      
      return;
    }
    
    console.log("[Proposal] Showing confirmation modal for:", proposalData);
    // Save the proposal to state - this is what ensures hasActiveProposal() returns true
    setCurrentProposal(proposalData);
    setIsProposalModalOpen(true);
    setIsProcessingProposal(false); // Ensure loading state is reset when modal opens
  }, []);

  // New function to clear the current proposal
  const clearCurrentProposal = useCallback(() => {
    console.log("[Proposal] Clearing current proposal state");
    setCurrentProposal(null);
  }, []);

  // Handle modal close action - moved from page.tsx
  const handleModalClose = useCallback(() => {
    console.log("[Proposal Modal] User closed the modal without taking action.");
    
    // When the modal is closed by clicking outside (backdrop click),
    // we should treat it as a rejection to ensure new proposals can come through
    if (currentProposal) {
      const mintToReject = currentProposal.mint_address;
      console.log(`[Proposal Modal Close] User clicked outside proposal for ${mintToReject}. Treating as rejection.`);
      // Use functional update to ensure we're working with the latest state
      setRejectedProposals(prev => {
        const newState = { ...prev, [mintToReject]: Date.now() };
        // Update ref immediately
        rejectedProposalsRef.current = newState;
        console.log("[Proposal Modal Close] Updated rejectedProposalsRef.current:", JSON.stringify(rejectedProposalsRef.current));
        console.log("[Proposal Modal Close] Updated rejectedProposals state:", JSON.stringify(newState));
        return newState;
      });
    }
    
    // Hide the modal
    setIsProposalModalOpen(false);
    
    // Clear the proposal state - critical for allowing new proposals
    clearCurrentProposal();
    setIsProcessingProposal(false);
    
    // Process the next proposal in the queue if available
    setTimeout(() => {
      processNextSellProposal();
    }, 500); // Small delay to ensure state is updated
  }, [currentProposal, clearCurrentProposal, processNextSellProposal]);

  // Handle cancellation of proposal (modified to use internal processNextSellProposal)
  const handleCancelProposal = useCallback(() => {
    if (currentProposal) {
      const mintToReject = currentProposal.mint_address;
      console.log(`[Proposal Cancel] User rejected proposal for ${mintToReject}. Adding to ignore list.`);
      // Use functional update to ensure we're working with the latest state
      setRejectedProposals(prev => {
        const newState = { ...prev, [mintToReject]: Date.now() };
        // Update ref immediately
        rejectedProposalsRef.current = newState;
        console.log("[Proposal Cancel] Updated rejectedProposalsRef.current:", JSON.stringify(rejectedProposalsRef.current));
        console.log("[Proposal Cancel] Updated rejectedProposals state:", JSON.stringify(newState));
        return newState;
      });
    }
    
    // Use functions to clean up state
    handleModalClose();
    clearCurrentProposal();
    setIsProcessingProposal(false);
    
    // Check if there are any queued sell proposals to process (using internal function now)
    setTimeout(() => {
      processNextSellProposal();
    }, 500); // Small delay to ensure state is updated
  }, [currentProposal, handleModalClose, clearCurrentProposal, processNextSellProposal]);

  // Execute an approved transaction proposal
  const executeApprovedProposal = useCallback(async () => {
    if (!currentProposal || !publicKey || !connection || !sendTransaction || !walletAdapter) {
      console.error("Execute prerequisites missing (proposal/wallet/connection).");
      toast.error("Cannot execute: wallet not connected or proposal missing.");
      return;
    }

    // Ensure treasuryWalletAddress is available
    if (!treasuryWalletAddressRef.current) {
      toast.error("Treasury wallet address not available. Cannot proceed with trade.");
      setIsProcessingProposal(false); // Reset loading state
      return;
    }

    setIsProcessingProposal(true);
    console.log(`[Execute Proposal] Starting execution of ${currentProposal.type} for ${currentProposal.mint_address}`);

    // Define report payload
    let reportPayload: {
      user_wallet: string;
      status: string;
      trade_type: string;
      mint_address: string;
      tx_signature: string | null;
      amount_token: number;
      price_sol: number;
      total_sol: number;
      error_message: string | null;
      market?: string;
      fee_amount_sol?: number;
      fee_destination_wallet?: string;
    } = {
      user_wallet: publicKey.toBase58(),
      status: 'failure',
      trade_type: currentProposal.type.toUpperCase(),
      mint_address: currentProposal.mint_address,
      tx_signature: null,
      amount_token: currentProposal.token_amount || 0,
      price_sol: 0,
      total_sol: currentProposal.sol_amount || 0,
      error_message: 'Unknown failure'
    };

    try {
      const tradeType = currentProposal.type;
      const signature = await executePumpFunTrade({
        connection,
        wallet: {
          publicKey,
          signTransaction,
          sendTransaction
        } as WalletContextState,
        mintAddress: currentProposal.mint_address,
        tradeType,
        solAmount: currentProposal.sol_amount,
        tokenAmount: currentProposal.token_amount,
        slippageBps: tradeType === 'buy'
          ? currentConfigData?.buy_slippage_bps ?? 150
          : currentConfigData?.sell_slippage_bps ?? 150,
        treasuryWalletAddress: treasuryWalletAddressRef.current,
        priorityFeeMicroLamports: currentConfigData?.priority_fee_microlamports,
      });

      if (!signature) {
        throw new Error("Trade execution failed to return a signature.");
      }

      // Simplified success/failure reporting
      reportPayload.status = 'success';
      reportPayload.tx_signature = signature;

      toast.success(`${tradeType.toUpperCase()} transaction successful!`);
      
    } catch (error) {
      console.error(`[Execute Proposal] ${currentProposal.type.toUpperCase()} failed:`, error);
      const errorMsg = error instanceof Error ? error.message : String(error);
      toast.error(`${currentProposal.type.toUpperCase()} failed: ${errorMsg}`);

      reportPayload.status = 'failure';
      reportPayload.error_message = errorMsg;
    } finally {
      // Report trade to backend
      try {
        await fetchAuthenticatedApi('/api/trade/report', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(reportPayload),
        });
        console.log(`Trade report sent for ${currentProposal.mint_address} with status: ${reportPayload.status}`);
      } catch (reportError) {
        console.error("Error reporting trade:", reportError);
      }

      // Cleanup logic
      handleModalClose();
      clearCurrentProposal();
      setIsProcessingProposal(false);

      setTimeout(() => {
        fetchDataForRefresh();
        setTimeout(() => {
          processNextSellProposal();
        }, 500);
      }, 1000);
    }
  }, [currentProposal, publicKey, connection, sendTransaction, walletAdapter, 
       handleModalClose, clearCurrentProposal, processNextSellProposal, currentConfigData, fetchDataForRefresh]);

  return {
    currentProposal,
    isProposalModalOpen,
    isProcessingProposal,
    proposalStartTime,
    rejectedProposals,
    handleTransactionProposal,
    handleModalClose,
    clearCurrentProposal,
    handleCancelProposal,
    processNextSellProposal,
    executeApprovedProposal, // Added executeApprovedProposal to the return object
  };
} 