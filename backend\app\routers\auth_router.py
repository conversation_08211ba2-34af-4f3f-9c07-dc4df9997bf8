from fastapi import APIRouter, Depends, HTTPException, Request, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import uuid
import time

from app.database import get_db_session
from app.crud.user_crud import user_crud
from app.schemas.auth_schemas import AuthVerifyPayload, AuthVerifyResponse
from app.core.security import verify_siws_message, create_access_token
from app.dependencies import CustomRateLimiter, get_current_user
from app.db_models import User

# Get the logger
logger = logging.getLogger(__name__)

# Create router for auth endpoints
router = APIRouter(
    prefix="/api/auth",
    tags=["auth"],
    dependencies=[Depends(CustomRateLimiter(times=5, minutes=1))])  # 5 requests per minute


@router.post("/verify", response_model=AuthVerifyResponse,
            dependencies=[Depends(CustomRateLimiter(times=5, minutes=1))])  # 5 requests per minute
async def verify_siws_login(
    payload: AuthVerifyPayload,
    request: Request,  # Get request to access app state
    db: AsyncSession = Depends(get_db_session)
):
    logger.info(f"Attempting SIWS verification for {payload.walletAddress}")
    app = request.app

    # Verify the signature and message details
    is_valid = await verify_siws_message(
        message=payload.message,
        signature_b64=payload.signature,
        expected_address=payload.walletAddress,
        app_state=app.state  # Pass app state for nonce checking
    )

    if not is_valid:
        logger.warning(f"SIWS verification failed for {payload.walletAddress}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="SIWS verification failed. Invalid signature, message, or nonce."
        )

    # Verification successful, ensure user exists in DB
    user = await user_crud.get_or_create_user(db, payload.walletAddress)

    # Generate JWT access token
    access_token = create_access_token(subject=user.wallet_address)

    logger.info(f"[SIWS] SIWS verification successful, JWT issued for {user.wallet_address}")
    return AuthVerifyResponse(
        access_token=access_token,
        walletAddress=user.wallet_address
    )

@router.get("/challenge")
async def get_siws_challenge(request: Request):
    logger.debug("[SIWS] Challenge requested.")
    app = request.app
    nonce = str(uuid.uuid4().hex)  # Use hex for alphanumeric nonce
    # Make nonce validity shorter, e.g., 2 minutes
    expiry_seconds = 120
    expiry = time.time() + expiry_seconds

    async with app.state.siws_nonce_lock:
        # --- Nonce Cleanup (More Robust) ---
        current_time = time.time()
        # Create a new dict comprehension for non-expired nonces
        valid_nonces = {n: exp for n, exp in app.state.siws_nonces.items() if exp >= current_time}
        # Assign the cleaned dict back
        app.state.siws_nonces = valid_nonces
        # ------------------------------------

        # Store new nonce
        app.state.siws_nonces[nonce] = expiry
        logger.info(f"[SIWS] Generated SIWS nonce: {nonce}, valid for {expiry_seconds}s. Stored nonces: {len(app.state.siws_nonces)}")

    return {"nonce": nonce}

@router.get("/check")
async def check_token_validity(current_user: User = Depends(get_current_user)):
    """Simple endpoint to check if a token is still valid.
    Returns 200 OK if valid, 401 Unauthorized if not."""
    # If we got here, token is valid (because of get_current_user dependency)
    logger.debug(f"[Auth] Token validity check successful for {current_user.wallet_address}")
    return {"valid": True, "wallet_address": current_user.wallet_address} 