"use client";

import React from 'react';
import { Zap, Shield, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { HoldingItem } from '@/types/api';
import HoldingCard from './holding-card';
import { useAuth } from '@/components/auth-context';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface HoldingsSectionProps {
    holdings: HoldingItem[] | undefined;
    livePrices: Record<string, number | null>;
    monitoringStatus?: Record<string, boolean>;
    isLoading: boolean;
    onSell: (holding: HoldingItem) => void;
    onPartialSell: (holding: HoldingItem, percentage: number) => void;
    onBuy?: (holding: HoldingItem, solAmount: number) => void;
    sellingMint: string | null;
    buyingMint?: string | null;
    className?: string;
    disabled?: boolean;
}

const HoldingsSection = ({ 
    holdings, 
    livePrices,
    monitoringStatus = {},
    isLoading, 
    onSell,
    onPartialSell,
    onBuy,
    sellingMint,
    buyingMint,
    className,
    disabled = false
}: HoldingsSectionProps) => {
    const { isAuthenticated } = useAuth();
    
    // Calculate total value - since HoldingItem doesn't have value_usd, we'll estimate it
    const totalValue = holdings?.reduce((sum, holding) => {
        // Get the actual monitoring status (from the map if available, otherwise from the holding)
        const isMonitoringActive = monitoringStatus[holding.token_mint] !== undefined 
            ? monitoringStatus[holding.token_mint] 
            : holding.monitoring_active;
        
        // Only use current price if monitoring is active
        const currentPrice = isMonitoringActive 
            ? (livePrices[holding.token_mint] || holding.current_price_sol || 0)
            : (holding.current_price_sol || 0);
        const valueInSol = holding.amount_held * currentPrice;        
        return sum + valueInSol;
    }, 0) || 0;

    // Count inactive monitored holdings using the most up-to-date status
    const inactiveCount = holdings?.filter(h => {
        // Get monitoring status from the map if available, otherwise use the holding's status
        return monitoringStatus[h.token_mint] !== undefined 
            ? !monitoringStatus[h.token_mint] 
            : !h.monitoring_active;
    }).length || 0;

    // Determine grid classes based on number of holdings
    const gridClasses = !holdings || holdings.length === 0
        ? "col-span-full" // Empty state
        : holdings.length === 1
            ? "col-span-full" // Single holding takes full width
            : "md:grid-cols-2"; // Multiple holdings - max 2 per row

    return (
        <Card className={`glass-card glass-card-hover ${className || ''}`}>
            <CardHeader>
                <CardTitle>Your Holdings</CardTitle>
                <CardDescription className="flex flex-wrap items-center gap-4">
                    <span>Total Value: {totalValue.toFixed(4)} SOL</span>
                    <span className="flex items-center gap-2">
                        <span>Tokens: {holdings?.length || 0}</span>
                        {inactiveCount > 0 && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <span className="bg-status-warning/10 px-3 py-1 rounded-full text-xs border border-status-warning/30 flex items-center gap-1 cursor-help">
                                            <AlertCircle className="h-3 w-3 text-status-warning" />
                                            <span className="text-status-warning">{inactiveCount} inactive</span>
                                        </span>
                                    </TooltipTrigger>
                                    <TooltipContent side="bottom" className="text-xs max-w-xs">
                                        <p>
                                            {inactiveCount} token(s) currently have inactive price monitoring. This means automated sell proposals (like Take Profit or Stop Loss) will not be triggered for them. However, you can still trade these tokens manually using the Buy/Sell buttons on the card.
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )}
                    </span>
                </CardDescription>
            </CardHeader>
            <CardContent>
                {!isAuthenticated ? (
                    <div className="flex flex-col items-center justify-center p-10 text-center">
                        <Shield className="h-16 w-16 text-foreground/20 mb-4" />
                        <h3 className="text-xl font-semibold mb-2 text-foreground/20">Authentication Required</h3>
                        <p className="text-foreground/20 max-w-md">
                            Please connect wallet and sign in to view your holdings.
                        </p>
                    </div>
                ) : (
                    <div className={`grid grid-cols-1 ${gridClasses} gap-3 p-3`}>
                        {isLoading ? (
                            // Loading skeletons
                            Array(3).fill(0).map((_, i) => (
                                <div 
                                    key={`skeleton-${i}`} 
                                    className="h-[180px] bg-[#111111] animate-pulse rounded-xl p-4"
                                />
                            ))
                        ) : !holdings || holdings.length === 0 ? (
                            <div className="col-span-full flex items-center justify-center h-40 text-white/40 bg-[#0A0A0A] rounded-xl border border-[rgba(75,75,75,0.15)]">
                                No tokens currently held by the bot.
                            </div>
                        ) : (
                            holdings.map((holding) => {
                                // Get effective monitoring status (from map if available, otherwise from holding)
                                const effectiveStatus = {
                                    ...holding,
                                    monitoring_active: monitoringStatus[holding.token_mint] !== undefined
                                        ? monitoringStatus[holding.token_mint]
                                        : holding.monitoring_active
                                };
                                
                                return (
                                    <HoldingCard
                                        key={holding.token_mint}
                                        holding={effectiveStatus}
                                        livePrice={livePrices[holding.token_mint] || null}
                                        onSell={onSell}
                                        isSelling={sellingMint === holding.token_mint}
                                        onPartialSell={onPartialSell}
                                        onBuy={onBuy}
                                        isBuying={buyingMint === holding.token_mint}
                                        disabled={disabled}
                                    />
                                );
                            })
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default HoldingsSection; 