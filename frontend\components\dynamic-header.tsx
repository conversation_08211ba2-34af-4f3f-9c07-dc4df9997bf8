"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useScroll } from '@/hooks';
import { Menu, X } from 'lucide-react';

interface DynamicHeaderProps {
  className?: string;
}

export function DynamicHeader({ className }: DynamicHeaderProps) {
  const { scrollDirection, isAtTop, scrollY } = useScroll();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Determine header state with lower thresholds for better responsiveness
  const isHidden = scrollDirection === 'down' && scrollY > 80 && !isAtTop;
  const isScrolledUp = scrollDirection === 'up' && scrollY > 80 && !isAtTop;
  const isTransparent = isAtTop;

  // Smooth scroll to section function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    // Close mobile menu after navigation
    setIsMobileMenuOpen(false);
  };



  return (
    <header
      className={`
        fixed top-0 left-1/2 transform -translate-x-1/2 z-50
        w-[85%] max-w-3xl
        ${isHidden
          ? '-translate-y-full opacity-0 transition-all duration-700 ease-in'
          : 'translate-y-0 opacity-100 transition-all duration-300 ease-out'
        }
        ${isTransparent
          ? 'bg-transparent border-transparent backdrop-blur-none rounded-full mt-4'
          : isScrolledUp
            ? 'bg-black/60 border border-white/30 backdrop-blur-lg rounded-full mt-4 header-shadow-glow'
            : 'bg-black/20 border-white/10 backdrop-blur-md rounded-full mt-4'
        }
        ${className}
      `}
    >
      <div
        className={`
          flex items-center justify-between transition-all duration-300 ease-out
          ${isTransparent
            ? 'px-6 py-4'
            : isScrolledUp
              ? 'px-6 py-3'
              : 'px-6 py-4'
          }
        `}
      >
        {/* Logo */}
        <div
          className={`
            font-bold font-figtree text-white transition-all duration-300 ease-out tracking-tighter cursor-pointer
            ${isTransparent
              ? 'text-2xl'
              : isScrolledUp
                ? 'text-xl'
                : 'text-2xl'
            }
          `}
          onClick={() => scrollToSection('hero')}
        >
          ScryBot
        </div>

        {/* Desktop Navigation Links */}
        <nav className="hidden md:flex items-center space-x-8">
          <button
            onClick={() => scrollToSection('how-it-works')}
            className={`
              font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight
              ${isScrolledUp ? 'text-sm' : 'text-base'}
            `}
          >
            How It Works
          </button>
          <button
            onClick={() => scrollToSection('features')}
            className={`
              font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight
              ${isScrolledUp ? 'text-sm' : 'text-base'}
            `}
          >
            Features
          </button>
          <button
            onClick={() => scrollToSection('faq')}
            className={`
              font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight
              ${isScrolledUp ? 'text-sm' : 'text-base'}
            `}
          >
            FAQ
          </button>
        </nav>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="md:hidden p-2 text-white/80 hover:text-white transition-colors"
          aria-label="Toggle mobile menu"
        >
          {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>

        {/* Launch App Button - Desktop */}
        <Link href="/dashboard" className="hidden md:block">
          <Button
            variant="default"
            size={isScrolledUp ? "default" : "lg"}
            className={`
              hover:shadow-[0_0_10px_#00d4ff] transition-all duration-300 ease-out
              ${isScrolledUp ? 'text-sm px-4 py-2' : 'text-base px-6 py-3'}
            `}
          >
            Launch App
          </Button>
        </Link>
      </div>

      {/* Mobile Menu Dropdown */}
      {isMobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 mt-2 mx-4 bg-black/90 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
          <nav className="flex flex-col p-4 space-y-4">
            <button
              onClick={() => scrollToSection('how-it-works')}
              className="font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight text-left py-2"
            >
              How It Works
            </button>
            <button
              onClick={() => scrollToSection('features')}
              className="font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight text-left py-2"
            >
              Features
            </button>
            <button
              onClick={() => scrollToSection('faq')}
              className="font-figtree text-white/80 hover:text-white transition-all duration-300 ease-out tracking-tight text-left py-2"
            >
              FAQ
            </button>
            <div className="pt-2 border-t border-white/20">
              <Link href="/dashboard" onClick={() => setIsMobileMenuOpen(false)}>
                <Button
                  variant="default"
                  size="lg"
                  className="w-full hover:shadow-[0_0_10px_#00d4ff] transition-all duration-300 ease-out"
                >
                  Launch App
                </Button>
              </Link>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
