import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold tracking-tighter transition-all duration-200 focus:outline-none",
  {
    variants: {
      variant: {
        default:
          "glass-darker border-border text-foreground backdrop-blur-sm",
        secondary:
          "bg-secondary/10 border-secondary/30 text-secondary-foreground backdrop-blur-sm",
        destructive:
          "bg-destructive/20 border-destructive/30 text-destructive backdrop-blur-sm",
        outline: 
          "border-border text-foreground/80 backdrop-blur-sm",
        success:
          "bg-status-success/10 border-status-success/30 text-status-success backdrop-blur-sm",
        primary:
          "bg-primary/10 border-primary/30 text-primary backdrop-blur-sm",
        accent:
          "bg-accent/10 border-accent/30 text-accent backdrop-blur-sm", 
        warning:
          "bg-status-warning/10 border-status-warning/30 text-status-warning backdrop-blur-sm",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
