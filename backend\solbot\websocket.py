import asyncio
import json
import logging
import websockets
from websockets.exceptions import ConnectionClosed, ConnectionClosedError
import random
import time
from .config import SHYFT_WS_URI_3, SHYFT_WS_URI_4
from app.states import MintState
from .utilities import token_type

logger = logging.getLogger(__name__)

# Create a custom filter to prevent duplicate log messages
class DuplicateFilter(logging.Filter):
    def __init__(self):
        super().__init__()
        self._last_log = {}

    def filter(self, record):
        # Create a key based on module, line number, and message
        log_key = (record.module, record.lineno, record.getMessage())
        current_time = record.created
        
        # Check if we've seen this exact log message recently (within 0.1 seconds)
        if log_key in self._last_log:
            last_time = self._last_log[log_key]
            if current_time - last_time < 0.1:  # Within 100ms
                return False
        
        # Update last seen time
        self._last_log[log_key] = current_time
        
        # Clean up old entries (older than 1 second)
        for key in list(self._last_log.keys()):
            if current_time - self._last_log[key] > 1.0:
                del self._last_log[key]
        
        return True

# Apply the filter to this module's logger
logger.addFilter(DuplicateFilter())

async def web_socket_listener(app):
    uri_index = 0
    endpoints = [SHYFT_WS_URI_3, SHYFT_WS_URI_4]
    total_endpoints = len(endpoints)
    consecutive_failures = 0
    max_consecutive_failures = 3
    base_delay = 0.1
    jitter_range = 1
    max_delay = 1
    
    # Track connection time for metrics
    connection_attempts = 0
    
    # Initialize subscription metrics tracking
    if not hasattr(app.state, 'subscription_metrics'):
        app.state.subscription_metrics = {
            'requests_total': 0,
            'confirmations_total': 0,
            'timeouts_total': 0,
            'latencies': [],  # List of (mint_address, latency) tuples
        }
    
    # Add structure to track pending subscriptions with timeout
    if not hasattr(app.state, 'pending_subscriptions'):
        app.state.pending_subscriptions = {}  # {request_id: (mint_address, timestamp)}
        app.state.subscription_timeout = 10.0  # 10 seconds timeout
    
    # Add structure to track failed unsubscriptions
    if not hasattr(app.state, 'failed_unsubscriptions'):
        app.state.failed_unsubscriptions = {}  # {mint_address: (subscription_id, attempts, last_attempt_time)}
        app.state.unsubscription_retry_interval = 30.0  # seconds between retry attempts
        app.state.max_unsubscription_attempts = 3
    
    while True:
        connection_attempts += 1
        connection_attempt_time = time.time()
        logger.info(f"[TIMESTAMP] Starting connection attempt #{connection_attempts} at {connection_attempt_time}")
        
        current_uri = endpoints[uri_index]
        retries = 0
        max_retries_per_endpoint = 5
        while retries < max_retries_per_endpoint:
            try:
                logger.info(f"Connecting to {current_uri} (Attempt {retries + 1}/{max_retries_per_endpoint})")
                await cleanup_subscription_mappings(app)
                connect_start_time = time.time()
                logger.debug(f"[TIMESTAMP] WebSocket connect start at {connect_start_time}")
                
                async with websockets.connect(current_uri, ping_interval=86400, ping_timeout=43200) as websocket:  # type: ignore[attr-defined]
                    connect_success_time = time.time()
                    connection_time = connect_success_time - connect_start_time
                    logger.info(f"[TIMESTAMP] WebSocket connected at {connect_success_time} (took {connection_time:.3f}s)")
                    consecutive_failures = 0
                    
                    # Create tasks for message handling, subscriptions, unsubscriptions
                    msg_task = asyncio.create_task(handle_messages(websocket, app))
                    sub_task = asyncio.create_task(handle_subscriptions(websocket, app))
                    unsub_task = asyncio.create_task(handle_unsubscriptions(websocket, app))
                    
                    # Add new timeout tracking task
                    timeout_task = asyncio.create_task(check_subscription_timeouts(app))
                    
                    # Add new periodic mapping verification task
                    mapping_verification_task = asyncio.create_task(periodic_mapping_verification(app, websocket))
                    
                    # Wait for any task to complete (which usually means there was an error)
                    done, pending = await asyncio.wait(
                        [msg_task, sub_task, unsub_task, mapping_verification_task, timeout_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # Cancel all other tasks
                    for task in pending:
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                            
                    # Check if any completed task had an exception
                    for task in done:
                        if task.exception():
                            exception = task.exception()
                            if exception is not None:
                                raise exception
            except ConnectionClosed as e:
                disconnect_time = time.time()
                logger.warning(f"[TIMESTAMP] WebSocket closed at {disconnect_time}: {e}")
                retries += 1
            except Exception as e:
                error_time = time.time()
                logger.error(f"[TIMESTAMP] WebSocket error at {error_time}: {e}", exc_info=True)
                retries += 1
                
            if retries < max_retries_per_endpoint:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"Max failures ({max_consecutive_failures}) reached. Waiting {max_delay}s.")
                    await asyncio.sleep(max_delay)
                    consecutive_failures = 0
                else:
                    delay = base_delay + random.uniform(-jitter_range, jitter_range)
                    retry_time = time.time()
                    logger.info(f"[TIMESTAMP] Retry scheduled at {retry_time}. Waiting {delay}s before retrying...")
                    await asyncio.sleep(delay)
                    
        endpoint_switch_time = time.time()
        uri_index = (uri_index + 1) % total_endpoints
        logger.info(f"[TIMESTAMP] Switching endpoint at {endpoint_switch_time} to {endpoints[uri_index]}")

async def check_subscription_timeouts(app):
    """
    Periodically check for subscription requests that have timed out.
    Removes timed-out requests and their associated mint addresses.
    """
    check_interval = 1.0  # Check every 1 second
    
    while True:
        try:
            current_time = time.time()
            timed_out_requests = []
            mint_addresses_to_remove = []
            
            # Identify timed-out subscription requests
            async with app.state.subscriptions_lock:
                for request_id, (mint_address, request_time) in list(app.state.pending_subscriptions.items()):
                    if current_time - request_time > app.state.subscription_timeout:
                        timed_out_requests.append(request_id)
                        mint_addresses_to_remove.append(mint_address)
                        
                        # Track timeout in metrics
                        app.state.subscription_metrics['timeouts_total'] += 1
                        
                        logger.warning(f"[TIMEOUT] Subscription request {request_id} for {mint_address} timed out after {app.state.subscription_timeout}s")
                
                # Clean up timed-out requests
                for request_id in timed_out_requests:
                    app.state.pending_subscriptions.pop(request_id, None)
                    app.state.request_id_to_mint_address.pop(request_id, None)
                    app.state.request_id_to_type.pop(request_id, None)
                
                # Remove timed-out mint addresses from tracking
                for mint_address in mint_addresses_to_remove:
                    app.state.subscribed_addresses.discard(mint_address)
                    
                    # Also update mint state to indicate it's no longer being monitored
                    async with app.state.mint_states_lock:
                        current_state = app.state.mint_states.get(mint_address)
                        # Handle addresses in either PENDING or ACTIVE state
                        if current_state in [MintState.PENDING, MintState.ACTIVE]:
                            app.state.mint_states[mint_address] = MintState.STOPPED
                            # Remove from activation times
                            app.state.mint_activation_times.pop(mint_address, None)
                            logger.info(f"[TIMEOUT] Marked {mint_address} as STOPPED due to subscription timeout (was {current_state})")
            
            # Log periodic statistics on subscription success/failure
            if timed_out_requests and len(timed_out_requests) > 0:
                async with app.state.subscriptions_lock:
                    success_rate = app.state.subscription_metrics['confirmations_total'] / max(1, app.state.subscription_metrics['requests_total']) * 100
                    timeout_rate = app.state.subscription_metrics['timeouts_total'] / max(1, app.state.subscription_metrics['requests_total']) * 100
                    
                    # Calculate average latency if we have data
                    avg_latency = 0
                    if app.state.subscription_metrics['latencies']:
                        latencies = [latency for _, latency in app.state.subscription_metrics['latencies'][-100:]]  # Last 100 samples
                        avg_latency = sum(latencies) / max(1, len(latencies))
                    
                    logger.info(f"[METRICS] Subscription stats: {success_rate:.1f}% success, {timeout_rate:.1f}% timeout, avg latency: {avg_latency:.2f}s")
            
            # Sleep before next check
            await asyncio.sleep(check_interval)
                
        except asyncio.CancelledError:
            logger.info("Subscription timeout checker cancelled")
            break
        except Exception as e:
            logger.error(f"Error in subscription timeout checker: {e}", exc_info=True)
            await asyncio.sleep(check_interval)

async def cleanup_subscription_mappings(app):
    async with app.state.subscriptions_lock:
        active_subscriptions = {mint_addr for _, mint_addr in app.state.subscription_id_to_mint_address.items()}
        app.state.subscription_id_to_mint_address.clear()
        app.state.request_id_to_mint_address.clear()
        app.state.request_id_to_type.clear()
        app.state.subscribed_addresses = active_subscriptions
        
        # Clear pending subscriptions on reconnect
        if hasattr(app.state, 'pending_subscriptions'):
            app.state.pending_subscriptions.clear()
            
        logger.info(f"Cleaned up mappings. Preserved {len(active_subscriptions)} subscriptions")

async def handle_subscriptions(websocket, app):
    subscription_queue = app.state.subscription_queue
    base_delay = 1.2
    max_delay = 1.2
    
    # Add tracking counters
    subscription_requests_sent = 0
    subscription_failures = 0
    
    subscription_handler_start = time.time()
    logger.info(f"[TIMESTAMP] Subscription handler started at {subscription_handler_start}")
    
    # Create a mapping to track when subscription requests were sent
    if not hasattr(app.state, 'subscription_request_times'):
        app.state.subscription_request_times = {}
    
    try:
        # First, clear stale mappings and resubscribe to active addresses
        resubscription_start = time.time()
        logger.info(f"[TIMESTAMP] Starting resubscription process at {resubscription_start}")
        
        async with app.state.subscriptions_lock:
            app.state.subscription_id_to_mint_address.clear()
            app.state.request_id_to_mint_address.clear()
            app.state.request_id_to_type.clear()
            app.state.subscribed_addresses.clear()
            
            # Also clear pending subscriptions
            if hasattr(app.state, 'pending_subscriptions'):
                app.state.pending_subscriptions.clear()
                
            logger.info("[MAPPING] Cleared stale subscription mappings")
            
        async with app.state.mint_states_lock:
            active_addresses = {addr for addr, state in app.state.mint_states.items() if state == MintState.ACTIVE}
            logger.info(f"[SUBSCRIPTION] Found {len(active_addresses)} active addresses to resubscribe")
            
        resubscription_count = 0
        async with app.state.subscriptions_lock:
            for mint_address in active_addresses:
                if mint_address not in app.state.subscribed_addresses:
                    subscription_request_time = time.time()
                    
                    async with app.state.jsonrpc_id_counter_lock:
                        request_id = next(app.state.jsonrpc_id_counter)
                        
                    message = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "method": "logsSubscribe",
                        "params": [{"mentions": [mint_address]}, {"commitment": "confirmed"}]
                    }
                    
                    # Track when this request was sent for latency measurements
                    app.state.subscription_request_times[request_id] = subscription_request_time
                    
                    # Add to pending subscriptions with timestamp
                    app.state.pending_subscriptions[request_id] = (mint_address, subscription_request_time)
                    
                    # Update type mapping (but NOT the subscription_id mapping yet)
                    app.state.request_id_to_type[request_id] = 'subscribe'
                    app.state.request_id_to_mint_address[request_id] = mint_address
                    
                    # Only mark as preliminarily subscribed, proper mapping happens after confirmation
                    app.state.subscribed_addresses.add(mint_address)
                    
                    # Update metrics
                    app.state.subscription_metrics['requests_total'] += 1
                    
                    logger.info(f"[SUBSCRIPTION] Requesting subscription for {mint_address} with request ID {request_id}")
                    logger.debug(f"[MAPPING] Current request mappings: {len(app.state.request_id_to_mint_address)} entries, {len(app.state.request_id_to_type)} types")
                    
                    # Send the subscription request
                    await websocket.send(json.dumps(message))
                    subscription_requests_sent += 1
                    resubscription_count += 1
                    
                    # Brief delay between subscriptions to avoid overwhelming the server
                    if resubscription_count % 10 == 0:
                        await asyncio.sleep(0.1)
        
        resubscription_end = time.time()
        resubscription_duration = resubscription_end - resubscription_start
        logger.info(f"[TIMESTAMP] Resubscription completed at {resubscription_end} (took {resubscription_duration:.3f}s, sent {resubscription_count} requests)")
        
    except Exception as e:
        logger.error(f"[ERROR] Resubscription error: {e}", exc_info=True)
        raise
        
    # Handle new subscription requests from the queue
    while True:
        try:
            mint_address = await subscription_queue.get()
            subscription_request_start = time.time()
            logger.debug(f"[TIMESTAMP] Processing subscription request for {mint_address} at {subscription_request_start}")
            
            # Verify mint is still active
            async with app.state.mint_states_lock:
                current_state = app.state.mint_states.get(mint_address)
                if current_state not in [MintState.PENDING, MintState.ACTIVE]:
                    logger.info(f"[SUBSCRIPTION] {mint_address} not pending or active (state: {current_state}). Skipping subscription.")
                    continue
            
            # Check if already subscribed
            async with app.state.subscriptions_lock:
                if mint_address not in app.state.subscribed_addresses:
                    retries = 0
                    subscribed = False
                    delay = base_delay
                    
                    while not subscribed and retries < 3:
                        try:
                            subscription_attempt_time = time.time()
                            logger.debug(f"[TIMESTAMP] Subscription attempt #{retries+1} for {mint_address} at {subscription_attempt_time}")
                            
                            async with app.state.jsonrpc_id_counter_lock:
                                request_id = next(app.state.jsonrpc_id_counter)
                                
                            message = {
                                "jsonrpc": "2.0",
                                "id": request_id,
                                "method": "logsSubscribe",
                                "params": [{"mentions": [mint_address]}, {"commitment": "confirmed"}]
                            }
                            
                            # Track when this request was sent
                            app.state.subscription_request_times[request_id] = time.time()
                            
                            # Add to pending subscriptions
                            app.state.pending_subscriptions[request_id] = (mint_address, time.time())
                            
                            # Update request mappings
                            app.state.request_id_to_mint_address[request_id] = mint_address
                            app.state.request_id_to_type[request_id] = 'subscribe'
                            
                            # Mark as preliminarily subscribed
                            app.state.subscribed_addresses.add(mint_address)
                            
                            # Update metrics
                            app.state.subscription_metrics['requests_total'] += 1
                            
                            # Send subscription request
                            await websocket.send(json.dumps(message))
                            subscription_requests_sent += 1
                            
                            # Set as subscribed to break retry loop
                            subscribed = True
                            logger.debug(f"[SUBSCRIPTION] Sent subscription request for {mint_address} with ID {request_id}")
                            
                        except ConnectionClosedError as e:
                            logger.error(f"[ERROR] WebSocket closed while subscribing to {mint_address}: {e}")
                            raise
                        except Exception as e:
                            retries += 1
                            subscription_failures += 1
                            logger.error(f"[ERROR] Failed to subscribe to {mint_address}: {e}. Retry {retries}/3 in {delay}s")
                            
                            # Remove from subscribed_addresses since we failed
                            app.state.subscribed_addresses.discard(mint_address)
                            
                            await asyncio.sleep(delay)
                            delay = min(delay * 2, max_delay)
                            
                    if not subscribed:
                        logger.error(f"[SUBSCRIPTION] Failed to subscribe to {mint_address} after 3 attempts.")
                        
                    # Log stats periodically
                    if subscription_requests_sent % 100 == 0:
                        elapsed = time.time() - subscription_handler_start
                        rate = subscription_requests_sent / elapsed if elapsed > 0 else 0
                        logger.info(f"[STATS] Subscription handler: sent {subscription_requests_sent} requests ({rate:.1f}/s), {subscription_failures} failures")
                else:
                    logger.debug(f"[SUBSCRIPTION] {mint_address} already in subscribed_addresses set, skipping")
                
        except asyncio.CancelledError:
            cancel_time = time.time()
            logger.info(f"[TIMESTAMP] Subscription handler cancelled at {cancel_time}")
            break
        except Exception as e:
            error_time = time.time()
            logger.error(f"[TIMESTAMP] Exception in subscription handler at {error_time}: {e}", exc_info=True)
            raise

async def handle_unsubscriptions(websocket, app):
    unsubscribe_queue = app.state.unsubscribe_queue
    while True:
        try:
            mint_address = await unsubscribe_queue.get()
            
            # Check if this is a retry for a previously failed unsubscription
            is_retry = False
            async with app.state.subscriptions_lock:
                if mint_address in app.state.failed_unsubscriptions:
                    subscription_id, attempts, _ = app.state.failed_unsubscriptions[mint_address]
                    is_retry = True
                    logger.info(f"Retrying unsubscription for {mint_address} (attempt {attempts+1}/{app.state.max_unsubscription_attempts})")
                    # Remove from failed unsubscriptions as we're processing it now
                    del app.state.failed_unsubscriptions[mint_address]
                else:
                    # Normal unsubscription flow
                    subscription_id = app.state.subscription_ids.get(mint_address)
                    if subscription_id:
                        app.state.subscribed_addresses.discard(mint_address)
                        app.state.subscription_ids.pop(mint_address, None)
                        app.state.subscription_id_to_mint_address.pop(subscription_id, None)
                    else:
                        logger.debug(f"No subscription ID for {mint_address}")
                        continue
            
            if subscription_id:
                async with app.state.lock:
                    app.state.unsubscription_events[mint_address] = asyncio.Event()
                await unsubscribe(websocket, mint_address, subscription_id, app)
                async with app.state.lock:
                    unsub_event = app.state.unsubscription_events.get(mint_address)
                if unsub_event:
                    try:
                        await asyncio.wait_for(unsub_event.wait(), timeout=10)
                        logger.debug(f"Unsubscription completed for {mint_address}")
                    except asyncio.TimeoutError:
                        # Handle timeout - track this failed unsubscription
                        logger.debug(f"Timeout waiting for unsubscription of {mint_address}")
                        
                        async with app.state.subscriptions_lock:
                            # Check if it was already a retry
                            if is_retry:
                                prev_attempts = attempts + 1
                            else:
                                prev_attempts = 1
                                
                            # Check if we've exceeded max retry attempts
                            if prev_attempts >= app.state.max_unsubscription_attempts:
                                logger.debug(f"Max unsubscription attempts reached for {mint_address}. Adding to problematic subscriptions list.")
                                
                                # Add to problematic subscriptions for monitoring
                                if not hasattr(app.state, 'problematic_subscriptions'):
                                    app.state.problematic_subscriptions = {}
                                
                                # Store the problematic subscription with more info
                                app.state.problematic_subscriptions[subscription_id] = {
                                    'mint_address': mint_address,
                                    'first_failure': time.time(),
                                    'attempts': prev_attempts
                                }
                                
                                # Add to ignore list for message handler to filter these out
                                if not hasattr(app.state, 'ignored_subscription_ids'):
                                    app.state.ignored_subscription_ids = set()
                                app.state.ignored_subscription_ids.add(subscription_id)
                                
                                logger.warning(f"Added subscription ID {subscription_id} for {mint_address} to ignored subscriptions list")
                            else:
                                # Schedule a retry by adding to the failed unsubscriptions
                                app.state.failed_unsubscriptions[mint_address] = (subscription_id, prev_attempts, time.time())
                                # Schedule the retry by adding back to queue after delay
                                asyncio.create_task(schedule_unsubscription_retry(app, mint_address))
                                logger.debug(f"Scheduled unsubscription retry for {mint_address} (attempt {prev_attempts}/{app.state.max_unsubscription_attempts})")
                    finally:
                        async with app.state.lock:
                            app.state.unsubscription_events.pop(mint_address, None)
        except ConnectionClosedError as e:
            logger.error(f"WebSocket closed in handle_unsubscriptions: {e}")
            raise
        except asyncio.CancelledError:
            logger.info("handle_unsubscriptions cancelled")
            break
        except Exception as e:
            logger.error(f"Exception in handle_unsubscriptions: {e}")
            raise

async def schedule_unsubscription_retry(app, mint_address):
    """Schedule a retry for a failed unsubscription after a delay."""
    try:
        await asyncio.sleep(app.state.unsubscription_retry_interval)
        logger.info(f"Retrying unsubscription for {mint_address}")
        await app.state.unsubscribe_queue.put(mint_address)
    except asyncio.CancelledError:
        logger.debug(f"Unsubscription retry for {mint_address} was cancelled")
    except Exception as e:
        logger.error(f"Error scheduling unsubscription retry for {mint_address}: {e}")

async def unsubscribe(websocket, mint_address, subscription_id, app):
    async with app.state.jsonrpc_id_counter_lock:
        request_id = next(app.state.jsonrpc_id_counter)
    message = {
        "jsonrpc": "2.0",
        "id": request_id,
        "method": "logsUnsubscribe",
        "params": [subscription_id]
    }
    try:
        async with app.state.subscriptions_lock:
            app.state.request_id_to_type[request_id] = 'unsubscribe'
            app.state.request_id_to_mint_address[request_id] = mint_address
        await websocket.send(json.dumps(message))
        logger.debug(f"Unsubscribed from {mint_address} with ID {request_id}")
    except ConnectionClosedError as e:
        logger.error(f"WebSocket closed while unsubscribing from {mint_address}: {e}")
        raise
    except Exception as e:
        logger.error(f"Error unsubscribing from {mint_address}: {e}")

async def handle_messages(websocket, app):
    logger.info("Listening for messages...")
    
    # Track statistics for subscription responses
    subscription_response_times = []
    subscription_failures = 0
    
    # Initialize message counters
    message_count = 0
    notification_count = 0
    subscription_confirmations = 0
    unknown_subscription_count = 0
    
    handler_start_time = time.time()
    logger.info(f"[TIMESTAMP] Message handler started at {handler_start_time}")
    
    try:
        async for message in websocket:
            # Increment message counter
            message_count += 1
            
            # Timestamp at WebSocket message receipt
            ws_receipt_time = time.time()
            logger.debug(f"[TIMESTAMP] WebSocket message #{message_count} received at {ws_receipt_time}")
            logger.debug(f"[SHYFT_MESSAGE] Received message: {message}")
            
            try:
                # Timestamp at beginning of message parsing
                parsing_start_time = time.time()
                logger.debug(f"[TIMESTAMP] Message parsing started at {parsing_start_time} (delta: {parsing_start_time - ws_receipt_time:.6f}s)")
                
                data = json.loads(message)
                
                # Log full message structure at INFO level for inspection
                logger.debug(f"[SHYFT_DATA] Message #{message_count} structure: {json.dumps(data, indent=2)}")
                
                # Timestamp after message parsing
                parsing_end_time = time.time()
                logger.debug(f"[TIMESTAMP] Message parsing completed at {parsing_end_time} (delta: {parsing_end_time - parsing_start_time:.6f}s)")
                
                # Handle subscription confirmations
                if 'method' not in data and 'result' in data and 'id' in data:
                    request_id = data['id']
                    subscription_response_time = time.time()
                    
                    async with app.state.subscriptions_lock:
                        request_type = app.state.request_id_to_type.pop(request_id, None)
                        
                        if request_type == 'subscribe':
                            subscription_confirmations += 1
                            subscription_id = data['result']
                            mint_address = app.state.request_id_to_mint_address.pop(request_id, None)
                            
                            # Check if we found the mint address for this subscription
                            if mint_address:
                                # Calculate and log the round-trip time for the subscription
                                request_time = None
                                
                                # Get original request time
                                if request_id in app.state.subscription_request_times:
                                    request_time = app.state.subscription_request_times.pop(request_id)
                                    
                                    # Calculate latency
                                    if request_time:
                                        latency = subscription_response_time - request_time
                                        app.state.subscription_metrics['latencies'].append((mint_address, latency))
                                        logger.debug(f"[LATENCY] Subscription confirmation latency: {latency:.3f}s for {mint_address}")
                                        
                                        # Limit the size of latencies list to avoid unbounded growth
                                        if len(app.state.subscription_metrics['latencies']) > 1000:
                                            app.state.subscription_metrics['latencies'] = app.state.subscription_metrics['latencies'][-1000:]
                                
                                # Remove from pending subscriptions
                                app.state.pending_subscriptions.pop(request_id, None)
                                
                                # Update metrics
                                app.state.subscription_metrics['confirmations_total'] += 1
                                
                                logger.debug(f"[SUBSCRIPTION] Confirmation received: ID {subscription_id} for mint {mint_address} (request ID {request_id})")
                                
                                # NOW update all mapping dictionaries after confirmation
                                app.state.subscription_ids[mint_address] = subscription_id
                                app.state.subscription_id_to_mint_address[subscription_id] = mint_address
                                app.state.subscribed_addresses.add(mint_address)
                                
                                # Update mint state from PENDING to ACTIVE now that subscription is confirmed
                                async with app.state.mint_states_lock:
                                    if app.state.mint_states.get(mint_address) == MintState.PENDING:
                                        # Update the state to ACTIVE
                                        app.state.mint_states[mint_address] = MintState.ACTIVE
                                        # Reset the activation time to now since we're changing from PENDING to ACTIVE
                                        app.state.mint_activation_times[mint_address] = time.time()
                                        
                                        # Initialize price counter as soon as mint becomes ACTIVE
                                        async with app.state.price_counters_lock:
                                            if mint_address not in app.state.price_counters:
                                                app.state.price_counters[mint_address] = 0
                                                logger.debug(f"[PRICE_COUNTER_INIT] Initialized price counter for ACTIVE mint {mint_address}")
                                            # If it somehow exists already (e.g., from a previous session), ensure it's reset or leave as is? Resetting is safer.
                                            # else:
                                            #     app.state.price_counters[mint_address] = 0
                                            #     logger.warning(f"[PRICE_COUNTER_INIT] Reset existing price counter for newly ACTIVE mint {mint_address}")
                                        
                                        logger.debug(f"[SUBSCRIPTION] Updated {mint_address} from PENDING to ACTIVE state")
                                
                                logger.debug(f"[MAPPING] Updated: {len(app.state.subscription_id_to_mint_address)} subscription mappings, {len(app.state.subscription_ids)} mint addresses, {len(app.state.subscribed_addresses)} subscribed addresses")
                            else:
                                logger.warning(f"[SUBSCRIPTION] No mint address for request ID {request_id}")
                                subscription_failures += 1
                        elif request_type == 'unsubscribe':
                            # Handle unsubscription confirmation
                            mint_address = app.state.request_id_to_mint_address.pop(request_id, None)
                            if mint_address:
                                logger.debug(f"[UNSUBSCRIPTION] Confirmation received for {mint_address} (request ID {request_id})")
                                # Set the event to signal completion
                                async with app.state.lock:
                                    unsub_event = app.state.unsubscription_events.get(mint_address)
                                    if unsub_event:
                                        unsub_event.set()
                                    else:
                                        logger.warning(f"No unsubscription event found for {mint_address}")
                            else:
                                logger.warning(f"[UNSUBSCRIPTION] No mint address for request ID {request_id}")
                    continue
                    
                # Handle logs notifications
                if data.get('method') == 'logsNotification':
                    notification_count += 1
                    notification_receipt_time = time.time()
                    
                    # Get mint address early so we can include it in logs
                    params = data.get('params', {})
                    subscription_id = params.get('subscription')
                    mint_address = None
                    
                    # Check if this is from an ignored subscription ID we failed to unsubscribe
                    if hasattr(app.state, 'ignored_subscription_ids') and subscription_id in app.state.ignored_subscription_ids:
                        # Silently drop messages from problematic subscriptions we couldn't unsubscribe
                        continue
                    
                    async with app.state.subscriptions_lock:
                        mint_address = app.state.subscription_id_to_mint_address.get(subscription_id)
                        
                        if not mint_address:
                            unknown_subscription_count += 1
                            logger.debug(f"[MAPPING] No mint address for subscription ID {subscription_id} (notification #{notification_count})")
                            
                            # Skip unknown subscriptions instead of trying to recover - safer for trading data integrity
                            logger.debug(f"[SECURITY] Skipping notification for unknown subscription ID {subscription_id} - recovery disabled for data integrity")
                            continue
                    
                    # Log detailed structure of the logsNotification at INFO level
                    logger.debug(f"[SHYFT_NOTIFICATION] Mint: {mint_address}, Notification structure: {json.dumps(params, indent=2)}")
                    
                    # Track transaction count per mint address to diagnose delays
                    if not hasattr(app.state, 'tx_counts_since_active'):
                        app.state.tx_counts_since_active = {}
                    
                    # Get or initialize the counter
                    if mint_address not in app.state.tx_counts_since_active:
                        app.state.tx_counts_since_active[mint_address] = 1
                        # Log when we see the first transaction for this mint
                        activation_time = app.state.mint_activation_times.get(mint_address, 0)
                        current_time = time.time()
                        time_since_activation = current_time - activation_time if activation_time > 0 else -1
                        logger.debug(f"[TX_RECEIPT] First transaction received for {mint_address} ({time_since_activation:.2f}s after activation)")
                    else:
                        app.state.tx_counts_since_active[mint_address] += 1
                        # Log every 5th transaction receipt
                        if app.state.tx_counts_since_active[mint_address] % 5 == 0:
                            logger.debug(f"[TX_RECEIPT] Received {app.state.tx_counts_since_active[mint_address]} transactions for {mint_address}")
                    
                    # Timestamp at beginning of logs notification processing
                    logs_processing_start_time = time.time()
                    logger.debug(f"[TIMESTAMP] Notification #{notification_count} processing started at {logs_processing_start_time} for mint {mint_address} (delta: {logs_processing_start_time - notification_receipt_time:.6f}s)")
                    
                    result = params.get('result', {})
                    value = result.get('value', {})
                    signature = value.get('signature')
                    
                    # Log detailed value structure as this is likely where differences between Helius and Shyft exist
                    logger.debug(f"[SHYFT_VALUE] Mint: {mint_address}, Value structure: {json.dumps(value, indent=2)}")
                    
                    # Record blockchain timestamp from transaction data if available
                    if 'blockTime' in value:
                        blockchain_time = value.get('blockTime')
                        current_time = time.time()
                        time_diff = current_time - blockchain_time
                        logger.debug(f"[LATENCY] Transaction to receipt: {time_diff:.2f}s for mint {mint_address} (tx timestamp: {blockchain_time})")
                    
                    async with app.state.mint_states_lock:
                        current_state = app.state.mint_states.get(mint_address)
                        if current_state != MintState.ACTIVE:
                            logger.debug(f"{mint_address} not active. Ignoring.")
                            continue
                    
                    # Check if the accumulator is already finalized before processing new signatures
                    is_finalized = False
                    async with app.state.lock:
                        if mint_address in app.state.accumulators:
                            is_finalized = app.state.accumulators[mint_address].get('finalized', False)
                    
                    if is_finalized:
                        logger.debug(f"Ignoring signature for {mint_address} as accumulator is already finalized.")
                        continue
                    
                    # Extract price from transaction for immediate detection
                    if signature:
                        # Timestamp before queue addition
                        queue_add_time = time.time()
                        logger.debug(f"[TIMESTAMP] Adding signature to queue at {queue_add_time} for mint {mint_address} (delta: {queue_add_time - logs_processing_start_time:.6f}s)")
                        
                        # First, add to normal queue for regular processing
                        # Line that adds signatures to the old queue was removed
                        logger.debug(f"Received signature: {signature} for {mint_address}")
                        
                        # Timestamp before price extraction
                        price_extraction_start_time = time.time()
                        logger.debug(f"[TIMESTAMP] Starting price extraction at {price_extraction_start_time} for mint {mint_address} (delta: {price_extraction_start_time - queue_add_time:.6f}s)")
                        
                        # Now do direct price extraction to check for 100th price
                        async with app.state.pending_realtime_signatures_lock:
                            if mint_address not in app.state.pending_realtime_signatures:
                                app.state.pending_realtime_signatures[mint_address] = []
                            app.state.pending_realtime_signatures[mint_address].append(signature)
                            logger.debug(f"Added signature {signature[:10]}... to pending list for {mint_address}. List size: {len(app.state.pending_realtime_signatures[mint_address])}")
                        
                        # Timestamp after price extraction
                        price_extraction_end_time = time.time()
                        logger.debug(f"[TIMESTAMP] Completed price extraction at {price_extraction_end_time} for mint {mint_address} (delta: {price_extraction_end_time - price_extraction_start_time:.6f}s)")
                        
                        # Total time from WebSocket receipt to completion
                        total_processing_time = price_extraction_end_time - ws_receipt_time
                        logger.debug(f"[TIMESTAMP] Total processing time: {total_processing_time:.6f}s for mint {mint_address}")
                        
                        # Log detailed stats periodically
                        if notification_count % 1000 == 0:
                            elapsed_time = time.time() - handler_start_time
                            msgs_per_sec = message_count / elapsed_time if elapsed_time > 0 else 0
                            notifications_per_sec = notification_count / elapsed_time if elapsed_time > 0 else 0
                            
                            logger.info(f"[STATS] Processed {message_count} messages ({msgs_per_sec:.1f}/s), "
                                      f"{notification_count} notifications ({notifications_per_sec:.1f}/s), "
                                      f"{subscription_confirmations} subscription confirms, "
                                      f"{unknown_subscription_count} unknown subscriptions")
                    else:
                        logger.warning("logsNotification without signature.")
                    
                    # Timestamp at end of logs notification processing
                    logs_processing_end_time = time.time()
                    logger.debug(f"[TIMESTAMP] Logs notification processing completed at {logs_processing_end_time} for mint {mint_address} (delta: {logs_processing_end_time - logs_processing_start_time:.6f}s)")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse message: {e}")
                continue
            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)
                continue
    except ConnectionClosed as e:
        connection_closed_time = time.time()
        logger.warning(f"[TIMESTAMP] WebSocket closed in message handler at {connection_closed_time}: {e}")
        raise
    except Exception as e:
        error_time = time.time()
        logger.error(f"[TIMESTAMP] Unexpected error in message handler at {error_time}: {e}", exc_info=True)
        raise

async def ensure_completion_processor_running(app):
    """Ensure the high-priority completion processor is running."""
    if not hasattr(app.state, 'completion_processor_running'):
        app.state.completion_processor_running = False
        
    if not app.state.completion_processor_running:
        app.state.completion_processor_running = True
        asyncio.create_task(process_completion_queue(app))
        logger.debug("Started high-priority completion processor")

async def process_completion_queue(app):
    """
    Process the high-priority completion queue.
    Items in this queue have already hit 100 prices and should be
    completed immediately with highest priority.
    """
    try:
        logger.debug("High-priority completion processor started")
        while True:
            try:
                # Get the next mint address that has reached 100 prices
                priority, mint_address, signature = await app.state.completion_queue.get()
                detection_time = app.state.detection_times.get(mint_address, 0)
                current_time = time.time()
                elapsed = current_time - detection_time if detection_time else 0
                
                logger.debug(f"Completion Queue: Processing high-priority completion for {mint_address} (detected {elapsed:.2f}s ago)")
                
                # Check if this mint address is already being processed or finalized
                skip = False
                async with app.state.lock:
                    if mint_address in app.state.accumulators:
                        if app.state.accumulators[mint_address].get('finalized', False):
                            logger.debug(f"Skipping {mint_address} as it's already finalized")
                            skip = True
                
                if not skip:
                    # Trigger the completion process directly
                    from .processing import complete_data_collection
                    
                    # Initialize accumulator if needed
                    async with app.state.mint_lock_manager.acquire(mint_address):
                        from .accumulator import initialize_accumulator
                        if mint_address not in app.state.accumulators:
                            app.state.accumulators[mint_address] = initialize_accumulator()
                            logger.debug(f"Initialized accumulator for {mint_address} in high-priority completion")
                    
                    # Directly trigger completion for this mint address
                    completion_start = time.time()
                    logger.debug(f"Starting high-priority completion for {mint_address}")
                    
                    # Get the accumulator and pass it to complete_data_collection
                    accumulator = app.state.accumulators.get(mint_address)
                    if accumulator:
                        await complete_data_collection(mint_address, accumulator, app)
                    else:
                        logger.error(f"Accumulator for {mint_address} missing when processing completion queue.")
                    
                    completion_end = time.time()
                    
                    # Log metrics
                    detection_to_completion = completion_start - detection_time if detection_time else 0
                    completion_duration = completion_end - completion_start
                    logger.debug(f"High-priority completion for {mint_address} completed:"
                              f" Detection to start: {detection_to_completion:.2f}s,"
                              f" Completion duration: {completion_duration:.2f}s")
                
                    # Ensure proper cleanup after completion by calling skip_mint_address
                    from .monitoring import skip_mint_address
                    logger.debug(f"Cleaning up mint {mint_address} after successful completion")
                    await skip_mint_address(mint_address, app)
                
                # Mark task as done
                app.state.completion_queue.task_done()
                
            except asyncio.CancelledError:
                logger.error("High-priority completion processor cancelled")
                break
            except Exception as e:
                logger.error(f"Error processing high-priority completion: {e}", exc_info=True)
                # Brief pause to avoid tight error loops
                await asyncio.sleep(0.1)
                
    except asyncio.CancelledError:
        logger.error("High-priority completion processor shutting down")
    except Exception as e:
        logger.error(f"Fatal error in high-priority completion processor: {e}", exc_info=True)
    finally:
        app.state.completion_processor_running = False

# Add these new functions for mapping verification and recovery
async def verify_subscription_mappings(app):
    """
    Verify the consistency of subscription mappings and repair any inconsistencies.
    Returns a tuple of (verified_count, repaired_count).
    """
    verification_start = time.time()
    logger.debug(f"[TIMESTAMP] Starting subscription mapping verification at {verification_start}")
    
    verified_count = 0
    repaired_count = 0
    removed_count = 0
    
    # Track which mint addresses were repaired in this verification cycle
    repaired_addresses = set()
    
    # First, get all active and pending mint addresses
    valid_mint_addresses = set()
    async with app.state.mint_states_lock:
        for mint_address, state in app.state.mint_states.items():
            if state in [MintState.ACTIVE, MintState.PENDING]:
                valid_mint_addresses.add(mint_address)
    
    async with app.state.subscriptions_lock:
        # Log current mapping stats
        sub_id_to_mint = len(app.state.subscription_id_to_mint_address)
        mint_to_sub_id = len(app.state.subscription_ids)
        subscribed_addrs = len(app.state.subscribed_addresses)
        logger.debug(f"[MAPPING] Verifying mappings: {sub_id_to_mint} sub -> mint, {mint_to_sub_id} mint -> sub, {subscribed_addrs} addresses")
        
        # Check for addresses with ID but missing from reverse mapping
        for mint_address, subscription_id in list(app.state.subscription_ids.items()):
            verified_count += 1
            
            # Check if the mint address is valid (ACTIVE or PENDING)
            if mint_address not in valid_mint_addresses:
                # Mint is not active or pending - remove it from all mappings
                removed_count += 1
                logger.debug(f"[MAPPING] Removing non-active/non-pending mint {mint_address} from subscription mappings")
                app.state.subscription_ids.pop(mint_address, None)
                app.state.subscription_id_to_mint_address.pop(subscription_id, None)
                app.state.subscribed_addresses.discard(mint_address)
                continue
                
            # Only repair active or pending mint addresses
            mapped_mint = app.state.subscription_id_to_mint_address.get(subscription_id)
            
            # Verify bidirectional integrity of the mapping
            if mapped_mint != mint_address:
                repaired_count += 1
                repaired_addresses.add(mint_address)
                if mapped_mint is None:
                    logger.warning(f"[MAPPING] Repairing: Subscription ID {subscription_id} for {mint_address} missing from id→mint mapping")
                else:
                    logger.warning(f"[MAPPING] Repairing: Subscription ID {subscription_id} maps to {mapped_mint} but should map to {mint_address}")
                app.state.subscription_id_to_mint_address[subscription_id] = mint_address
            
            # Ensure address is in subscribed_addresses set
            if mint_address not in app.state.subscribed_addresses:
                repaired_count += 1
                repaired_addresses.add(mint_address)
                logger.warning(f"[MAPPING] Repairing: {mint_address} missing from subscribed_addresses set")
                app.state.subscribed_addresses.add(mint_address)
                
        # Check reverse consistency
        for subscription_id, mint_address in list(app.state.subscription_id_to_mint_address.items()):
            verified_count += 1
            
            # Check if the mint address is valid (ACTIVE or PENDING)
            if mint_address not in valid_mint_addresses:
                # Mint is not active or pending - remove it from all mappings
                removed_count += 1
                logger.info(f"[MAPPING] Removing non-active/non-pending mint {mint_address} from subscription mappings")
                app.state.subscription_id_to_mint_address.pop(subscription_id, None)
                app.state.subscription_ids.pop(mint_address, None)
                app.state.subscribed_addresses.discard(mint_address)
                continue
                
            # Only repair active or pending mint addresses
            if mint_address not in app.state.subscription_ids or app.state.subscription_ids[mint_address] != subscription_id:
                repaired_count += 1
                repaired_addresses.add(mint_address)
                logger.warning(f"[MAPPING] Repairing: {mint_address} has inconsistent mapping in subscription_ids")
                app.state.subscription_ids[mint_address] = subscription_id
                
            # Ensure address is in subscribed_addresses set
            if mint_address not in app.state.subscribed_addresses:
                repaired_count += 1
                repaired_addresses.add(mint_address)
                logger.warning(f"[MAPPING] Repairing: {mint_address} missing from subscribed_addresses set")
                app.state.subscribed_addresses.add(mint_address)
    
    # Check which repaired addresses have 100 prices
    completed_repaired_addresses = []
    
    async with app.state.price_counters_lock:
        for mint_address in repaired_addresses:
            price_count = app.state.price_counters.get(mint_address, 0)
            if price_count >= 100:
                completed_repaired_addresses.append(mint_address)
    
    # Log addresses that were both repaired and have 100+ prices
    if completed_repaired_addresses:
        addresses_str = ', '.join(completed_repaired_addresses)
        logger.warning(f"[DATA_INTEGRITY] The following mint addresses were repaired and have 100+ prices (review for data integrity): {addresses_str}")
        
        # Create or update a record of these addresses for manual review
        if not hasattr(app.state, 'repaired_completed_mints'):
            app.state.repaired_completed_mints = set()
        
        # Add addresses to the tracking set
        app.state.repaired_completed_mints.update(completed_repaired_addresses)
    
    verification_end = time.time()
    logger.debug(f"[TIMESTAMP] Mapping verification completed at {verification_end} (took {verification_end - verification_start:.3f}s)")
    logger.debug(f"[MAPPING] Verification complete: {verified_count} mappings checked, {repaired_count} repairs made, {removed_count} invalid mappings removed")
    
    return verified_count, repaired_count

async def dump_repaired_completed_mints(app, include_detailed_info=False):
    """
    Dumps the list of repaired mint addresses that have collected 100+ prices to a separate log file.
    This function creates a log file for manual review of potentially problematic addresses.
    
    Args:
        app: The FastAPI app instance
        include_detailed_info: Whether to include additional detailed information about each mint
    """
    if not hasattr(app.state, 'repaired_completed_mints') or not app.state.repaired_completed_mints:
        logger.info("[DATA_INTEGRITY] No repaired completed mints to dump")
        return None
    
    try:
        import os
        from datetime import datetime
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file_path = os.path.join(log_dir, f"repaired_completed_mints_{current_time}.log")
        
        # Get all addresses that have been repaired and reached 100+ prices
        repaired_mints = list(app.state.repaired_completed_mints)
        repaired_mints.sort()  # Sort for easier reading
        
        # Additional data for each address
        mint_data = []
        
        # Collect price counter data
        async with app.state.price_counters_lock:
            for mint_address in repaired_mints:
                price_count = app.state.price_counters.get(mint_address, 0)
                
                # Base data
                mint_info = {
                    "mint_address": mint_address,
                    "price_count": price_count,
                }
                
                # Add detailed info if requested
                if include_detailed_info:
                    # Get current subscription state
                    async with app.state.subscriptions_lock:
                        subscription_id = app.state.subscription_ids.get(mint_address, "None")
                        in_subscribed_set = mint_address in app.state.subscribed_addresses
                        reverse_mapping_correct = False
                        if subscription_id != "None":
                            mapped_mint = app.state.subscription_id_to_mint_address.get(subscription_id)
                            reverse_mapping_correct = (mapped_mint == mint_address)
                    
                    # Get mint state
                    async with app.state.mint_states_lock:
                        mint_state = app.state.mint_states.get(mint_address, "Unknown")
                        activation_time = app.state.mint_activation_times.get(mint_address, 0)
                        time_since_activation = time.time() - activation_time if activation_time > 0 else -1
                    
                    # Add to mint info
                    mint_info.update({
                        "subscription_id": subscription_id,
                        "in_subscribed_set": in_subscribed_set,
                        "reverse_mapping_correct": reverse_mapping_correct,
                        "mint_state": str(mint_state),
                        "activation_time": activation_time,
                        "time_since_activation": f"{time_since_activation:.2f}s" if time_since_activation > 0 else "Unknown"
                    })
                
                mint_data.append(mint_info)
        
        # Write to log file
        with open(log_file_path, 'w') as f:
            f.write(f"REPAIRED COMPLETED MINTS LOG - {current_time}\n")
            f.write(f"Total addresses: {len(repaired_mints)}\n")
            f.write("-" * 80 + "\n")
            
            for data in mint_data:
                if include_detailed_info:
                    f.write(f"Mint: {data['mint_address']}\n")
                    f.write(f"  Prices: {data['price_count']}\n")
                    f.write(f"  Subscription ID: {data['subscription_id']}\n")
                    f.write(f"  In subscribed_addresses set: {data['in_subscribed_set']}\n")
                    f.write(f"  Reverse mapping correct: {data['reverse_mapping_correct']}\n")
                    f.write(f"  Mint state: {data['mint_state']}\n")
                    f.write(f"  Activation time: {data['activation_time']}\n")
                    f.write(f"  Time since activation: {data['time_since_activation']}\n")
                    f.write("-" * 40 + "\n")
                else:
                    f.write(f"Mint: {data['mint_address']} - Prices: {data['price_count']}\n")
        
        logger.info(f"[DATA_INTEGRITY] Dumped {len(repaired_mints)} repaired completed mints to {log_file_path}")
        
        # Also log a summary to the regular log
        if len(repaired_mints) > 0:
            logger.warning(f"[DATA_INTEGRITY] {len(repaired_mints)} mint addresses were repaired and have 100+ prices. Check {log_file_path} for details.")
        
        return log_file_path
    
    except Exception as e:
        logger.error(f"Error dumping repaired completed mints: {e}", exc_info=True)
        return None

# This function can be accessed via an API endpoint for manual triggers
async def manually_dump_repaired_mints(app):
    """
    API-accessible function to manually trigger a detailed dump of repaired mint addresses.
    Returns the path to the generated log file or None if there was an error.
    """
    logger.info("[DATA_INTEGRITY] Manually triggered dump of repaired completed mints")
    return await dump_repaired_completed_mints(app, include_detailed_info=True)

async def purge_old_problematic_subscriptions(app, age_threshold_seconds=3600):
    """
    Purge problematic subscriptions that haven't been successfully unsubscribed 
    after a very long time. This prevents indefinite tracking of old subscriptions.
    
    Args:
        app: The FastAPI app instance
        age_threshold_seconds: Number of seconds after which to purge problematic subscriptions
    """
    try:
        if not hasattr(app.state, 'problematic_subscriptions'):
            return
        
        purge_count = 0
        current_time = time.time()
        purge_threshold = current_time - age_threshold_seconds
        
        async with app.state.subscriptions_lock:
            for sub_id, info in list(app.state.problematic_subscriptions.items()):
                if info['first_failure'] < purge_threshold:
                    # Log that we're purging this problematic subscription
                    logger.warning(f"Purging very old problematic subscription: ID {sub_id} for {info['mint_address']} (age: {age_threshold_seconds}+ seconds)")
                    
                    # Remove from tracking structures
                    app.state.problematic_subscriptions.pop(sub_id, None)
                    if hasattr(app.state, 'ignored_subscription_ids'):
                        app.state.ignored_subscription_ids.discard(sub_id)
                    
                    purge_count += 1
        
        if purge_count > 0:
            logger.info(f"Purged {purge_count} problematic subscriptions older than {age_threshold_seconds} seconds")
    
    except Exception as e:
        logger.error(f"Error purging old problematic subscriptions: {e}", exc_info=True)

async def check_failed_unsubscriptions(app):
    """Check for and retry any failed unsubscriptions that have been waiting past the retry interval."""
    try:
        current_time = time.time()
        mint_addresses_to_retry = []
        problematic_count = 0
        
        # Find any failed unsubscriptions that are due for retry
        async with app.state.subscriptions_lock:
            if hasattr(app.state, 'failed_unsubscriptions'):
                for mint_address, (_, _, last_attempt_time) in list(app.state.failed_unsubscriptions.items()):
                    if current_time - last_attempt_time > app.state.unsubscription_retry_interval:
                        mint_addresses_to_retry.append(mint_address)
            
            # Count problematic subscriptions
            if hasattr(app.state, 'problematic_subscriptions'):
                problematic_count = len(app.state.problematic_subscriptions)
                
                # Log problematic subscription details periodically
                if problematic_count > 0:
                    # Log IDs that have been problematic for over 24 hours
                    long_standing_issues = []
                    current_time = time.time()
                    ten_minutes_ago = current_time - (10 * 60)
                    
                    for sub_id, info in list(app.state.problematic_subscriptions.items()):
                        if info['first_failure'] < ten_minutes_ago:
                            long_standing_issues.append(f"{sub_id} ({info['mint_address']})")
                    
                    if long_standing_issues:
                        logger.warning(f"Long-standing problematic subscriptions (>10min): {', '.join(long_standing_issues)}")
        
        # Queue them for retry
        for mint_address in mint_addresses_to_retry:
            logger.info(f"Queuing retry for stale failed unsubscription: {mint_address}")
            await app.state.unsubscribe_queue.put(mint_address)
            
        # Log stats if we have problematic subscriptions
        if problematic_count > 0:
            logger.warning(f"Currently tracking {problematic_count} problematic subscriptions that failed to unsubscribe")
    
    except Exception as e:
        logger.error(f"Error checking failed unsubscriptions: {e}", exc_info=True)

async def periodic_mapping_verification(app, websocket):
    """
    Periodically verify and repair subscription mappings.
    This helps ensure we don't miss transactions due to mapping inconsistencies.
    Also checks for and retries failed unsubscriptions that might have been missed.
    """
    try:
        # Initial delay before first check
        await asyncio.sleep(10)
        
        verification_interval = 30  # Check every 30 seconds
        dump_interval_count = 20    # Dump repaired mints log every ~10 minutes (20 * 30s)
        purge_interval_count = 2880 # Purge old problematic subscriptions every day (2880 * 30s = 24h)
        count = 0
        purge_count = 0
        
        while True:
            try:
                verified, repaired = await verify_subscription_mappings(app)
                
                # Check for stale failed unsubscriptions that need retry
                await check_failed_unsubscriptions(app)
                
                # Periodically dump the list of repaired completed mints
                count += 1
                purge_count += 1
                
                if count >= dump_interval_count:
                    await dump_repaired_completed_mints(app)
                    count = 0
                
                # Periodically purge very old problematic subscriptions
                if purge_count >= purge_interval_count:
                    await purge_old_problematic_subscriptions(app)
                    purge_count = 0
                
                # Wait for next verification cycle
                await asyncio.sleep(verification_interval)
                
            except Exception as e:
                logger.error(f"Error in mapping verification: {e}", exc_info=True)
                await asyncio.sleep(5)  # Shorter delay if there was an error
    except asyncio.CancelledError:
        logger.info("Periodic mapping verification cancelled")
    except Exception as e:
        logger.error(f"Fatal error in periodic mapping verification: {e}", exc_info=True)
        raise