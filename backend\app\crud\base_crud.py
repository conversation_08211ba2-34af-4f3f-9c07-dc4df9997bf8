from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import TypeVar, Generic, Type, Optional, List, Dict, Any

# Type variable for SQLAlchemy model
T = TypeVar('T')

class BaseCRUD(Generic[T]):
    """
    Base CRUD class with common operations for all models.
    """
    
    def __init__(self, model: Type[T]):
        """
        Initialize the CRUD operations with the SQLAlchemy model.
        
        Args:
            model: The SQLAlchemy model class
        """
        self.model = model
    
    async def get(self, db: AsyncSession, id_value: Any) -> Optional[T]:
        """
        Get a single record by its primary key.
        
        Args:
            db: AsyncSession for database operations
            id_value: Value of the primary key
            
        Returns:
            The model instance or None if not found
        """
        query = select(self.model).filter(self.model.id == id_value)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_all(self, db: AsyncSession) -> List[T]:
        """
        Get all records of the model.
        
        Args:
            db: AsyncSession for database operations
            
        Returns:
            List of all model instances
        """
        query = select(self.model)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def create(self, db: AsyncSession, obj_data: Dict[str, Any]) -> T:
        """
        Create a new record.
        
        Args:
            db: AsyncSession for database operations
            obj_data: Dictionary with model field values
            
        Returns:
            The created model instance
        """
        obj = self.model(**obj_data)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    
    async def update(self, db: AsyncSession, db_obj: T, obj_data: Dict[str, Any]) -> T:
        """
        Update an existing record.
        
        Args:
            db: AsyncSession for database operations
            db_obj: The existing model instance to update
            obj_data: Dictionary with updated field values
            
        Returns:
            The updated model instance
        """
        for key, value in obj_data.items():
            if hasattr(db_obj, key):
                setattr(db_obj, key, value)
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def delete(self, db: AsyncSession, id_value: Any) -> bool:
        """
        Delete a record by its primary key.
        
        Args:
            db: AsyncSession for database operations
            id_value: Value of the primary key
            
        Returns:
            True if deleted, False if not found
        """
        obj = await self.get(db, id_value)
        if obj:
            await db.delete(obj)
            await db.commit()
            return True
        return False 