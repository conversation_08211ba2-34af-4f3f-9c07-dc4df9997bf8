Okay, here is the detailed, step-by-step implementation plan, formatted so you can copy and paste each step's instructions directly into Cursor or a similar AI assistant.

---

**Document: Detailed Backend Implementation Plan for MVP**

**Overall Goal Summary:**

This plan outlines the backend modifications required to transform a single-instance Solana trading bot into a multi-user web application. Users will connect via Phantom Wallet (non-custodially), configure bot parameters (buy amount, TP/SL, optional filters), start/stop their bot instance, and monitor holdings/trades via a dashboard. The backend will manage user state, propose transactions for user signing via the Wallet Adapter, and fetch live prices for TP/SL execution and P&L display. **Security Note:** User private keys will NOT be handled by the backend.

*(Keep this full document for your reference. Provide the AI with the 'Instructions for AI' section for each step, one step at a time, after giving it the initial context below if needed.)*

**Initial Context Prompt for AI (Optional):**

> "I need your help modifying an existing Python FastAPI backend for a Solana trading bot. The goal is to adapt it into a multi-user system supporting a web application, allowing users to connect via Phantom Wallet, configure their bot instance, start/stop it, and monitor results non-custodially using the Wallet Adapter model for transaction signing. We will implement this incrementally following specific steps. Let's start with Phase 1, Step 1.1."

---

**Phase 1: Foundation & Basic Data Management**

*(Phase Goal: Establish database connection, core data models, and basic API endpoints for authentication and configuration.)*

---

**Step 1.1: Database Setup & Core Models**

*   **Goal:** Set up the PostgreSQL database connection using SQLAlchemy's async capabilities and define the essential `Users` and `Configurations` data models.
*   **Instructions for AI:**
    ```python
    # Goal: Set up PostgreSQL DB connection and define Users/Configurations models.

    # 1. Ensure necessary libraries are installed:
    # pip install sqlalchemy[asyncio] asyncpg psycopg2-binary python-dotenv alembic

    # 2. Create/update environment file (.env) with database credentials:
    # DATABASE_URL=postgresql+asyncpg://user:password@host:port/dbname

    # 3. Create `app/database.py`:
    #    - Import necessary SQLAlchemy async components (create_async_engine, async_sessionmaker, AsyncSession).
    #    - Import `os` and `load_dotenv`. Load environment variables.
    #    - Get DATABASE_URL from environment variables.
    #    - Create an `async_engine` using `create_async_engine`.
    #    - Create an `async_session_factory` using `async_sessionmaker`.
    #    - Define an async dependency function `get_db_session()` that yields an `AsyncSession` from the factory.

    # 4. Create `app/models.py` (or modify existing):
    #    - Import SQLAlchemy components (declarative_base, Column, Integer, String, Float, DateTime, ForeignKey, Enum as PyEnum).
    #    - Import `datetime` from `datetime`.
    #    - Define `Base = declarative_base()`.
    #    - Define SQLAlchemy model `User`:
    #        - `__tablename__ = 'users'`
    #        - `wallet_address = Column(String, primary_key=True, index=True)`
    #        - `created_at = Column(DateTime, default=datetime.utcnow)`
    #        - Define relationship to Configurations: `configurations = relationship("Configuration", back_populates="owner")`
    #    - Define SQLAlchemy model `Configuration`:
    #        - `__tablename__ = 'configurations'`
    #        - `id = Column(Integer, primary_key=True, index=True)`
    #        - `user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)`
    #        - `max_buy_sol = Column(Float, default=0.01)`
    #        - `tp_percent = Column(Float, default=100.0)` # e.g., 100.0 means 100%
    #        - `sl_percent = Column(Float, default=50.0)` # e.g., 50.0 means 50%
    #        - `min_unique_wallets = Column(Integer, nullable=True)`
    #        - `min_total_volume = Column(Float, nullable=True)`
    #        - `max_holder1_percent = Column(Float, nullable=True)`
    #        - `updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)`
    #        - Define relationship to User: `owner = relationship("User", back_populates="configurations")`

    # 5. Create `app/crud/base_crud.py` (optional base class) and `app/crud/user_crud.py`, `app/crud/config_crud.py`:
    #    - In `user_crud.py`: Define async function `get_or_create_user(db: AsyncSession, wallet_address: str)`:
    #        - Query User by wallet_address.
    #        - If exists, return user.
    #        - If not exists, create new User instance, add, commit, refresh, and return. Handle potential race conditions if necessary (e.g., try/except IntegrityError).
    #    - In `config_crud.py`: Define async functions:
    #        - `get_configuration_by_user(db: AsyncSession, user_wallet: str)`: Query Configuration filtered by user_wallet. Return the first result or None.
    #        - `update_or_create_configuration(db: AsyncSession, user_wallet: str, config_data: dict)`:
    #            - Call `get_configuration_by_user`.
    #            - If exists, update its fields from `config_data`.
    #            - If not exists, create new Configuration instance (ensure `user_wallet` is set).
    #            - Add, commit, refresh, and return the configuration object.

    # 6. Modify `app/__init__.py` (or wherever FastAPI app is created):
    #    - Import `Base` from `app.models` and `async_engine` from `app.database`.
    #    - Define an async function `create_db_and_tables()` to run `Base.metadata.create_all(bind=async_engine)`.
    #    - Call `create_db_and_tables()` during application startup (e.g., within the `lifespan` context manager or a startup event handler). Ensure it's awaited correctly.
    #    - Ensure the `get_db_session` dependency is available for use in routers.
    ```
*   **Files Involved:** `.env` (new/modified), `app/database.py` (new), `app/models.py` (new/modified), `app/crud/user_crud.py` (new), `app/crud/config_crud.py` (new), `app/__init__.py` (modified).
*   **Testing:**
    1.  Set up PostgreSQL DB and update `.env`.
    2.  Run the FastAPI application. Check logs for successful DB connection and table creation messages. Verify tables `users` and `configurations` exist in your database.
    3.  *(Optional but Recommended)* Create a separate `test_crud.py` script. Import necessary components (`get_db_session`, CRUD functions, models). Write async test functions using `pytest-asyncio` or similar to call `get_or_create_user`, `get_configuration_by_user`, `update_or_create_configuration`. Assert that data is correctly written/retrieved from the database.

---

**Step 1.2: Basic Authentication Endpoint & Session Stub**

*   **Goal:** Create the initial API endpoint for wallet verification and establish a placeholder for future session management.
*   **Instructions for AI:**
    ```python
    # Goal: Implement basic /api/auth/verify endpoint.

    # 1. Create `app/schemas/auth_schemas.py`:
    #    - Define Pydantic model `AuthVerifyPayload`:
    #        - `walletAddress: str`
    #        - `signedMessage: str | None = None` # Placeholder for now
    #    - Define Pydantic model `AuthVerifyResponse`:
    #        - `status: str`
    #        - `walletAddress: str`

    # 2. Create `app/routers/auth_router.py`:
    #    - Import `APIRouter`, `Depends`, `HTTPException`.
    #    - Import `AsyncSession` from `sqlalchemy.ext.asyncio`.
    #    - Import `get_db_session` from `app.database`.
    #    - Import `get_or_create_user` from `app.crud.user_crud`.
    #    - Import `AuthVerifyPayload`, `AuthVerifyResponse` from `app.schemas.auth_schemas`.
    #    - Create an `APIRouter` instance: `router = APIRouter(prefix="/api/auth", tags=["auth"])`.
    #    - Define endpoint `POST /verify`:
    #        - Use `@router.post("/verify", response_model=AuthVerifyResponse)` decorator.
    #        - Function signature: `async def verify_wallet(payload: AuthVerifyPayload, db: AsyncSession = Depends(get_db_session))`.
    #        - **Ignore `payload.signedMessage` validation for now.**
    #        - Call `user = await get_or_create_user(db, payload.walletAddress)`. Handle potential errors if needed.
    #        - Return `AuthVerifyResponse(status='verified', walletAddress=user.wallet_address)`.

    # 3. Modify `app/__init__.py` (or main app file):
    #    - Import the `auth_router` from `app.routers.auth_router`.
    #    - Include the router in the main FastAPI app: `app.include_router(auth_router)`.
    ```
*   **Files Involved:** `app/schemas/auth_schemas.py` (new), `app/routers/auth_router.py` (new), `app/__init__.py` (modified).
*   **Testing:**
    1.  Run the FastAPI application.
    2.  Use `curl` or a tool like Postman/Insomnia:
        *   Send a `POST` request to `/api/auth/verify`.
        *   Request Body (JSON): `{"walletAddress": "YOUR_TEST_WALLET_ADDRESS"}`
        *   Verify the response is `{"status": "verified", "walletAddress": "YOUR_TEST_WALLET_ADDRESS"}`.
        *   Check the `users` table in your database; the test wallet address should now exist. Sending the same request again should not create a duplicate.

---

**Step 1.3: Configuration API Endpoints**

*   **Goal:** Create API endpoints to allow fetching and saving user-specific bot configurations. Implement placeholder authentication.
*   **Instructions for AI:**
    ```python
    # Goal: Implement GET and POST endpoints for user configurations.

    # 1. Create `app/schemas/config_schemas.py`:
    #    - Define Pydantic model `ConfigurationBase` (contains all fields from Configuration model EXCEPT id, user_wallet, updated_at):
    #        - `max_buy_sol: float | None = 0.01`
    #        - `tp_percent: float | None = 100.0`
    #        - `sl_percent: float | None = 50.0`
    #        - `min_unique_wallets: int | None = None`
    #        - `min_total_volume: float | None = None`
    #        - `max_holder1_percent: float | None = None`
    #    - Define Pydantic model `ConfigurationCreate` inheriting from `ConfigurationBase`.
    #    - Define Pydantic model `ConfigurationResponse` inheriting from `ConfigurationBase`:
    #        - `user_wallet: str`
    #        - `updated_at: datetime`
    #        - `class Config: orm_mode = True` (or `from_attributes = True` for Pydantic v2+)

    # 2. Create `app/routers/config_router.py`:
    #    - Import `APIRouter`, `Depends`, `HTTPException`.
    #    - Import `AsyncSession`.
    #    - Import `get_db_session`.
    #    - Import relevant CRUD functions from `app.crud.config_crud`.
    #    - Import Pydantic models from `app.schemas.config_schemas`.
    #    - Create router: `router = APIRouter(prefix="/api/config", tags=["configuration"])`.
    #    - Define endpoint `GET /`:
    #        - Decorator: `@router.get("/", response_model=ConfigurationResponse)`
    #        - Function signature: `async def get_config(user_wallet: str, db: AsyncSession = Depends(get_db_session)) # Placeholder Auth`.
    #        - Call `config = await get_configuration_by_user(db, user_wallet)`.
    #        - If `config` is None, return a default `ConfigurationResponse` object (e.g., create an instance with default values and the provided `user_wallet`).
    #        - If `config` exists, return it (SQLAlchemy model will be converted by Pydantic).
    #    - Define endpoint `POST /`:
    #        - Decorator: `@router.post("/", response_model=ConfigurationResponse)`
    #        - Function signature: `async def update_config(user_wallet: str, config_in: ConfigurationCreate, db: AsyncSession = Depends(get_db_session)) # Placeholder Auth`.
    #        - Convert `config_in` Pydantic model to a dictionary suitable for the CRUD function: `config_data = config_in.dict(exclude_unset=True)` (or `.model_dump(exclude_unset=True)` for Pydantic v2+).
    #        - Call `updated_config = await update_or_create_configuration(db, user_wallet, config_data)`.
    #        - Return `updated_config`.

    # 3. Modify `app/__init__.py` (or main app file):
    #    - Import the `config_router`.
    #    - Include the router: `app.include_router(config_router)`.
    ```
*   **Files Involved:** `app/schemas/config_schemas.py` (new), `app/routers/config_router.py` (new), `app/crud/config_crud.py` (modified), `app/__init__.py` (modified).
*   **Testing:**
    1.  Run FastAPI. Ensure a user exists from previous step (`YOUR_TEST_WALLET_ADDRESS`).
    2.  Use `curl`/Postman:
        *   `GET /api/config?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Verify you get default configuration values.
        *   `POST /api/config?user_wallet=YOUR_TEST_WALLET_ADDRESS` with Request Body (JSON): `{"max_buy_sol": 0.05, "tp_percent": 150.0}` -> Verify the response shows updated values. Check the `configurations` table in the DB.
        *   `GET /api/config?user_wallet=YOUR_TEST_WALLET_ADDRESS` again -> Verify the response shows the saved `0.05` and `150.0` values.
        *   `POST /api/config?user_wallet=YOUR_TEST_WALLET_ADDRESS` with Request Body (JSON): `{"sl_percent": 40.0}` -> Verify only `sl_percent` updates and others remain. Check DB.
        *   `GET /api/config?user_wallet=ANOTHER_WALLET_ADDRESS` -> Verify you get defaults for a different user.

---

**Phase 2: Bot State & Basic Orchestration Stub**

*(Phase Goal: Implement database storage for bot state (Running/Stopped), create API endpoints to control this state, refactor the core bot logic signature, and set up a placeholder manager for user-specific bot tasks.)*

---

**Step 2.1: Bot State Model & API**

*   **Goal:** Define the `BotState` model and create API endpoints for users to check and change their bot's status (start/stop).
*   **Instructions for AI:**
    ```python
    # Goal: Define BotState model and implement API endpoints for status control.

    # 1. Create `app/schemas/bot_schemas.py`:
    #    - Import `Enum` from `enum`.
    #    - Define `BotStatusEnum(str, Enum)` with values 'Stopped', 'Running', 'Error'.
    #    - Define Pydantic model `BotStateResponse`:
    #        - `user_wallet: str`
    #        - `status: BotStatusEnum`
    #        - `last_changed: datetime`
    #        - `class Config: orm_mode = True` (or `from_attributes = True`)

    # 2. Modify `app/models.py`:
    #    - Import `BotStatusEnum` from `app.schemas.bot_schemas`.
    #    - Import `Enum as PyEnum` from `sqlalchemy`.
    #    - Define SQLAlchemy model `BotState`:
    #        - `__tablename__ = 'bot_states'`
    #        - `id = Column(Integer, primary_key=True, index=True)`
    #        - `user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, unique=True, index=True)`
    #        - `status = Column(PyEnum(BotStatusEnum), default=BotStatusEnum.Stopped, nullable=False)`
    #        - `last_changed = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)`
    #        - Define relationship: `owner = relationship("User")` (or adjust existing User model if needed).

    # 3. Create `app/crud/bot_state_crud.py`:
    #    - Import `AsyncSession`, `select`.
    #    - Import `BotState`, `BotStatusEnum`, `User`.
    #    - Define async function `get_bot_state(db: AsyncSession, user_wallet: str)`:
    #        - Query `BotState` filtered by `user_wallet`. Return first result or None.
    #    - Define async function `set_bot_status(db: AsyncSession, user_wallet: str, status: BotStatusEnum)`:
    #        - Call `get_bot_state`.
    #        - If exists, update `state.status = status`.
    #        - If not exists, create new `BotState` instance with `user_wallet` and `status`. Check if user exists first using `get_or_create_user`.
    #        - Add, commit, refresh, and return the state object.

    # 4. Create `app/routers/bot_router.py`:
    #    - Import `APIRouter`, `Depends`, `HTTPException`.
    #    - Import `AsyncSession`.
    #    - Import `get_db_session`.
    #    - Import CRUD functions from `app.crud.bot_state_crud`.
    #    - Import `BotStateResponse`, `BotStatusEnum`.
    #    - Create router: `router = APIRouter(prefix="/api/bot", tags=["bot_control"])`.
    #    - Define endpoint `GET /status`:
    #        - Decorator: `@router.get("/status", response_model=BotStateResponse)`
    #        - Function signature: `async def get_status(user_wallet: str, db: AsyncSession = Depends(get_db_session)) # Placeholder Auth`.
    #        - Call `state = await get_bot_state(db, user_wallet)`.
    #        - If `state` is None, return default `BotStateResponse(user_wallet=user_wallet, status=BotStatusEnum.Stopped, last_changed=datetime.utcnow())`.
    #        - Else, return `state`.
    #    - Define endpoint `POST /start`:
    #        - Decorator: `@router.post("/start", response_model=BotStateResponse)`
    #        - Function signature: `async def start_bot_request(user_wallet: str, db: AsyncSession = Depends(get_db_session)) # Placeholder Auth`.
    #        - Call `updated_state = await set_bot_status(db, user_wallet, BotStatusEnum.Running)`.
    #        - **Log:** `logger.info(f"TODO: Start bot task requested for {user_wallet}. Actual start logic needed.")`
    #        - Return `updated_state`.
    #    - Define endpoint `POST /stop`:
    #        - Decorator: `@router.post("/stop", response_model=BotStateResponse)`
    #        - Function signature: `async def stop_bot_request(user_wallet: str, db: AsyncSession = Depends(get_db_session)) # Placeholder Auth`.
    #        - Call `updated_state = await set_bot_status(db, user_wallet, BotStatusEnum.Stopped)`.
    #        - **Log:** `logger.info(f"TODO: Stop bot task requested for {user_wallet}. Actual stop logic needed.")`
    #        - Return `updated_state`.

    # 5. Modify `app/__init__.py` (or main app file):
    #    - Import the `bot_router`.
    #    - Include the router: `app.include_router(bot_router)`.
    #    - Rerun the `create_db_and_tables()` logic on startup (or use Alembic migrations) to create the `bot_states` table.
    ```
*   **Files Involved:** `app/schemas/bot_schemas.py` (new), `app/models.py` (modified), `app/crud/bot_state_crud.py` (new), `app/routers/bot_router.py` (new), `app/__init__.py` (modified).
*   **Testing:**
    1.  Run FastAPI. Ensure the `bot_states` table is created.
    2.  Use `curl`/Postman:
        *   `GET /api/bot/status?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Should return 'Stopped' status initially.
        *   `POST /api/bot/start?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Should return 'Running' status. Check DB. Check logs for TODO message.
        *   `GET /api/bot/status?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Should return 'Running'.
        *   `POST /api/bot/stop?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Should return 'Stopped'. Check DB. Check logs for TODO message.
        *   `GET /api/bot/status?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Should return 'Stopped'.

---

**Step 2.2: Refactor Core Bot Logic Signature**

*   **Goal:** Modify the main entry point function of the existing bot logic so it can be called with user-specific context (wallet address and configuration). Add warnings for shared state usage.
*   **Instructions for AI:**
    ```python
    # Goal: Refactor main bot entry function to accept user context and add warnings for shared state.

    # 1. Locate the primary async function that starts the bot's execution (e.g., in `solbot/solbot_main.py`, or maybe a function called by background tasks in `app/__init__.py`).
    #    Let's assume it's `async def original_bot_main(): ...`

    # 2. Rename and modify the function signature:
    #    Change `async def original_bot_main():`
    #    to `async def run_user_bot_session(user_wallet: str, user_config: dict):`
    #    (Use `dict` for now, could be a Pydantic model later).

    # 3. Update Internal Logic:
    #    - Search within `run_user_bot_session` and functions it calls directly/indirectly.
    #    - Replace reads from global config files (related to user settings like buy amount, TP/SL) with reads from the `user_config` dictionary argument.
    #    - Add `user_wallet` to log messages within this execution path for context. Example: `logger.info(f"[{user_wallet}] Processing token...")`

    # 4. **Identify and Warn about Shared State:**
    #    - Search for accesses to shared `app.state` attributes (like `app.state.accumulators`, `app.state.mint_states`, `app.state.price_counters`, `app.state.subscription_ids`, `app.state.mint_activation_times`, etc.) within the call stack starting from `run_user_bot_session`.
    #    - **For each identified access point, add a warning log message.** Example:
    #      ```python
    #      import logging
    #      logger = logging.getLogger(__name__)
    #      # ... inside a function called by run_user_bot_session ...
    #      # Example access:
    #      # accumulator = app.state.accumulators.get(mint_address)
    #      # Add warning BEFORE the access:
    #      logger.warning(f"[{user_wallet}] Accessing shared app.state.accumulators - needs isolation for multi-user support.")
    #      accumulator = app.state.accumulators.get(mint_address) # The original access
    #      ```
    #    - **Do NOT** try to fix the isolation in this step, just add the warnings.

    # 5. Ensure the refactored function remains `async`.

    # 6. Remove or comment out the original way this function was called globally (e.g., from `app/__init__.py` background tasks if it was run globally before). It will now be invoked per-user by the Bot Manager.
    ```
*   **Files Involved:** `solbot/solbot_main.py` (or primary bot entry point, modified), potentially functions in `solbot/processing.py`, `solbot/prediction.py`, `listener/new_token_listener_pumpfun.py`, `solbot/websocket.py` (modified to add warnings). `app/__init__.py` (modified to remove global bot start).
*   **Testing:**
    1.  The primary test is that the application still runs without crashing due to the refactoring.
    2.  Create a temporary test script (`test_bot_session.py`):
        *   Import `run_user_bot_session`.
        *   Create mock `user_config` dict.
        *   Run `asyncio.run(run_user_bot_session('test_wallet_1', mock_config))`.
        *   Run it within a `try/except asyncio.TimeoutError` or similar to stop it after a few seconds.
        *   Examine the logs carefully. You **should** see the `WARNING` messages about accessing shared `app.state` components, prefixed with `[test_wallet_1]`. This confirms the warnings were added correctly.

---

**Step 2.3: User Bot Manager Stub**

*   **Goal:** Create the `UserBotManager` class to track and manage `asyncio.Task` objects for each running user bot session (initially using placeholder tasks).
*   **Instructions for AI:**
    ```python
    # Goal: Create the UserBotManager class stub.

    # 1. Create `app/bot_manager.py`:
    #    - Import `asyncio`, `logging`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Define class `UserBotManager`:
    #        - `def __init__(self):`
    #            - `self.active_bots: dict[str, asyncio.Task] = {}`
    #            - `self._lock = asyncio.Lock()` # Add a lock for thread safety
    #        - `async def start_bot(self, user_wallet: str):`
    #            - `async with self._lock:`
    #                - `if user_wallet in self.active_bots:`
    #                    - `logger.info(f"Bot already running for {user_wallet}")`
    #                    - `return False # Indicate already running`
    #                - `logger.info(f"Creating placeholder bot task for {user_wallet}")`
    #                - `# TODO: Replace placeholder task with actual bot session call in a later step`
    #                - `placeholder_task = asyncio.create_task(self._placeholder_bot_loop(user_wallet))`
    #                - `self.active_bots[user_wallet] = placeholder_task`
    #                - `logger.info(f"Placeholder bot task started for {user_wallet}")`
    #                - `return True # Indicate started`
    #        - `async def stop_bot(self, user_wallet: str):`
    #            - `async with self._lock:`
    #                - `task = self.active_bots.pop(user_wallet, None)`
    #                - `if task:`
    #                    - `logger.info(f"Attempting to stop bot task for {user_wallet}")`
    #                    - `task.cancel()`
    #                    - `try:`
    #                        - `await asyncio.wait_for(task, timeout=5.0)` # Wait briefly for cleanup
    #                    - `except asyncio.CancelledError:`
    #                        - `logger.info(f"Bot task for {user_wallet} cancelled successfully.")`
    #                    - `except asyncio.TimeoutError:`
    #                        - `logger.warning(f"Timeout waiting for bot task {user_wallet} to cancel.")`
    #                    - `except Exception as e:`
    #                        - `logger.error(f"Error during task cancellation for {user_wallet}: {e}")`
    #                    - `return True # Indicate stopped`
    #                - `else:`
    #                    - `logger.info(f"No active bot task found for {user_wallet} to stop.")`
    #                    - `return False # Indicate not found`
    #        - `async def _placeholder_bot_loop(self, user_wallet: str):` # Helper for placeholder task
    #            - `logger.info(f"Placeholder loop started for {user_wallet}")`
    #            - `try:`
    #                - `while True:`
    #                    - `await asyncio.sleep(60)` # Simulate work
    #            - `except asyncio.CancelledError:`
    #                - `logger.info(f"Placeholder loop cancelled for {user_wallet}")`
    #                - `raise # Re-raise CancelledError`
    #            - `except Exception as e:`
    #                - `logger.error(f"Error in placeholder loop for {user_wallet}: {e}")`
    #            - `finally:`
    #                 - `logger.info(f"Placeholder loop finished for {user_wallet}")`

    # 2. Instantiate the manager globally at the bottom of `app/bot_manager.py`:
    #    `bot_manager = UserBotManager()`
    ```
*   **Files Involved:** `app/bot_manager.py` (new).
*   **Testing:**
    1.  Create a temporary async test script (`test_manager.py`):
        *   Import `bot_manager` from `app.bot_manager`.
        *   Import `asyncio`.
        *   Define `async def main():`
            *   `await bot_manager.start_bot('wallet_A')`
            *   `await bot_manager.start_bot('wallet_B')`
            *   `print(f"Active bots: {bot_manager.active_bots.keys()}")` # Should show A and B
            *   `await asyncio.sleep(1)`
            *   `await bot_manager.stop_bot('wallet_A')`
            *   `print(f"Active bots after stop A: {bot_manager.active_bots.keys()}")` # Should show only B
            *   `await bot_manager.stop_bot('wallet_B')`
            *   `print(f"Active bots after stop B: {bot_manager.active_bots.keys()}")` # Should be empty
        *   Run `asyncio.run(main())`.
        *   Check logs for start/stop messages and placeholder loop messages/cancellation.

---

**Step 2.4: Integrate Bot Manager with API**

*   **Goal:** Connect the `/api/bot/start` and `/api/bot/stop` API endpoints to the corresponding methods in the `UserBotManager`.
*   **Instructions for AI:**
    ```python
    # Goal: Connect Start/Stop API endpoints to the UserBotManager.

    # 1. Modify `app/routers/bot_router.py`:
    #    - Import `bot_manager` from `app.bot_manager`.
    #    - In the `start_bot_request` function (`POST /start`):
    #        - After successfully calling `set_bot_status(..., BotStatusEnum.Running)`, add:
    #          ```python
    #          success = await bot_manager.start_bot(user_wallet)
    #          if not success:
    #              # Handle case where bot might already be marked as running in manager
    #              # Optional: Could potentially reset DB state back to Stopped if manager says already running?
    #              logger.warning(f"Bot manager reported bot already running for {user_wallet}, but DB state was just set to Running.")
    #          ```
    #        - Remove the "TODO: Start bot task requested..." log message.
    #    - In the `stop_bot_request` function (`POST /stop`):
    #        - After successfully calling `set_bot_status(..., BotStatusEnum.Stopped)`, add:
    #          ```python
    #          await bot_manager.stop_bot(user_wallet)
    #          ```
    #        - Remove the "TODO: Stop bot task requested..." log message.
    ```
*   **Files Involved:** `app/routers/bot_router.py` (modified).
*   **Testing:**
    1.  Run FastAPI.
    2.  Use `curl`/Postman:
        *   `POST /api/bot/start?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Check logs. You should see "Creating placeholder bot task..." from the manager. DB state should be 'Running'.
        *   `POST /api/bot/stop?user_wallet=YOUR_TEST_WALLET_ADDRESS` -> Check logs. You should see "Attempting to stop bot task..." and potentially "Placeholder loop cancelled...". DB state should be 'Stopped'.
        *   Call `/start` again, then `/stop` again to ensure it handles transitions correctly.

---

This concludes the detailed breakdown for Phases 1 and 2. Subsequent phases will involve:

*   **Phase 3:** Integrating the actual `run_user_bot_session` into the `UserBotManager`, handling shared state isolation (`app.state` refactoring).
*   **Phase 4:** Implementing the Holdings/Trades models and CRUD, refactoring bot logic to update these tables.
*   **Phase 5:** Implementing the Transaction Proposal flow (backend -> frontend communication).
*   **Phase 6:** Implementing the Price Monitor service for TP/SL and live P&L.
*   **Phase 7:** Building the Dashboard API endpoint.
*   **Phase 8:** Implementing real authentication (e.g., sign-in with Solana) instead of placeholders.
*   **Phase 9:** Frontend Development (using the APIs built here).


Excellent! Glad to hear Phases 1 and 2 are implemented successfully. Let's proceed with the next set of phases to integrate the actual bot logic and add the necessary features for trading and monitoring.

---

**Phase 3: Core Bot Logic Integration & State Isolation**

*(Phase Goal: Replace the placeholder bot task with the actual refactored bot logic, and begin isolating shared `app.state` components to work per-user.)*

---

**Step 3.1: Integrate `run_user_bot_session` into Bot Manager**

*   **Goal:** Replace the placeholder task in `UserBotManager` with the actual `run_user_bot_session` function, passing the correct user context. Add basic error handling.
*   **Instructions for AI:**
    ```python
    # Goal: Replace placeholder task in UserBotManager with the real bot session function.

    # 1. Modify `app/bot_manager.py`:
    #    - Import the refactored `run_user_bot_session` function (adjust path as needed, e.g., `from solbot.solbot_main import run_user_bot_session`).
    #    - Import CRUD functions needed to get user config (e.g., `from app.crud.config_crud import get_configuration_by_user`).
    #    - Import `get_db_session` (or pass session factory during manager init).
    #    - Import `set_bot_status` from `app.crud.bot_state_crud`.
    #    - Import `BotStatusEnum` from `app.schemas.bot_schemas`.
    #    - Modify the `start_bot` method:
    #        - Inside the `async with self._lock:` block, before creating the task:
    #            - Fetch the user's configuration from the DB:
    #              ```python
    #              # Need a DB session here. Option 1: Pass session factory to manager.
    #              # Option 2 (simpler for now): Create a temporary session.
    #              from app.database import async_session_factory
    #              async with async_session_factory() as db:
    #                  user_config_db = await get_configuration_by_user(db, user_wallet)
    #              if not user_config_db:
    #                  logger.error(f"[{user_wallet}] No configuration found. Cannot start bot.")
    #                  # Optional: Set DB state back to Stopped/Error?
    #                  return False
    #              # Convert SQLAlchemy model to dict for the bot function (adjust if using Pydantic)
    #              user_config = {c.name: getattr(user_config_db, c.name) for c in user_config_db.__table__.columns}
    #              logger.info(f"[{user_wallet}] Loaded configuration for bot session.")
    #              ```
    #            - Replace the line creating `placeholder_task` with:
    #              ```python
    #              # Wrap the actual bot session in a helper to handle completion/errors
    #              bot_task = asyncio.create_task(self._run_bot_wrapper(user_wallet, user_config))
    #              ```
    #            - Update the log message: `logger.info(f"Actual bot task created for {user_wallet}")`
    #    - Remove the `_placeholder_bot_loop` method.
    #    - Add a new method `_run_bot_wrapper(self, user_wallet: str, user_config: dict)`:
    #        - This wrapper will run the actual bot session and handle its completion or errors.
    #        - ```python
    #          logger.info(f"[{user_wallet}] Starting bot execution wrapper.")
    #          try:
    #              # Make sure all necessary application context (like app.state if needed globally)
    #              # is accessible or passed appropriately if run_user_bot_session needs it.
    #              # If app.state is needed, the manager might need access to the FastAPI app instance.
    #              await run_user_bot_session(user_wallet=user_wallet, user_config=user_config)
    #              logger.info(f"[{user_wallet}] Bot session finished normally.")
    #              final_status = BotStatusEnum.Stopped
    #          except asyncio.CancelledError:
    #              logger.info(f"[{user_wallet}] Bot session cancelled.")
    #              # Status is likely already set to Stopped by the stop_bot request, no change needed here.
    #              return # Don't proceed to final status update
    #          except Exception as e:
    #              logger.exception(f"[{user_wallet}] Bot session failed with error: {e}") # Use logger.exception for traceback
    #              final_status = BotStatusEnum.Error
    #          finally:
    #              # Clean up task entry in the manager AFTER the task finishes/errors/is cancelled
    #              async with self._lock:
    #                  # Check if task is still the one we expect before removing
    #                  # This handles cases where stop_bot might have run concurrently
    #                  current_task = self.active_bots.get(user_wallet)
    #                  if current_task is asyncio.current_task():
    #                       self.active_bots.pop(user_wallet, None)
    #                       logger.info(f"[{user_wallet}] Removed task from active_bots dict in finally block.")
    #                  elif current_task:
    #                       logger.warning(f"[{user_wallet}] Task in active_bots dict was different. Not removing.")
    #                  else:
    #                       logger.info(f"[{user_wallet}] Task already removed from active_bots dict.")


    #          # Update the DB state only if the task wasn't cancelled externally
    #          # Check if the task is marked as cancelled right before DB update
    #          if not asyncio.current_task().cancelled():
    #               try:
    #                  logger.info(f"[{user_wallet}] Setting final DB state to {final_status.value}")
    #                  from app.database import async_session_factory
    #                  async with async_session_factory() as db:
    #                      await set_bot_status(db, user_wallet, final_status)
    #               except Exception as db_err:
    #                   logger.error(f"[{user_wallet}] Failed to set final DB state: {db_err}")

    #          ```
    ```
*   **Files Involved:** `app/bot_manager.py` (modified), `solbot/solbot_main.py` (imported from).
*   **Testing:**
    1.  Run FastAPI.
    2.  Use `curl`/Postman:
        *   Ensure a config exists for `YOUR_TEST_WALLET_ADDRESS`.
        *   `POST /api/bot/start?user_wallet=YOUR_TEST_WALLET_ADDRESS`.
        *   **Monitor logs carefully.** You should see:
            *   "Loaded configuration..."
            *   "Actual bot task created..."
            *   "Starting bot execution wrapper..."
            *   Logs from your actual `run_user_bot_session` function.
            *   The `WARNING` messages about accessing shared `app.state` (from Step 2.2).
    3.  Let it run for a bit. Then:
        *   `POST /api/bot/stop?user_wallet=YOUR_TEST_WALLET_ADDRESS`.
        *   **Monitor logs.** You should see:
            *   "Attempting to stop bot task..."
            *   Messages indicating cancellation within `run_user_bot_session` if you added handling there.
            *   "Bot session cancelled." (from the wrapper)
            *   DB state should be 'Stopped'.
    4.  **(Error Test):** Introduce a deliberate error inside `run_user_bot_session` (e.g., `raise ValueError("Test Error")`). Start the bot. Check logs for "Bot session failed..." and the traceback. Check DB state; it should be set to 'Error'.

---


Here is the breakdown of the revised Step 3.2 into smaller, actionable, and testable sub-steps:

**Revised Step 3.2 Breakdown: Implement Hybrid Architecture**

**Overall Goal:** Reconfigure the backend to use shared global state (`app.state`) for initial 100-price collection/prediction, while user sessions (`run_user_bot_session`) react to broadcasted buy signals using a simplified `UserBotContext`.

---

**Step 3.2.1: Restore Global State & Tasks**

*   **Goal:** Ensure the necessary global state attributes exist in `app.state` and the global background tasks that manage them are running. Revert any functions that were incorrectly modified to accept `UserBotContext`.
*   **Instructions for AI:**
    ```python
    # Goal: Restore required global state attributes and background tasks in app/__init__.py
    # Also revert global functions inappropriately modified in the previous attempt.

    # 1. Modify `app/__init__.py`:
    #    - In the `CustomFastAPI.__init__` method:
    #        - Verify or **re-add** the following attributes to `self.state`:
    #          `self.state.accumulators = {}`
    #          `self.state.mint_states = {}`
    #          `self.state.price_counters = {}`
    #          `self.state.detection_times = {}`
    #          `self.state.mint_activation_times = {}`
    #          `self.state.completion_queue = asyncio.PriorityQueue()`
    #          `self.state.accumulators_lock = asyncio.Lock()` # Or existing equivalent lock
    #          `self.state.mint_states_lock = asyncio.Lock()`
    #          `self.state.price_counters_lock = asyncio.Lock()`
    #          `self.state.mint_lock_manager = MintAddressLockManager()`
    #    - In the `lifespan` function:
    #        - Ensure the following `asyncio.create_task` calls are present and **uncommented**:
    #            - `asyncio.create_task(monitor_accumulators(app))`
    #            - `asyncio.create_task(log_mint_states(app))`
    #            - `asyncio.create_task(process_signatures(app))`
    #            - `asyncio.create_task(process_completion_queue(app))` # Add if missing, ensure definition exists
    #    - **Add `bot_manager` to `app.state`**: After the global `bot_manager` instance is created/imported, add `app.state.bot_manager = bot_manager`.

    # 2. Verify/Revert Global Functions (`monitoring.py`, `processing.py`, `websocket.py`):
    #    - **Search and ensure** the following functions DO NOT accept `UserBotContext` as a parameter and operate using the global `app` instance or `app.state`:
    #        - `monitor_accumulators(app)`
    #        - `log_mint_states(app)`
    #        - `process_signatures(app)`
    #        - `skip_mint_address(mint_address, app)`
    #        - `check_transaction_for_prices(app, signature, mint_address)`
    #        - `complete_data_collection(mint_address, accumulator, app)`
    #        - `process_transaction(transaction, app, signature)`
    #    - **Delete** any `_for_user` variants of these functions if they exist (e.g., delete `monitor_accumulators_for_user`).
    #    - **Verify `accumulator.py`:** Confirm functions in `solbot/accumulator.py` do not import/use `app` or `app.state`.
    ```
*   **Files Involved:** `app/__init__.py`, `solbot/monitoring.py`, `solbot/processing.py`, `solbot/websocket.py`, `solbot/accumulator.py`.
*   **Testing:**
    1.  Run the FastAPI application.
    2.  Check the startup logs. Ensure there are no errors related to missing state attributes or starting background tasks. Verify logs indicate global tasks like `monitor_accumulators` have started.
    3.  Make basic API calls (`/api/auth/verify`, `/api/config`) to ensure the app is generally functional.

---

**Step 3.2.2: Define Simplified `UserBotContext` & Update `UserBotManager`**

*   **Goal:** Create the simplified `UserBotContext` class (holding only the signal queue) and update `UserBotManager` to manage these contexts and include a *stub* for broadcasting signals.
*   **Instructions for AI:**
    ```python
    # Goal: Define the simplified UserBotContext and update UserBotManager to manage it.

    # 1. Create/Modify `app/user_context.py`:
    #    - Ensure the `UserBotContext` class exists.
    #    - Modify its `__init__` to ONLY contain `user_wallet` and `incoming_buy_signals` queue.
    #    - Remove all other attributes (accumulators, states, counters, locks, monitoring_tasks).
    #    - Keep imports minimal (`asyncio`, `Queue`).
    #    ```python
    #    # Expected content of app/user_context.py
    #    import asyncio
    #    from asyncio import Queue
    #
    #    class UserBotContext:
    #        def __init__(self, user_wallet: str):
    #            self.user_wallet: str = user_wallet
    #            self.incoming_buy_signals: asyncio.Queue = asyncio.Queue()
    #    ```

    # 2. Modify `app/bot_manager.py`:
    #    - Import `UserBotContext` from `app.user_context`.
    #    - In `UserBotManager.__init__`: Add `self.user_contexts: dict[str, UserBotContext] = {}`.
    #    - Modify `start_bot`: Before creating the task, create and store the `UserBotContext`:
    #        `user_context = UserBotContext(user_wallet)`
    #        `self.user_contexts[user_wallet] = user_context`
    #        Update task creation to pass `user_context` to `_run_bot_wrapper`.
    #    - Modify `stop_bot`: After cancelling the main task, remove the context:
    #        `self.user_contexts.pop(user_wallet, None)`
    #        Log cleanup.
    #    - Modify `_run_bot_wrapper` signature to accept `user_context: UserBotContext`.
    #    - Modify the call inside `_run_bot_wrapper` to pass `user_context` to `run_user_bot_session`.
    #    - Modify the `finally` block in `_run_bot_wrapper` to also clean up context:
    #        `self.user_contexts.pop(user_wallet, None)`
    #    - **Add the broadcast method stub:**
    #      ```python
    #      async def broadcast_buy_signal(self, mint_address: str, prediction_details: dict):
    #          # TODO: Implement actual broadcasting logic in Step 3.2.5
    #          logger.info(f"[STUB] Broadcasting BUY signal for {mint_address} with details: {prediction_details}")
    #          # Placeholder: just log active users it would broadcast to
    #          async with self._lock:
    #              active_wallets = list(self.active_bots.keys())
    #          logger.info(f"[STUB] Would broadcast to: {active_wallets}")
    #      ```
    ```
*   **Files Involved:** `app/user_context.py`, `app/bot_manager.py`.
*   **Testing:**
    1.  Run FastAPI.
    2.  Use API (`/api/bot/start`) to start a bot for `wallet_A`. Check logs for "Created UserBotContext". Check `bot_manager.user_contexts` dictionary via debugger or logging if possible.
    3.  Use API (`/api/bot/stop`) to stop the bot for `wallet_A`. Check logs for "Cleaned up UserBotContext". Check `bot_manager.user_contexts` is empty again.
    4.  In a test script, import `bot_manager` and manually call `await bot_manager.broadcast_buy_signal("some_mint", {"prob": 0.9})`. Verify the "[STUB] Broadcasting..." log message appears.

---

**Step 3.2.3: Adapt WebSocket Handler for Global Price Check**

*   **Goal:** Ensure the WebSocket `handle_messages` function calls the *global* `check_transaction_for_prices` function and doesn't attempt user context lookups for initial price collection.
*   **Instructions for AI:**
    ```python
    # Goal: Ensure WebSocket handler uses global check_transaction_for_prices.

    # 1. Modify `solbot/websocket.py`:
    #    - Locate the `handle_messages` async function.
    #    - Find the section that processes `logsNotification` messages.
    #    - After extracting `signature` and `mint_address`:
    #        - **Remove** any code that attempts to loop through `bot_manager.user_contexts` to find a `target_user_context` at this stage.
    #        - Ensure the code **directly calls** the global price checking function:
    #          `await check_transaction_for_prices(app, signature, mint_address)`
    #        - Make sure the `app` instance is correctly passed or accessible to `handle_messages`.
    #    - **Verify** that the global `check_transaction_for_prices` function (which should exist after Step 3.2.1) correctly interacts with global `app.state.price_counters`, `app.state.detection_times`, and the global `app.state.completion_queue`.
    ```
*   **Files Involved:** `solbot/websocket.py`.
*   **Testing:**
    1.  Run FastAPI.
    3.  **Focus on Logs:** Check logs *very carefully*. When a notification comes in, you should see logs indicating `check_transaction_for_prices` (the global one) is being called. You should *not* see logs related to user context lookups *within* the `handle_messages` function for this initial processing.
    4.  Check if interactions with global `app.state.price_counters` (if logged by `check_transaction_for_prices`) occur.

---

**Step 3.2.4: Implement Global Prediction Trigger & Data Saving (No Broadcast Yet)**

*   **Goal:** Modify the global completion process to run prediction, perform feature engineering, fetch holders, save the data row to CSV, but *not yet* broadcast the result.
*   **Instructions for AI:**
    ```python
    # Goal: Implement global prediction trigger, including data saving, but stub out broadcasting.

    # 1. Ensure a global prediction trigger function exists:
    #    - This might be `process_completion_queue(app)` processing `app.state.completion_queue` or logic within `complete_data_collection(app)`. Let's assume we create/use `run_global_prediction`.
    #    - Define/Modify `async def run_global_prediction(mint_address: str, accumulator_data: dict, app: FastAPI):` (likely in `solbot/prediction.py`).

    # 2. Implement the Data Processing & Saving Logic within `run_global_prediction`:
    #    - Import necessary functions/constants: `build_processed_data_from_accumulator`, `fetch_holders`, `feature_engineering`, `save_dataframe`, `HOLDERS_LIMIT`, `MERGED_TRANSACTIONS_FILE`, `np`.
    #    - Add the data processing block as outlined previously (fetch holders, integrate, feature engineer, save dataframe). Use the conceptual code from the "Combined Prompt" (Part 1, Item 5.e). Include try/except block for robustness.
    #    ```python
    #    # Inside run_global_prediction, after getting processed_data = build_processed_data...
    #    if processed_data:
    #        try:
    #            # Fetch holders, integrate into processed_data (handle errors/length mismatch)
    #            # ... (code from previous prompt) ...
    #
    #            # Feature engineering
    #            df_engineered = feature_engineering(processed_data)
    #
    #            # Save data row
    #            if df_engineered is not None and not df_engineered.empty:
    #                await save_dataframe(app, df_engineered, MERGED_TRANSACTIONS_FILE)
    #                logger.info(f"[{mint_address}] Saved data row globally.")
    #            # ... (handle else case) ...
    #        except Exception as data_err:
    #            logger.error(f"[{mint_address}] Error during global data processing/saving: {data_err}", exc_info=True)
    #    else:
    #         logger.error(f"[{mint_address}] Failed build processed data. Skipping save/prediction.")
    #         return # Exit if no data
    #    ```

    # 3. Add Prediction Call (Stub Broadcast):
    #    - After the data saving block within `run_global_prediction`:
    #        - Call `prediction_result = await predict_internal(processed_data, thresholds=None)` (pass `processed_data` dictionary). Ensure `predict_internal` is imported.
    #        - **Comment out the broadcast call for now:**
    #          ```python
    #          # if prediction_result['buy_signal']:
    #          #     logger.info(f"[{mint_address}] Global prediction is BUY. TODO: Implement broadcast.")
    #          #     # prediction_details = { 'probability': prediction_result['probabilities']['1'], ... } # Construct details
    #          #     # await app.state.bot_manager.broadcast_buy_signal(mint_address, prediction_details)
    #          # else:
    #          #     logger.info(f"[{mint_address}] Global prediction is NOT BUY.")
    #          ```

    # 4. Ensure `run_global_prediction` is called correctly:
    #    - Verify that the function processing the `app.state.completion_queue` (e.g., `process_completion_queue`) correctly retrieves the global accumulator from `app.state.accumulators` and calls `await run_global_prediction(mint_address, accumulator_data, app)`.
    #    - Ensure the global accumulator is cleaned up from `app.state.accumulators` after `run_global_prediction` finishes.
    ```
*   **Files Involved:** `solbot/prediction.py` (new/modified `run_global_prediction`), `solbot/processing.py` (modified caller like `process_completion_queue` or `complete_data_collection`), `solbot/data_fetching.py` (import `fetch_holders`), `solbot/data_storage.py` (import `save_dataframe`), `solbot/config.py` (import `MERGED_TRANSACTIONS_FILE`, `HOLDERS_LIMIT`).
*   **Testing:**
    1.  Manually trigger the global completion process for a test mint address (e.g., by putting an item onto `app.state.completion_queue` if that's the trigger, or calling the relevant part of `complete_data_collection`). You'll need mock accumulator data.
    2.  Verify logs show: Holders being fetched, feature engineering happening, `save_dataframe` being called, and the data row being saved log message.
    3.  Check the `merged_transactions_df.csv` file to confirm the new row with features was appended correctly.
    4.  Verify the prediction runs (`predict_internal` logs).
    5.  Verify the "TODO: Implement broadcast" log appears if the prediction was BUY.

---

**Step 3.2.5: Enable Prediction Broadcasting**

*   **Goal:** Implement the actual broadcasting logic in `UserBotManager` and uncomment the call in `run_global_prediction`.
*   **Instructions for AI:**
    ```python
    # Goal: Implement signal broadcasting from global prediction to active users.

    # 1. Modify `app/bot_manager.py`:
    #    - Implement the actual logic inside `async def broadcast_buy_signal(self, mint_address: str, prediction_details: dict):`
    #    - Replace the previous stub implementation.
    #    - Use the logic provided previously: lock manager, get active wallets, iterate outside lock, get context, put signal onto `context.incoming_buy_signals` queue using `put_nowait` with QueueFull handling.
    #      ```python
    #      # Replace STUB with actual implementation in broadcast_buy_signal
    #      logger.info(f"Broadcasting BUY signal for {mint_address} to active users.")
    #      signal_payload = (mint_address, prediction_details)
    #      async with self._lock:
    #          active_wallets = list(self.active_bots.keys()) # Get wallets with active bot tasks

    #      logger.debug(f"Attempting to broadcast to: {active_wallets}")
    #      for user_wallet in active_wallets:
    #          # Get context again inside loop in case state changed
    #          context = self.user_contexts.get(user_wallet)
    #          if context:
    #              try:
    #                  context.incoming_buy_signals.put_nowait(signal_payload)
    #                  logger.debug(f"Sent signal for {mint_address} to user {user_wallet}")
    #              except asyncio.QueueFull:
    #                  logger.warning(f"Incoming buy signal queue full for user {user_wallet}. Signal for {mint_address} dropped.")
    #          else:
    #              logger.debug(f"User context for {user_wallet} missing during broadcast (likely stopped).")
    #      ```

    # 2. Modify `solbot/prediction.py` (or where `run_global_prediction` is):
    #    - **Uncomment** the block that calls `broadcast_buy_signal`:
    #      ```python
    #      # Inside run_global_prediction, after predict_internal
    #      if prediction_result['buy_signal']:
    #          logger.info(f"[{mint_address}] Global prediction is BUY. Broadcasting...")
    #          # Construct prediction_details dict (ensure necessary features for filtering are included)
    #          prediction_details = {
    #              'probability': prediction_result['probabilities']['1'],
    #              # Add features needed by user filters, retrieving from 'processed_data' dict:
    #              'unique_wallets_count': processed_data.get('unique_wallets_count'),
    #              'sol_volume_sum': processed_data.get('sol_volume_sum'),
    #              'holder_1_perc_of_total_supply': processed_data.get('holder_1_perc_of_total_supply')
    #              # Add more if needed...
    #          }
    #          await app.state.bot_manager.broadcast_buy_signal(mint_address, prediction_details)
    #      else:
    #          logger.info(f"[{mint_address}] Global prediction is NOT BUY.")
    #      ```
    #    - Ensure `processed_data` dictionary is available in this scope to extract features for `prediction_details`.
    ```
*   **Files Involved:** `app/bot_manager.py`, `solbot/prediction.py` (or wherever `run_global_prediction` is).
*   **Testing:**
    1.  Start the FastAPI app.
    2.  Start a bot session for `wallet_A` using the API.
    3.  Manually trigger the global prediction process for a test mint that *should* result in a "BUY" signal.
    4.  Check logs: Verify `run_global_prediction` logs "Global prediction is BUY. Broadcasting...". Verify `UserBotManager.broadcast_buy_signal` logs "Broadcasting BUY signal..." and "Sent signal... to user wallet_A".
    5.  Check if the `run_user_bot_session` task (running for `wallet_A`) logs receiving the signal (this requires Step 3.2.6 to be implemented).
    6.  Start a second bot for `wallet_B`. Trigger the global prediction BUY again. Verify *both* users get the signal logged. Stop `wallet_A`. Trigger BUY again. Verify only `wallet_B` gets the signal.

---

**Step 3.2.6: Adapt User Session to Receive Signals**

*   **Goal:** Implement the reactive loop within `run_user_bot_session` to wait for signals from the queue, apply user filters, and log the placeholder for the transaction proposal.
*   **Instructions for AI:**
    ```python
    # Goal: Implement the reactive loop in run_user_bot_session.

    # 1. Modify `solbot/prediction.py` (or where `run_user_bot_session` is defined):
    #    - Ensure signature is `async def run_user_bot_session(user_wallet: str, user_config: dict, user_context: UserBotContext):`.
    #    - Replace the internal logic with the `async while True:` loop structure provided in the previous prompt ("Refined Prompt for Cursor AI (Implement Step 3.2 - Hybrid Architecture) - Only Part 6 Updated").
    #    - This loop should:
    #        - `await asyncio.wait_for(user_context.incoming_buy_signals.get(), timeout=...)`
    #        - Log receipt of signal.
    #        - Apply user filters based on `user_config` and `prediction_details` (ensure details needed are present). Log filter results.
    #        - If filters pass, construct the `transaction_proposal` dictionary.
    #        - Log the "PLACEHOLDER - PHASE 5: Propose..." message with the proposal details.
    #        - Call `user_context.incoming_buy_signals.task_done()`.
    #        - Include `try...except asyncio.TimeoutError` around the `wait_for` to handle the timeout and allow cancellation checks.
    #        - Include `try...except asyncio.CancelledError` around the main `while True` loop.
    #        - Include a general `except Exception` within the loop to log errors processing signals without crashing the whole session.
    #    - Ensure necessary imports (`asyncio`, `logging`, `UserBotContext`).
    ```
*   **Files Involved:** `solbot/prediction.py` (or where `run_user_bot_session` is defined).
*   **Testing:**
    1.  Ensure previous steps (especially 3.2.5 broadcasting) are working.
    2.  Start the FastAPI app.
    3.  Start a bot session for `wallet_A` with specific filter configurations (e.g., set `min_unique_wallets` to 50).
    4.  Trigger a global prediction/broadcast for `mint_X` with `prediction_details` that *fail* the filter (e.g., `unique_wallets_count: 40`). Check logs for `wallet_A`: verify it received the signal but logged skipping due to the filter.
    5.  Trigger a global prediction/broadcast for `mint_Y` with `prediction_details` that *pass* the filters. Check logs for `wallet_A`: verify it received the signal, logged passing filters, and logged the "PLACEHOLDER - PHASE 5" message with the correct `transaction_proposal` details.
    6.  Start a bot for `wallet_B` with *different* filters. Trigger global prediction BUY for `mint_Y` again. Verify `wallet_A` and `wallet_B` process the signal according to their *own* filters.
    7.  Test stopping the bot (`POST /api/bot/stop`). Verify the `run_user_bot_session` task logs cancellation and exits cleanly.

---

This detailed breakdown should allow you and the AI to tackle Step 3.2 incrementally and test each part before moving on. Remember to carefully review the AI's code after each sub-step. Good luck!

---

You're absolutely right. Breaking Phase 4 down further into individually testable steps is much better for managing complexity and ensuring correctness. My apologies for grouping them too broadly before.

Here's the breakdown of **Phase 4** into smaller, testable steps:

---

**Phase 4: Holdings & Trades Tracking (Implementation)**

*(Goal: Implement database storage for what tokens the bot holds for each user and their trade history. Integrate this with the buy/sell actions.)*

---

**Step 4.1: Implement Database Models**

*   **Goal:** Define the SQLAlchemy models for `Holding` and `Trade` in the database schema file.
*   **Instructions for AI:**
    ```python
    # Goal: Define Holding and Trade SQLAlchemy models.

    # 1. Modify `app/db_models.py`:
    #    - Import necessary SQLAlchemy components: `Float`, `Enum as PyEnum`, `UniqueConstraint`. Ensure `Base`, `Column`, `Integer`, `String`, `ForeignKey`, `DateTime`, `relationship`, `datetime` are imported.
    #    - Define the `Holding` model class inheriting from `Base`:
    #      ```python
    #      class Holding(Base):
    #          __tablename__ = 'holdings'
    #          id = Column(Integer, primary_key=True, index=True)
    #          user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    #          token_mint = Column(String, nullable=False, index=True)
    #          amount_held = Column(Float, nullable=False)
    #          average_buy_price_sol = Column(Float, nullable=False)
    #          last_acquired_at = Column(DateTime, default=datetime.utcnow)
    #          current_price_sol = Column(Float, nullable=True) # For storing latest price from monitor (Phase 6)
    #          updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    #          # Relationship (optional but good practice - requires backref in User)
    #          # owner = relationship("User", back_populates="holdings")
    #          # Constraint to ensure only one holding record per user per token
    #          __table_args__ = (UniqueConstraint('user_wallet', 'token_mint', name='uq_user_token_holding'),)
    #      ```
    #    - Define the `TradeTypeEnum` class inheriting from `str` and `PyEnum`:
    #      ```python
    #      class TradeTypeEnum(str, PyEnum):
    #          BUY = 'BUY'
    #          SELL = 'SELL'
    #      ```
    #    - Define the `Trade` model class inheriting from `Base`:
    #      ```python
    #      class Trade(Base):
    #          __tablename__ = 'trades'
    #          id = Column(Integer, primary_key=True, index=True)
    #          user_wallet = Column(String, ForeignKey('users.wallet_address'), nullable=False, index=True)
    #          timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    #          trade_type = Column(PyEnum(TradeTypeEnum, name='trade_type_enum'), nullable=False)
    #          token_mint = Column(String, nullable=False, index=True)
    #          amount_token = Column(Float, nullable=False)
    #          price_sol = Column(Float, nullable=False) # Avg price for this specific trade
    #          total_sol = Column(Float, nullable=False) # Cost for buy, proceeds for sell
    #          pnl_sol = Column(Float, nullable=True) # Calculated only for SELL trades
    #          tx_signature = Column(String, nullable=True, unique=True, index=True) # Optional
    #          # Relationship (optional but good practice - requires backref in User)
    #          # owner = relationship("User", back_populates="trades")
    #      ```
    #    - Add corresponding `relationship` definitions to the `User` model if you want bidirectional relationships (e.g., `holdings = relationship("Holding", back_populates="owner")`, `trades = relationship("Trade", back_populates="owner")`). Ensure `cascade="all, delete-orphan"` is added if appropriate for your logic.

    # 2. Modify `app/__init__.py`:
    #    - Ensure the `create_db_and_tables` function (called during startup) is correctly set up to use `Base.metadata.create_all`. This will automatically include the new models.
    ```
*   **Files Involved:** `app/db_models.py`, `app/__init__.py`.
*   **Testing:**
    1.  Delete your local database file (`test.db`) or drop the tables from your PostgreSQL database (`solbot_dev_db`) using pgAdmin to ensure a clean start.
    2.  Run `python run.py`.
    3.  Check the startup logs. Verify the application starts without errors. Crucially, look for logs indicating successful table creation or connection.
    4.  Connect to your database (PostgreSQL via pgAdmin or inspect `test.db` if using SQLite fallback). Verify that the new tables `holdings` and `trades` now exist with the correct columns and constraints (like the unique constraint on holdings). Stop the application after verification.

---

**Step 4.2: Implement Holdings CRUD Functions**

*   **Goal:** Create the asynchronous functions necessary to manage records in the `holdings` table (get, create, update on buy/sell).
*   **Instructions for AI:**
    ```python
    # Goal: Implement CRUD functions for the Holding model.

    # 1. Create `app/crud/holding_crud.py`:
    #    - Import `Holding`, `AsyncSession`, `select`, `update`, `delete`, `datetime`, `IntegrityError`, `text`, `logging`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Implement `async def get_holdings_by_user(db: AsyncSession, user_wallet: str) -> list[Holding]:`
    #        - Executes `select(Holding).where(Holding.user_wallet == user_wallet)`. Returns `result.scalars().all()`.
    #    - Implement `async def get_holding(db: AsyncSession, user_wallet: str, token_mint: str) -> Holding | None:`
    #        - Executes `select(Holding).where(Holding.user_wallet == user_wallet, Holding.token_mint == token_mint)`. Returns `result.scalars().first()`.
    #    - Implement `async def update_holding_on_buy(db: AsyncSession, user_wallet: str, token_mint: str, amount_bought: float, cost_sol: float) -> Holding | None:`
    #        - Handle potential `amount_bought <= 0` case early (log warning, return None).
    #        - Call `existing_holding = await get_holding(db, user_wallet, token_mint)`.
    #        - If `existing_holding`:
    #            - Calculate `new_total_cost = (existing_holding.average_buy_price_sol * existing_holding.amount_held) + cost_sol`.
    #            - Calculate `new_total_amount = existing_holding.amount_held + amount_bought`.
    #            - Calculate `new_avg_price = new_total_cost / new_total_amount` if `new_total_amount > 1e-12` else 0. # Avoid division by zero
    #            - Update: `existing_holding.amount_held = new_total_amount`, `existing_holding.average_buy_price_sol = new_avg_price`, `existing_holding.last_acquired_at = datetime.utcnow()`.
    #            - `holding_to_return = existing_holding`
    #        - Else (`not existing_holding`):
    #            - Calculate `avg_price = cost_sol / amount_bought` if `amount_bought > 1e-12` else 0. # Avoid division by zero
    #            - Create new `Holding` instance: `new_holding = Holding(user_wallet=user_wallet, token_mint=token_mint, amount_held=amount_bought, average_buy_price_sol=avg_price)`.
    #            - `db.add(new_holding)`
    #            - `holding_to_return = new_holding`
    #        - `try...except IntegrityError`: Handle potential race condition on insert (log warning, rollback, fetch again if needed).
    #        - `await db.commit()`
    #        - `await db.refresh(holding_to_return)`
    #        - Return `holding_to_return`.
    #    - Implement `async def update_holding_on_sell(db: AsyncSession, user_wallet: str, token_mint: str, amount_sold: float) -> float | None:`
    #        - Handle `amount_sold <= 0` case (log warning, return None or current amount).
    #        - Call `existing_holding = await get_holding(db, user_wallet, token_mint)`.
    #        - If `not existing_holding`: `logger.warning(...)`; return `None`.
    #        - If `amount_sold >= existing_holding.amount_held - 1e-12` (selling almost all or more, handle float precision):
    #            - `logger.info(f"Selling all/more than held of {token_mint} for {user_wallet}. Deleting record.")`
    #            - `await db.delete(existing_holding)`
    #            - `await db.commit()`
    #            - Return `0.0` # Indicate zero remaining holding
    #        - Else (partial sell):
    #            - `existing_holding.amount_held -= amount_sold`
    #            - `existing_holding.updated_at = datetime.utcnow()`
    #            - `remaining_amount = existing_holding.amount_held`
    #            - `await db.commit()`
    #            - `await db.refresh(existing_holding)`
    #            - Return `remaining_amount`.
    #    - Implement `async def update_holding_current_price(db: AsyncSession, holding_id: int, current_price: float):` (Optional for now, needed for Phase 6)
    #        - Execute `stmt = update(Holding).where(Holding.id == holding_id).values(current_price_sol=current_price, updated_at=datetime.utcnow())`.
    #        - `await db.execute(stmt)`
    #        - `await db.commit()`.

    # 2. Create `app/crud/__init__.py` (or update existing):
    #    - Ensure `holding_crud` (an instance or the functions) is exported or importable.
    ```
*   **Files Involved:** `app/crud/holding_crud.py` (new), `app/crud/__init__.py` (modified).
*   **Testing:**
    1.  **Write Unit Tests:** Create `tests/test_holding_crud.py`.
        *   Use `pytest` and `pytest-asyncio`.
        *   Set up a fixture to provide an async test database session (potentially using an in-memory SQLite for speed or resetting the main dev DB between tests).
        *   Test `update_holding_on_buy`: Create a user, call once (new holding created), assert values. Call again for same user/mint (holding updated), assert new amount and *correctly calculated average price*.
        *   Test `update_holding_on_sell`: Create a holding via `update_holding_on_buy`. Call `update_holding_on_sell` with a partial amount, assert the returned remaining amount and check DB. Call again to sell the rest, assert return is `0.0` (or very close) and verify record is deleted from DB using `get_holding`. Test selling more than held.
        *   Test `get_holding` and `get_holdings_by_user` to verify data retrieval.
    2.  Run the tests using `pytest`. Ensure all assertions pass.

---

**Step 4.3: Implement Trades CRUD Functions**

*   **Goal:** Create the asynchronous functions necessary to record completed trades in the `trades` table, including P&L calculation for sells.
*   **Instructions for AI:**
    ```python
    # Goal: Implement CRUD functions for the Trade model.

    # 1. Create `app/crud/trade_crud.py`:
    #    - Import `Trade`, `Holding`, `TradeTypeEnum`, `AsyncSession`, `datetime`, `select`, `logging`.
    #    - Import `get_holding` from `app.crud.holding_crud`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Implement `async def create_trade_record(db: AsyncSession, user_wallet: str, trade_type: TradeTypeEnum, token_mint: str, amount_token: float, price_sol: float, total_sol: float, tx_signature: str | None = None) -> Trade:`
    #        - Initialize `pnl_sol = None`.
    #        - If `trade_type == TradeTypeEnum.SELL`:
    #            - `logger.debug(f"Calculating PNL for SELL trade: {user_wallet}/{token_mint}")`
    #            - Call `holding = await get_holding(db, user_wallet, token_mint)`
    #            - If `holding`:
    #                - `avg_buy_price = holding.average_buy_price_sol`
    #                - `pnl_sol = (price_sol - avg_buy_price) * amount_token`
    #                - `logger.debug(f"PNL Calculated: Price={price_sol}, AvgBuy={avg_buy_price}, Amount={amount_token}, PNL={pnl_sol}")`
    #            - Else: `logger.warning(f"Could not find holding for {token_mint} to calculate PNL for sell trade.")`
    #        - Create `Trade` instance: `new_trade = Trade(user_wallet=user_wallet, trade_type=trade_type, token_mint=token_mint, amount_token=amount_token, price_sol=price_sol, total_sol=total_sol, pnl_sol=pnl_sol, tx_signature=tx_signature)`.
    #        - `db.add(new_trade)`
    #        - `await db.commit()`
    #        - `await db.refresh(new_trade)`
    #        - Return `new_trade`.
    #    - Implement `async def get_recent_trades_by_user(db: AsyncSession, user_wallet: str, limit: int = 50) -> list[Trade]:`
    #        - Execute `select(Trade).where(Trade.user_wallet == user_wallet).order_by(Trade.timestamp.desc()).limit(limit)`.
    #        - Return `result.scalars().all()`.

    # 2. Update `app/crud/__init__.py`:
    #    - Ensure `trade_crud` (an instance or the functions) is exported or importable.
    ```
*   **Files Involved:** `app/crud/trade_crud.py` (new), `app/crud/__init__.py` (modified).
*   **Testing:**
    1.  **Write Unit Tests:** Create `app/tests/test_trade_crud.py`.
        *   Use `pytest-asyncio` and a test DB session fixture.
        *   Test `create_trade_record` for `trade_type=BUY`. Verify record saved correctly, `pnl_sol` should be `None`.
        *   Test `create_trade_record` for `trade_type=SELL`:
            *   **Prerequisite:** Ensure a `Holding` record exists for the user/mint (you might need to call `holding_crud.update_holding_on_buy` within the test setup).
            *   Call `create_trade_record` with SELL details.
            *   Verify the record saved correctly and that `pnl_sol` was calculated based on the `average_buy_price_sol` from the prerequisite holding record.
        *   Test `get_recent_trades_by_user`: Create multiple trade records, call the function, and assert the correct number and order of trades are returned.
    2.  Run the tests using `pytest`. Ensure all assertions pass.

---

**Step 4.4: Implement Trade Reporting API Endpoint**

*   **Goal:** Create the `/api/trades/report` endpoint that the frontend will call after a transaction attempt, allowing the backend to update the database accordingly.
*   **Instructions for AI:**
    ```python
    # Goal: Implement the /api/trades/report endpoint.

    # 1. Create `app/schemas/trade_schemas.py`:
    #    - Import `BaseModel`, `datetime`.
    #    - Import `TradeTypeEnum` from `app.db_models`.
    #    - Define `TradeReportPayload`:
    #        - `user_wallet: str`
    #        - `tx_signature: str` # Signature of the buy/sell tx
    #        - `status: str` # 'success' or 'failure'
    #        - `trade_type: TradeTypeEnum`
    #        - `mint_address: str`
    #        - `amount_token: float` # Amount of token bought/sold
    #        - `price_sol: float` # Actual executed average price for the trade
    #        - `total_sol: float` # Actual SOL cost (for BUY) or proceeds (for SELL)
    #        - `error_message: str | None = None`
    #    - Define `TradeReportResponse`:
    #        - `message: str`
    #        - `trade_id: int | None = None`
    #        - `holding_updated: bool = False`

    # 2. Create `app/routers/trade_router.py`:
    #    - Import `APIRouter`, `Depends`, `HTTPException`, `Request`, `logging`.
    #    - Import `AsyncSession`, `get_db_session`.
    #    - Import CRUD functions `create_trade_record`, `update_holding_on_buy`, `update_holding_on_sell`.
    #    - Import Pydantic models `TradeReportPayload`, `TradeReportResponse`.
    #    - Import `TradeTypeEnum`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Create router: `router = APIRouter(prefix="/api/trades", tags=["trades"])`.
    #    - Define endpoint `POST /report`:
    #        - Decorator: `@router.post("/report", response_model=TradeReportResponse)`
    #        - Signature: `async def report_trade_result(payload: TradeReportPayload, request: Request, db: AsyncSession = Depends(get_db_session))`.
    #        - **(TODO - Auth):** `logger.warning("TODO: Implement real authentication for trade reporting!")`
    #        - `logger.info(f"Received trade report: {payload.dict(exclude_none=True)}")`
    #        - If `payload.status == 'success'`:
    #            - `holding_updated = False`
    #            - `trade_record = None`
    #            - `try:`
    #                - `# Important: Record trade BEFORE updating holding for PNL calc on sells`
    #                - `trade_record = await create_trade_record(db, user_wallet=payload.user_wallet, trade_type=payload.trade_type, token_mint=payload.mint_address, amount_token=payload.amount_token, price_sol=payload.price_sol, total_sol=payload.total_sol, tx_signature=payload.tx_signature)`
    #                - `if payload.trade_type == TradeTypeEnum.BUY:`
    #                    - `# cost_sol for BUY is payload.total_sol`
    #                    - `await update_holding_on_buy(db, user_wallet=payload.user_wallet, token_mint=payload.mint_address, amount_bought=payload.amount_token, cost_sol=payload.total_sol)`
    #                    - `holding_updated = True`
    #                    - `logger.info(f"PHASE 6 TODO: Signal Price Monitor to START polling for new BUY holding: {payload.mint_address} for user {payload.user_wallet}")`
    #                - `elif payload.trade_type == TradeTypeEnum.SELL:`
    #                    - `await update_holding_on_sell(db, user_wallet=payload.user_wallet, token_mint=payload.mint_address, amount_sold=payload.amount_token)`
    #                    - `holding_updated = True`
    #                    - `# Optional TODO: Signal Price Monitor to STOP polling if holding deleted?`
    #                - `return TradeReportResponse(message="Trade recorded successfully", trade_id=trade_record.id if trade_record else None, holding_updated=holding_updated)`
    #            - `except Exception as db_err:`
    #                - `logger.exception(f"Database error processing successful trade report for {payload.user_wallet}/{payload.mint_address}: {db_err}")`
    #                - `raise HTTPException(status_code=500, detail="Database error processing trade report")`
    #        - Elif `payload.status == 'failure'`:
    #            - `logger.warning(f"Received failed trade report for {payload.user_wallet} - {payload.mint_address}: {payload.error_message}")`
    #            - `return TradeReportResponse(message="Failed trade reported", holding_updated=False)`
    #        - Else: `raise HTTPException(status_code=400, detail="Invalid status in trade report")`

    # 3. Modify `app/__init__.py` (or `app/routes.py`):
    #    - Import the `trade_router`.
    #    - Include the router in the main FastAPI app: `app.include_router(trade_router)`.
    ```
*   **Files Involved:** `app/schemas/trade_schemas.py` (new), `app/routers/trade_router.py` (new), `app/__init__.py` or `app/routes.py` (modified).
*   **Testing:**
    1.  Run `python run.py`.
    2.  Use `curl`:
        *   Send a `POST` to `/api/trades/report` with a valid `TradeReportPayload` for a successful `BUY`. Body example:
            ```json
            {
                "user_wallet": "WalletToTestCRUD1",
                "tx_signature": "5...",
                "status": "success",
                "trade_type": "BUY",
                "mint_address": "MintToTestCRUD",
                "amount_token": 100.0,
                "price_sol": 0.0005,
                "total_sol": 0.05
            }
            ```
        *   Verify 200 OK response with `message: "Trade recorded successfully"`, `trade_id` set, `holding_updated: true`. Check logs, DB `trades` table, DB `holdings` table (new record or updated avg price/amount). Check "PHASE 6 TODO" log.
        *   Send a `POST` for a successful `SELL` for the *same* user/mint. Body example:
            ```json
            {
                "user_wallet": "WalletToTestCRUD1",
                "tx_signature": "6...",
                "status": "success",
                "trade_type": "SELL",
                "mint_address": "MintToTestCRUD",
                "amount_token": 50.0,
                "price_sol": 0.0010,
                "total_sol": 0.05
            }
            ```
        *   Verify 200 OK response, check logs, DB `trades` table (PNL should be calculated), DB `holdings` table (amount decreased).
        *   Send a `POST` with `status: "failure"`. Verify 200 OK response with `message: "Failed trade reported"`, check logs, verify DB was *not* changed.
        *   Test edge cases like selling the remaining amount.

---
This breaks Phase 4 into manageable, individually testable steps focusing first on the database structure, then the logic for managing holdings, then the logic for recording trades, and finally the API endpoint to tie it together.

---

Okay, excellent! Thank you for providing the complete codebase reflecting the successful implementation of Phase 4 (Holdings & Trades Tracking).

Now we move on to **Phase 5: Transaction Proposal (Backend -> Frontend)**. This phase is crucial for enabling the non-custodial trading flow via the user's Wallet Adapter.

**Goal:** Implement the mechanism for the backend (`run_user_bot_session`) to securely send transaction details (buy/sell proposals) to the specific user's connected frontend session, allowing the frontend to trigger the signing process via Phantom.

---

Okay, excellent! Thank you for providing the complete codebase reflecting the successful implementation of Phase 4 (Holdings & Trades Tracking).

Now we move on to **Phase 5: Transaction Proposal (Backend -> Frontend)**. This phase is crucial for enabling the non-custodial trading flow via the user's Wallet Adapter.

**Goal:** Implement the mechanism for the backend (`run_user_bot_session`) to securely send transaction details (buy/sell proposals) to the specific user's connected frontend session, allowing the frontend to trigger the signing process via Phantom.

---

**Phase 5: Transaction Proposal (Backend -> Frontend)**

---

**Step 5.1: WebSocket Connection Management**

*   **Goal:** Establish a way to track active WebSocket connections and associate them with authenticated user wallets, so the backend knows where to send proposals.
*   **Strategy:** We'll use a dictionary managed within the FastAPI `app.state` (or potentially move this to `UserBotManager` later if preferred, but `app.state` is simpler for now) to map `user_wallet` strings to active `WebSocket` connection objects. We'll create a new WebSocket endpoint for the frontend to connect to after authentication.
*   **Instructions for AI:**
    ```python
    # Goal: Implement WebSocket connection management for sending proposals.

    # 1. Modify `app/__init__.py`:
    #    - In `CustomFastAPI.__init__`:
    #        - Add state for managing active WebSocket connections:
    #          ```python
    #          from fastapi import WebSocket
    #          # ... other imports ...
    #          self.state.active_connections: Dict[str, WebSocket] = {} # user_wallet -> WebSocket
    #          self.state.connections_lock = asyncio.Lock() # Lock for accessing the dict
    #          ```
    #    - Ensure `WebSocket` is imported from `fastapi`.
    #    - Ensure `Dict` is imported from `typing`.

    # 2. Create `app/routers/websocket_router.py` (or add to an existing router file):
    #    - Import `APIRouter`, `WebSocket`, `WebSocketDisconnect`, `Depends`, `HTTPException`, `Request`.
    #    - Import `logging`, `asyncio`.
    #    - Import necessary components for *authentication* (assuming Phase 8 is not done yet, we'll use a placeholder - maybe require user_wallet as a query parameter for the WS connection initially).
    #    - `logger = logging.getLogger(__name__)`
    #    - Create router: `router = APIRouter(tags=["websockets"])`
    #    - Define WebSocket endpoint `/ws/{user_wallet}`:
    #      ```python
    #      @router.websocket("/ws/{user_wallet}")
    #      async def websocket_endpoint(websocket: WebSocket, user_wallet: str):
    #          # --- Placeholder Authentication ---
    #          # In a real app, you would validate a token passed via headers/query params first.
    #          # For MVP, we are trusting the user_wallet path parameter for now.
    #          logger.info(f"WebSocket connection attempt for user: {user_wallet}")
    #          # --- End Placeholder ---
    #
    #          await websocket.accept()
    #          logger.info(f"WebSocket accepted for user: {user_wallet}")
    #
    #          # Store the connection
    #          app = websocket.app # Get app instance from websocket
    #          async with app.state.connections_lock:
    #              # Optional: Disconnect previous connection if exists for this user
    #              # existing_ws = app.state.active_connections.get(user_wallet)
    #              # if existing_ws:
    #              #     try: await existing_ws.close(code=1000)
    #              #     except: pass # Ignore errors closing old socket
    #              app.state.active_connections[user_wallet] = websocket
    #              logger.info(f"Stored active WebSocket connection for user: {user_wallet}. Total: {len(app.state.active_connections)}")
    #
    #          try:
    #              # Keep the connection alive, listening for potential messages from client (optional)
    #              while True:
    #                  # You can receive messages here if needed, e.g., ping/pong or commands
    #                  # For now, just keep alive or handle disconnect
    #                  data = await websocket.receive_text()
    #                  logger.debug(f"Received message from {user_wallet}: {data}")
    #                  # Example echo: await websocket.send_text(f"Echo: {data}")
    #          except WebSocketDisconnect:
    #              logger.info(f"WebSocket disconnected for user: {user_wallet}")
    #          except Exception as e:
    #              logger.error(f"WebSocket error for user {user_wallet}: {e}", exc_info=True)
    #          finally:
    #              # Remove the connection on disconnect/error
    #              async with app.state.connections_lock:
    #                  # Check if the stored connection is the one that disconnected
    #                  stored_ws = app.state.active_connections.get(user_wallet)
    #                  if stored_ws is websocket:
    #                       app.state.active_connections.pop(user_wallet, None)
    #                       logger.info(f"Removed WebSocket connection for user: {user_wallet}. Total: {len(app.state.active_connections)}")
    #                  else:
    #                        logger.info(f"WebSocket for {user_wallet} already replaced or removed.")
    #      ```

    # 3. Modify `app/__init__.py` (or `app/routes.py`):
    #    - Import the `websocket_router` (adjust name as needed).
    #    - Include the router: `app.include_router(websocket_router)`.
    ```
*   **Files Involved:** `app/__init__.py`, `app/routers/websocket_router.py` (new, or merged), `app/routes.py` (if used).
*   **Testing:**
    1.  Run `python run.py`.
    2.  Use a WebSocket client tool (like Postman's WebSocket request feature, `websocat` command-line tool, or a simple Python `websockets` client script) to connect to `ws://localhost:8000/ws/TestWalletWS1`.
    3.  Check server logs: Verify "WebSocket connection attempt...", "WebSocket accepted...", "Stored active WebSocket connection..." messages appear for `TestWalletWS1`.
    4.  Connect *another* client to `ws://localhost:8000/ws/TestWalletWS2`. Verify logs for this second user. Check the "Total:" count in the logs reflects 2 active connections.
    5.  Disconnect the first client (`TestWalletWS1`). Verify server logs show "WebSocket disconnected..." and "Removed WebSocket connection..." for `TestWalletWS1`, and the total count drops to 1.
    6.  Disconnect the second client. Verify logs and total count becomes 0.

---

**Step 5.2: Implement Backend Sending Logic (Propose Transaction)**

*   **Goal:** Modify `run_user_bot_session` to find the user's WebSocket connection and send the `transaction_proposal` dictionary over it.
*   **Instructions for AI:**
    ```python
    # Goal: Send transaction proposals from run_user_bot_session via WebSocket.

    # 1. Modify `solbot/prediction.py` (`run_user_bot_session` function):
    #    - Ensure `app` instance is accessible (it might need to be passed down from the `_run_bot_wrapper` in `bot_manager.py` if not globally available).
    #       - Modify `app/bot_manager.py::_run_bot_wrapper`:
    #         - Accept `app` instance: `async def _run_bot_wrapper(self, user_wallet: str, user_config: dict, user_context: UserBotContext, app: FastAPI):`
    #         - Pass `app` when calling `run_user_bot_session`: `await run_user_bot_session(..., app=app)`
    #       - Modify `app/bot_manager.py::start_bot`: Pass `app` when creating the wrapper task: `bot_task = asyncio.create_task(self._run_bot_wrapper(..., app=app))` (requires `start_bot` to have access to `app`). Alternatively, store `app` ref in `UserBotManager.__init__`. Choose one approach.
    #       - Modify `solbot/prediction.py::run_user_bot_session` signature: `async def run_user_bot_session(..., app: FastAPI):`
    #    - Locate the "PHASE 5 TODO" placeholder where the `transaction_proposal` dictionary is created.
    #    - Replace the placeholder log with the actual sending logic:
    #      ```python
    #      # Inside the 'if passes_filters:' block in run_user_bot_session
    #      logger.info(f"[{user_wallet}] Filters passed for {mint_address}. Preparing BUY proposal.")
    #      buy_amount_sol = user_config.get('max_buy_sol', 0.01)
    #      transaction_proposal = {
    #          'type': 'buy', # Or 'sell' if implementing TP/SL trigger later
    #          'user_wallet': user_wallet,
    #          'mint_address': mint_address,
    #          'sol_amount': buy_amount_sol, # For buys
    #          # 'token_amount': ..., # For sells
    #          'slippage_bps': 1500 # Example
    #      }
    #
    #      # --- Send Proposal via WebSocket ---
    #      websocket_to_send = None
    #      try:
    #          # Acquire lock to safely access shared connections dict
    #          async with app.state.connections_lock:
    #              websocket_to_send = app.state.active_connections.get(user_wallet)
    #
    #          if websocket_to_send:
    #              logger.info(f"[{user_wallet}] Sending transaction proposal for {mint_address} via WebSocket.")
    #              # Prepare message payload (e.g., wrap proposal in a standard message format)
    #              message_payload = {
    #                  "message_type": "transaction_proposal",
    #                  "data": transaction_proposal
    #              }
    #              await websocket_to_send.send_json(message_payload) # Send as JSON
    #              logger.debug(f"[{user_wallet}] Proposal sent successfully.")
    #          else:
    #              logger.warning(f"[{user_wallet}] No active WebSocket connection found for user. Cannot send proposal for {mint_address}.")
    #              # TODO: How to handle this? Queue proposal? Discard? Log error?
    #
    #      except Exception as ws_err:
    #          logger.error(f"[{user_wallet}] Failed to send proposal via WebSocket: {ws_err}", exc_info=True)
    #          # TODO: Error handling if send fails. Retry? Mark user as disconnected?
    #      # --- End Send Proposal ---
    #      ```
    #    - Ensure necessary imports (`asyncio`, `logging`, `app.user_context.UserBotContext`, `fastapi.FastAPI`) are correct.
    ```
*   **Files Involved:** `solbot/prediction.py` (or wherever `run_user_bot_session` is), `app/bot_manager.py`.
*   **Testing:**
    1.  Run `python run.py`.
    2.  **Connect a WebSocket client** to `ws://localhost:8000/ws/YourTestWalletPG1` (use the same wallet address you use for API calls). Keep this connection active.
    3.  **Start the bot** for `YourTestWalletPG1` using `POST /api/bot/start?user_wallet=YourTestWalletPG1`. Verify the `run_user_bot_session` task starts and logs "Waiting for global buy signals...".
    4.  **Trigger a Global BUY Signal:** Manually trigger the global prediction process (e.g., via `completion_queue` or test function) for a mint address (`TestMint1`) ensuring `run_global_prediction` results in a "BUY" signal being broadcasted.
    5.  **Observe Server Logs:**
        *   Verify `run_global_prediction` logs broadcasting the signal.
        *   Verify `UserBotManager` logs sending the signal to `YourTestWalletPG1`.
        *   Verify `run_user_bot_session` logs receiving the signal.
        *   Verify `run_user_bot_session` applies filters (adjust mock data/filters if needed to ensure it passes).
        *   **CRITICAL:** Verify `run_user_bot_session` logs "Sending transaction proposal... via WebSocket." and "Proposal sent successfully."
    6.  **Observe WebSocket Client:** Your connected WebSocket client should receive a JSON message containing `{"message_type": "transaction_proposal", "data": {...}}` with the details matching the `transaction_proposal` dictionary.
    7.  **(Optional):** Test the case where no WebSocket is connected for the user when a signal arrives. Verify the "No active WebSocket connection found..." log message appears.

---

**Step 5.3 (Conceptual - No AI Prompt):**

*   This step involves **frontend development**. The AI cannot directly implement this.
*   **Tasks:** Implement the WebSocket client connection in your frontend framework (React, Vue, etc.). Listen for messages. When a `transaction_proposal` message arrives, parse it, use the Solana Wallet Adapter library (`@solana/wallet-adapter-react`, `@solana/web3.js`) to construct the transaction, prompt the user via Phantom (`signTransaction`/`sendTransaction`), and then call the `/api/trades/report` endpoint (from Phase 4.2) with the result.

---

This breakdown provides clear instructions for the backend parts of Phase 5. Remember to implement Step 5.3 on the frontend separately.

---

Okay, sounds like a plan. We'll consider Phase 5 (Steps 5.1 & 5.2 - WebSocket management and backend proposal sending) conceptually complete for now. If issues arise during integration or later phases, we can revisit.

Let's move on to **Phase 6: Price Monitor Service (Holdings & TP/SL)**.

**Goal:** Implement the background service that periodically fetches current market prices for tokens users are actually holding (recorded in the `Holdings` database table), updates the database with these prices, checks for Take Profit (TP) and Stop Loss (SL) conditions based on user configuration, and triggers the transaction proposal flow (Phase 5) for SELL orders when conditions are met.

---

**Phase 6: Price Monitor Service (Holdings & TP/SL)**

---

**Step 6.1: Create Price Monitor Service Structure**

*   **Goal:** Set up the basic structure for the background service and ensure it starts with the application.
*   **Instructions for AI:**
    ```python
    # Goal: Create the structure for the Price Monitor background service.

    # 1. Create `app/price_monitor.py`:
    #    - Import `asyncio`, `logging`, `FastAPI`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Define the main async function for the service:
    #      ```python
    #      async def run_price_monitor(app: FastAPI):
    #          logger.info("Starting Price Monitor Service...")
    #          # Determine sleep interval (make configurable later if needed)
    #          check_interval_seconds = 10 # Example: check every 10 seconds
    #          logger.info(f"Price check interval set to {check_interval_seconds} seconds.")
    #
    #          while True:
    #              try:
    #                  logger.debug("Price Monitor Service loop starting...")
    #                  # --- TODO: Add core logic in Step 6.2 ---
    #                  logger.debug("--- Placeholder: Fetching holdings ---")
    #                  await asyncio.sleep(0.1) # Simulate work
    #                  logger.debug("--- Placeholder: Fetching prices ---")
    #                  await asyncio.sleep(0.1) # Simulate work
    #                  logger.debug("--- Placeholder: Checking TP/SL ---")
    #                  await asyncio.sleep(0.1) # Simulate work
    #
    #                  logger.debug(f"Price Monitor Service loop finished. Sleeping for {check_interval_seconds}s...")
    #                  await asyncio.sleep(check_interval_seconds)
    #
    #              except asyncio.CancelledError:
    #                  logger.info("Price Monitor Service shutting down...")
    #                  break # Exit the loop cleanly on cancellation
    #              except Exception as e:
    #                  logger.exception(f"Error in Price Monitor Service loop: {e}")
    #                  # Avoid tight loop on unexpected errors
    #                  await asyncio.sleep(check_interval_seconds * 2) # Sleep longer after error
    #      ```

    # 2. Modify `app/__init__.py`:
    #    - Import `run_price_monitor` from `app.price_monitor`.
    #    - In the `lifespan` function, add a new task creation call during startup:
    #      ```python
    #      # Inside lifespan(app), after other task creations
    #      asyncio.create_task(run_price_monitor(app))
    #      logger.debug("Price Monitor Service task scheduled.")
    #      ```
    ```
*   **Files Involved:** `app/price_monitor.py` (new), `app/__init__.py` (modified).
*   **Testing:**
    1.  Run `python run.py`.
    2.  Check the startup logs. Verify the "Price Monitor Service task scheduled." message appears.
    3.  Observe the logs over the next minute or so. Verify that the "Price Monitor Service loop starting...", the placeholder messages, and the "Price Monitor Service loop finished. Sleeping..." messages appear roughly every 10 seconds (or your chosen interval). This confirms the background task is running correctly.

---

**Step 6.2: Implement Price Fetching Loop Logic**

*   **Goal:** Query the database for active holdings, identify unique mints, and implement the logic to fetch current market prices for those mints.
*   **Strategy:** We need to decide whether to use RPC calls or a Price API. **For the MVP, using a Price API (like Birdeye, if available and reliable for Pump.fun) is generally much simpler.** We will proceed with the assumption of using a Price API. If Birdeye/etc isn't viable, we'd need a different prompt for direct RPC state reading. *You will need to obtain an API key if required by the chosen service.*
*   **Instructions for AI:**
    ```python
    # Goal: Implement the logic within the Price Monitor Service to fetch holdings
    # from the DB and get current market prices using the Moralis Solana Token Price API.

    # 1. Add Configuration:
    #    - Ensure the Moralis API Key is in `.env`: `MORALIS_API_KEY=YOUR_MORALIS_API_KEY`
    #    - Add variables to `solbot/config.py`:
    #      ```python
    #      MORALIS_API_KEY = os.getenv("MORALIS_API_KEY")
    #      MORALIS_PRICE_ENDPOINT = "https://solana-gateway.moralis.io/token/mainnet/{address}/price" # V2 endpoint uses path param
    #      if not MORALIS_API_KEY:
    #          logger.warning("MORALIS_API_KEY environment variable not set. Price fetching will fail.")
    #      ```
    #    - Ensure `os` and `logger` are imported in `config.py`.

    # 2. Modify `app/price_monitor.py`:
    #    - Add necessary imports: `datetime`, `aiohttp`, `logging`, `asyncio`, `json`.
    #    - Import DB components: `async_session_factory`, `select` (from `sqlalchemy`), `Holding`.
    #    - Import Moralis config: `MORALIS_API_KEY`, `MORALIS_PRICE_ENDPOINT`.
    #    - Replace the `TODO` block inside the `run_price_monitor` loop with the following logic:
    #      ```python
    #      # --- Core Logic ---
    #      all_holdings: List[Holding] = []
    #      unique_mints_to_fetch = set()
    #
    #      # 1. Get all current holdings from DB
    #      try:
    #          async with async_session_factory() as db:
    #              # Fetch all holdings across all users
    #              query = select(Holding) # Select all Holding objects
    #              result = await db.execute(query)
    #              all_holdings = result.scalars().all()
    #
    #          if not all_holdings:
    #              logger.debug("No active holdings found to monitor.")
    #              await asyncio.sleep(check_interval_seconds)
    #              continue
    #
    #          unique_mints_to_fetch = {h.token_mint for h in all_holdings}
    #          logger.info(f"Found {len(all_holdings)} holdings across {len(unique_mints_to_fetch)} unique mints to check prices for.")
    #
    #      except Exception as db_err:
    #          logger.error(f"Error fetching holdings from DB: {db_err}", exc_info=True)
    #          await asyncio.sleep(check_interval_seconds * 2) # Longer sleep on DB error
    #          continue # Skip this cycle
    #
    #      # 2. Fetch current prices for unique mints using Moralis API
    #      mint_prices: Dict[str, float] = {} # mint_address -> price_usd
    #      if unique_mints_to_fetch and MORALIS_API_KEY: # Check if API key exists
    #          try:
    #              # Get the shared aiohttp session from app state
    #              http_session = app.state.http_session
    #              if not http_session:
    #                   logger.error("aiohttp ClientSession not found in app state.")
    #                   await asyncio.sleep(check_interval_seconds)
    #                   continue
    #
    #              headers = {"accept": "application/json", "X-API-Key": MORALIS_API_KEY}
    #              price_tasks = []
    #              for mint in unique_mints_to_fetch:
    #                  # Use a helper function to fetch price for one mint
    #                  price_tasks.append(fetch_single_price_moralis(http_session, MORALIS_PRICE_ENDPOINT, headers, mint))
    #
    #              results = await asyncio.gather(*price_tasks, return_exceptions=True)
    #
    #              for mint, result in zip(unique_mints_to_fetch, results):
    #                  if isinstance(result, Exception):
    #                      logger.warning(f"Failed to fetch Moralis price for {mint}: {result}")
    #                  elif result is not None:
    #                      mint_prices[mint] = result # Store the float price
    #
    #              logger.info(f"Fetched Moralis prices for {len(mint_prices)} / {len(unique_mints_to_fetch)} mints.")
    #
    #          except Exception as api_err:
    #              logger.error(f"Error fetching prices from Moralis API: {api_err}", exc_info=True)
    #              # Continue loop, TP/SL check will be skipped if no prices
    #      elif not MORALIS_API_KEY:
    #          logger.error("Cannot fetch prices, MORALIS_API_KEY is not set.")
    #
    #      # 3. Update DB & Check TP/SL (Implement in Step 6.3)
    #      if mint_prices:
    #          logger.info(f"--- PHASE 6.3 TODO: Update DB prices and check TP/SL ---")
    #          logger.debug(f"Fetched prices: {mint_prices}")
    #      else:
    #           logger.warning("No prices were successfully fetched in this cycle.")
    #
    #      # --- End Core Logic ---
    #      ```
    #    - **Add Helper Function:** Define `async def fetch_single_price_moralis(session: aiohttp.ClientSession, base_url_template: str, headers: dict, mint: str) -> float | None:` outside `run_price_monitor`.
    #      - This function takes session, URL template, headers, and mint.
    #      - Constructs the specific URL using f-string: `url = base_url_template.format(address=mint)`.
    #      - Performs the `session.get(url, headers=headers, timeout=10)` call.
    #      - Handles potential non-200 status codes (log error, return None). Handle 429 specifically (log warning, return None - rely on main loop sleep).
    #      - Parses the JSON response. Look for the `usdPrice` field.
    #      - Returns the price as a float if successful, `None` otherwise. Include robust error handling.
    #      ```python
    #      async def fetch_single_price_moralis(session: aiohttp.ClientSession, base_url_template: str, headers: dict, mint: str) -> float | None:
    #          url = base_url_template.format(address=mint)
    #          try:
    #              async with session.get(url, headers=headers, timeout=10) as response:
    #                  if response.status == 200:
    #                      try:
    #                          data = await response.json()
    #                          price_str = data.get('usdPrice') # Moralis uses usdPrice
    #                          if price_str is not None:
    #                              try:
    #                                   # Moralis price might already be float or need conversion
    #                                   return float(price_str)
    #                              except (ValueError, TypeError) as e:
    #                                   logger.error(f"Could not convert Moralis price '{price_str}' to float for {mint}. Error: {e}")
    #                                   return None
    #                          else:
    #                              logger.warning(f"Field 'usdPrice' not found for {mint} in Moralis response: {data}")
    #                              return None
    #                      except json.JSONDecodeError:
    #                          logger.warning(f"Failed to decode JSON response for {mint} from Moralis URL: {url}")
    #                          return None
    #                  elif response.status == 429:
    #                       logger.warning(f"Rate limited (429) fetching price for {mint} from Moralis.")
    #                       return None # Rely on main loop interval
    #                  else:
    #                       logger.warning(f"Moralis API request for {mint} failed: Status {response.status}, URL: {url}, Response: {await response.text()}")
    #                       return None
    #          except asyncio.TimeoutError:
    #               logger.warning(f"Timeout fetching price for {mint} from Moralis URL: {url}")
    #               return None
    #          except aiohttp.ClientError as e:
    #               logger.error(f"Client error fetching price for {mint} from Moralis URL: {url}: {e}")
    #               return None
    #          except Exception as e:
    #               logger.error(f"Unexpected error fetching price for {mint} from Moralis URL: {url}: {e}", exc_info=True)
    #               return None
    #      ```
        ```
    *   **Files Involved:** `.env` (add API key), `solbot/config.py` (add API key/endpoint vars), `app/price_monitor.py` (modified), `app/crud/holding_crud.py` (import).
    *   **Testing:**
        
        1.  **Manually add a holding** to your database using a test script (e.g., for user `TestWalletPG1` and a known active Pump.fun mint address).
        2.  Run `python run.py`.
        3.  Observe logs:
            *   Verify the Price Monitor service starts.
            *   Verify it logs "Found X holdings...".
            *   Verify it logs attempts to fetch prices using the helper function.
            *   Verify it logs successfully fetched prices or warnings/errors if fetching fails.
            *   Verify the "PHASE 6.3 TODO" log appears if prices were fetched.
        4.  Check database `holdings` table - the `current_price_sol` column should *not* be updated yet.

    ---

**Step 6.3: Implement DB Update & TP/SL Check**

*   **Goal:** Update the `current_price_sol` in the database for each holding and check if TP/SL conditions are met based on user config.
*   **Instructions for AI:**
    ```python
    # Goal: Update DB with fetched prices and check TP/SL conditions.

    # 1. Modify `app/price_monitor.py` (`run_price_monitor` function):
    #    - Import necessary CRUD: `config_crud`.
    #    - Replace the "PHASE 6.3 TODO" block with the following logic:
    #      ```python
    #      # 3. Update DB & Check TP/SL
    #      if mint_prices:
    #          logger.debug(f"Processing {len(all_holdings)} holdings against {len(mint_prices)} fetched prices.")
    #          # Create tasks for updating DB and checking TP/SL for each holding
    #          processing_tasks = []
    #          async with async_session_factory() as db: # Use one session for this batch
    #              # Fetch all relevant configurations in one go (optional optimization)
    #              user_wallets_with_holdings = {h.user_wallet for h in all_holdings}
    #              user_configs = {}
    #              for wallet in user_wallets_with_holdings:
    #                   config = await config_crud.get_configuration_by_user(db, wallet)
    #                   user_configs[wallet] = config # Store config or None if not found
    #
    #              for holding in all_holdings:
    #                  current_price = mint_prices.get(holding.token_mint)
    #                  if current_price is not None:
    #                      # Update DB holding price (use holding.id)
    #                      await holding_crud.update_holding_current_price(db, holding.id, current_price)
    #
    #                      # Check TP/SL
    #                      user_config_model = user_configs.get(holding.user_wallet)
    #                      if user_config_model:
    #                          # Convert config model to dict if needed, or access attributes directly
    #                          user_config = {c.name: getattr(user_config_model, c.name) for c in user_config_model.__table__.columns}
    #                          # Pass relevant data to a checking function
    #                          processing_tasks.append(
    #                               check_holding_tp_sl(app, holding, current_price, user_config)
    #                          )
    #                      else:
    #                          logger.warning(f"No config found for user {holding.user_wallet}, skipping TP/SL check for {holding.token_mint}")
    #                  else:
    #                      logger.debug(f"No current price fetched for {holding.token_mint}, cannot check TP/SL.")
    #
    #          # Run TP/SL checks concurrently
    #          if processing_tasks:
    #              await asyncio.gather(*processing_tasks)
    #
    #      else:
    #           logger.warning("No prices were successfully fetched in this cycle.")
    #      ```
    #    - **Add Helper Function:** Define `async def check_holding_tp_sl(app: FastAPI, holding: Holding, current_price: float, user_config: dict):` outside `run_price_monitor`.
    #      - This function performs the TP/SL logic for a single holding.
    #      - ```python
    #        async def check_holding_tp_sl(app: FastAPI, holding: Holding, current_price: float, user_config: dict):
    #            user_wallet = holding.user_wallet
    #            mint_address = holding.token_mint
    #            avg_buy_price = holding.average_buy_price_sol
    #            amount_held = holding.amount_held
    #
    #            tp_percent = user_config.get('tp_percent')
    #            sl_percent = user_config.get('sl_percent')
    #
    #            sell_reason = None
    #
    #            # Check TP
    #            if tp_percent is not None and avg_buy_price > 0:
    #                tp_price = avg_buy_price * (1 + tp_percent / 100.0)
    #                if current_price >= tp_price:
    #                    logger.info(f"[{user_wallet}] TAKE PROFIT triggered for {mint_address}. Current: {current_price}, TP: {tp_price}")
    #                    sell_reason = "TP"
    #
    #            # Check SL (only if TP not hit)
    #            if sell_reason is None and sl_percent is not None and avg_buy_price > 0:
    #                 sl_price = avg_buy_price * (1 - sl_percent / 100.0)
    #                 if current_price <= sl_price:
    #                      logger.info(f"[{user_wallet}] STOP LOSS triggered for {mint_address}. Current: {current_price}, SL: {sl_price}")
    #                      sell_reason = "SL"
    #
    #            # If sell triggered, prepare and send proposal (Phase 6.4)
    #            if sell_reason:
    #                 logger.info(f"--- PHASE 6.4 TODO: Trigger SELL proposal ---")
    #                 # Construct transaction_proposal for SELL
    #                 # proposal = {'type': 'sell', 'user_wallet': user_wallet, ...}
    #                 # await trigger_sell_proposal(app, proposal) # Need to implement this function
    #        ```
    ```
*   **Files Involved:** `app/price_monitor.py`, `app/crud/holding_crud.py` (import), `app/crud/config_crud.py` (import).
*   **Testing:**
    1.  Keep the holding record in the DB from the previous step.
    2.  Run `python run.py`.
    3.  Observe logs:
        *   Verify price fetching still occurs.
        *   Verify the "Processing X holdings..." log message appears.
        *   Verify logs indicating the `current_price_sol` is being updated (you might need to add debug logs in `update_holding_current_price`).
        *   Verify logs indicating TP/SL checks are happening.
    4.  **Check Database:** Using pgAdmin, check the `holdings` table. The `current_price_sol` column should now be populated with the recently fetched price.
    5.  **(Manual Trigger):** Manually edit the `average_buy_price_sol` in the `holdings` table and the `tp_percent`/`sl_percent` in the `configurations` table so that the *next* price fetched by the monitor *will* trigger a TP or SL condition.
    6.  Observe logs again. Verify the "TAKE PROFIT triggered" or "STOP LOSS triggered" message appears, followed by the "PHASE 6.4 TODO" log.

---

**Step 6.4: Trigger Sell Proposal**

*   **Goal:** When a TP/SL condition is met, construct the sell proposal and send it via the WebSocket mechanism developed in Phase 5.
*   **Instructions for AI:**
    ```python
    # Goal: Trigger sell proposals via WebSocket when TP/SL is hit.

    # 1. Modify `app/price_monitor.py`:
    #    - Import the helper function `test_send_proposal` from `test_step_5_2_proposal.py` for now,
    #      OR preferably, refactor the actual sending logic from Step 5.2 into a reusable
    #      utility function (e.g., `app/websocket_utils.py::send_proposal_to_user`)
    #      that both `run_user_bot_session` and `check_holding_tp_sl` can import and use.
    #      Let's assume we create `app/websocket_utils.py::send_proposal_to_user(app, user_wallet, proposal_dict)`.
    #
    #    - **Refactor Sending Logic (Create `app/websocket_utils.py`):**
    #      ```python
    #      # In app/websocket_utils.py
    #      import asyncio
    #      import logging
    #      import json
    #      from fastapi import FastAPI, WebSocket
    #
    #      logger = logging.getLogger(__name__)
    #
    #      async def send_proposal_to_user(app: FastAPI, user_wallet: str, proposal_dict: dict) -> bool:
    #          """Sends a transaction proposal JSON to a specific user's WebSocket."""
    #          websocket_to_send: WebSocket | None = None
    #          try:
    #              async with app.state.connections_lock:
    #                  websocket_to_send = app.state.active_connections.get(user_wallet)
    #
    #              if websocket_to_send:
    #                  logger.info(f"[{user_wallet}] Sending proposal ({proposal_dict.get('type')}) for {proposal_dict.get('mint_address')} via WebSocket.")
    #                  message_payload = {
    #                      "message_type": "transaction_proposal",
    #                      "data": proposal_dict
    #                  }
    #                  await websocket_to_send.send_json(message_payload)
    #                  logger.debug(f"[{user_wallet}] Proposal sent successfully.")
    #                  return True
    #              else:
    #                  logger.warning(f"[{user_wallet}] No active WebSocket found. Cannot send proposal: {proposal_dict}")
    #                  return False
    #          except Exception as ws_err:
    #              logger.error(f"[{user_wallet}] Failed to send proposal via WebSocket: {ws_err}", exc_info=True)
    #              # Attempt to close broken socket?
    #              if websocket_to_send:
    #                  try: await websocket_to_send.close()
    #                  except: pass
    #              # Remove potentially broken connection
    #              async with app.state.connections_lock:
    #                   app.state.active_connections.pop(user_wallet, None)
    #              return False
    #      ```
    #    - **Import the utility** in `app/price_monitor.py`: `from app.websocket_utils import send_proposal_to_user`
    #    - **Modify `check_holding_tp_sl`:**
    #        - Replace the "PHASE 6.4 TODO" block with:
    #        ```python
    #        # Inside check_holding_tp_sl, within the 'if sell_reason:' block
    #        logger.info(f"[{user_wallet}] Preparing SELL proposal for {mint_address} due to {sell_reason}.")
    #        # Construct the SELL proposal
    #        sell_proposal = {
    #            'type': 'sell',
    #            'user_wallet': user_wallet,
    #            'mint_address': mint_address,
    #            'token_amount': amount_held, # Propose selling the entire current holding
    #            'slippage_bps': user_config.get('sell_slippage_bps', 1500), # Get/set slippage from config
    #            'reason': sell_reason
    #        }
    #        # Send the proposal
    #        send_ok = await send_proposal_to_user(app, user_wallet, sell_proposal)
    #        if send_ok:
    #            logger.info(f"[{user_wallet}] Successfully sent SELL proposal for {mint_address}.")
    #            # TODO: Maybe mark holding as "sell pending" in DB to avoid resending?
    #        else:
    #             logger.error(f"[{user_wallet}] FAILED to send SELL proposal for {mint_address}.")
    #        ```
    # 2. **Modify `solbot/prediction.py` (`run_user_bot_session`):**
    #    - Import `send_proposal_to_user` from `app.websocket_utils`.
    #    - Replace the WebSocket sending block added in Step 5.2 with a call to the utility function:
    #      ```python
    #      # Inside run_user_bot_session, within the 'if passes_filters:' block
    #      # ... (construct transaction_proposal as before) ...
    #
    #      # Send proposal using the utility function
    #      send_ok = await send_proposal_to_user(app, user_wallet, transaction_proposal)
    #      if send_ok:
    #          logger.info(f"[{user_wallet}] Successfully sent BUY proposal for {mint_address}.")
    #      else:
    #          logger.error(f"[{user_wallet}] FAILED to send BUY proposal for {mint_address}.")
    #      ```
    ```
*   **Files Involved:** `app/price_monitor.py`, `solbot/prediction.py`, `app/websocket_utils.py` (new).
*   **Testing:**
    1.  Run `python run.py`.
    2.  Connect a WebSocket client for `ws://localhost:8000/ws/YourTestWalletPG1`.
    3.  Start the bot for `YourTestWalletPG1`.
    4.  Ensure a holding exists in the DB for this user/mint.
    5.  Manually adjust DB values (`average_buy_price_sol`, `tp_percent`/`sl_percent` in config) so that the next price check by the monitor *will* trigger a TP or SL.
    6.  Wait for the `PriceMonitorService` loop to run (default 10s).
    7.  Observe logs: Verify the "TAKE PROFIT triggered" or "STOP LOSS triggered" message appears.
    8.  **Observe WebSocket Client:** Verify the client receives a JSON message with `message_type: "transaction_proposal"` and `data` containing the correct **SELL** proposal details (`type: 'sell'`, correct `mint_address`, `token_amount`, etc.).
    9.  Verify server logs show the proposal was sent successfully via the utility function.

---

Okay, excellent! You've provided the codebase reflecting the completion of Phase 6 (Price Monitor Service).

Now, let's move on to **Phase 7: Dashboard API Endpoint**.

**Goal:** Create the API endpoint (`GET /api/dashboard/data`) that the frontend dashboard will call to retrieve all necessary data for a specific user, including their bot status, current holdings (with live P&L), and recent trade history.

---

**Phase 7: Dashboard API Endpoint**

---

**Step 7.1: Create Dashboard Router & Endpoint Structure**

*   **Goal:** Set up the new router file and the basic structure for the dashboard data endpoint.
*   **Instructions for AI:**
    ```python
    # Goal: Create the dashboard router and endpoint structure.

    # 1. Create `app/schemas/dashboard_schemas.py`:
    #    - Import `BaseModel`, `List`, `Optional` from `typing`.
    #    - Import `BotStateResponse` from `app.schemas.bot_schemas`.
    #    - Import `TradeItem` from `app.schemas.trade_schemas`.
    #    - Define `HoldingItem` Pydantic model (for dashboard response):
    #      ```python
    #      class HoldingItem(BaseModel):
    #          model_config = ConfigDict(from_attributes=True) # If using Pydantic v2+
    #          # class Config: orm_mode = True # If using Pydantic v1
    #
    #          token_mint: str
    #          amount_held: float
    #          average_buy_price_sol: float
    #          current_price_sol: Optional[float] = None
    #          live_pnl_percent: Optional[float] = None # Calculated field
    #          last_acquired_at: datetime
    #          updated_at: datetime
    #      ```
    #    - Define `DashboardDataResponse` Pydantic model:
    #      ```python
    #      class DashboardDataResponse(BaseModel):
    #          model_config = ConfigDict(arbitrary_types_allowed=True)
    #
    #          bot_state: BotStateResponse
    #          holdings: List[HoldingItem] = []
    #          recent_trades: List[TradeItem] = []
    #          # Add other summary stats later if needed (e.g., total PNL)
    #      ```
    #    - Ensure `ConfigDict` or `Config` is imported correctly based on Pydantic version. Ensure `datetime` is imported.

    # 2. Create `app/routers/dashboard_router.py`:
    #    - Import `APIRouter`, `Depends`, `HTTPException`, `Request`.
    #    - Import `AsyncSession`, `get_db_session`.
    #    - Import relevant CRUD functions: `get_bot_state`, `get_holdings_by_user`, `get_recent_trades_by_user` (from `trade_crud` dict).
    #    - Import Pydantic models: `DashboardDataResponse`, `BotStateResponse`, `HoldingItem`, `TradeItem`, `BotStatusEnum`.
    #    - Import DB models: `Holding`, `Trade`.
    #    - Import `logging`, `datetime`.
    #    - `logger = logging.getLogger(__name__)`
    #    - Create router: `router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])`.
    #    - Define endpoint `GET /data`:
    #        - Decorator: `@router.get("/data", response_model=DashboardDataResponse)`
    #        - Signature: `async def get_dashboard_data(user_wallet: str, request: Request, db: AsyncSession = Depends(get_db_session))`
    #        - **(TODO - Auth):** `logger.warning("TODO: Implement real authentication for dashboard!")`
    #        - Add basic structure:
    #          ```python
    #          logger.debug(f"Fetching dashboard data for user: {user_wallet}")
    #          try:
    #              # --- TODO: Implement data fetching logic in Step 7.2 ---
    #              bot_state_data = None
    #              holdings_data = []
    #              trades_data = []
    #
    #              # Placeholder response:
    #              default_bot_state = BotStateResponse(user_wallet=user_wallet, status=BotStatusEnum.STOPPED, last_changed=datetime.utcnow())
    #
    #              return DashboardDataResponse(
    #                  bot_state=default_bot_state,
    #                  holdings=holdings_data,
    #                  recent_trades=trades_data
    #              )
    #
    #          except Exception as e:
    #              logger.exception(f"Error fetching dashboard data for {user_wallet}: {e}")
    #              raise HTTPException(status_code=500, detail="Failed to fetch dashboard data")
    #          ```

    # 3. Modify `app/__init__.py` (or `app/routes.py`):
    #    - Import the `dashboard_router`.
    #    - Include the router: `app.include_router(dashboard_router)`.
    ```
*   **Files Involved:** `app/schemas/dashboard_schemas.py` (new), `app/routers/dashboard_router.py` (new), `app/__init__.py` or `app/routes.py` (modified).
*   **Testing:**
    1.  Run `python run.py`.
    2.  Use `curl` to make a `GET` request to `/api/dashboard/data?user_wallet=YourTestWalletPG1`. use powershell commands
    3.  Verify the response status code is 200 OK.
    4.  Verify the response body matches the structure of `DashboardDataResponse` with default/empty values for `bot_state` (Stopped), `holdings`, and `recent_trades`.
    5.  Check server logs for the "Fetching dashboard data..." message and ensure no errors are logged for this basic structure.

---

**Step 7.2: Implement Dashboard Data Fetching Logic**

*   **Goal:** Populate the `/api/dashboard/data` endpoint by fetching the required data (state, holdings, trades) from the database using the existing CRUD functions. Calculate live P&L for holdings.
*   **Instructions for AI:**
    ```python
    # Goal: Implement data fetching logic for the dashboard endpoint.

    # 1. Modify `app/routers/dashboard_router.py`:
    #    - Locate the `get_dashboard_data` function.
    #    - Replace the "TODO: Implement data fetching logic" block with the actual logic:
    #      ```python
    #      # --- Data Fetching Logic ---
    #      # Fetch Bot State
    #      bot_state_db = await get_bot_state(db, user_wallet)
    #      if not bot_state_db:
    #          bot_state_data = BotStateResponse(user_wallet=user_wallet, status=BotStatusEnum.STOPPED, last_changed=datetime.utcnow())
    #      else:
    #          # Use Pydantic's from_orm (v1) or from_attributes (v2)
    #          bot_state_data = BotStateResponse.model_validate(bot_state_db) # Pydantic v2+
    #          # bot_state_data = BotStateResponse.from_orm(bot_state_db) # Pydantic v1
    #
    #      # Fetch Holdings
    #      holdings_db: List[Holding] = await holding_crud.get_holdings_by_user(db, user_wallet)
    #      holdings_data: List[HoldingItem] = []
    #      for holding in holdings_db:
    #          live_pnl = None
    #          if holding.current_price_sol is not None and holding.average_buy_price_sol > 1e-12: # Avoid division by zero
    #               live_pnl = ((holding.current_price_sol / holding.average_buy_price_sol) - 1) * 100.0
    #
    #          # Use Pydantic's model_validate or from_orm
    #          holding_item = HoldingItem.model_validate(holding) # Pydantic v2+
    #          # holding_item = HoldingItem.from_orm(holding) # Pydantic v1
    #          holding_item.live_pnl_percent = live_pnl # Assign calculated PNL
    #          holdings_data.append(holding_item)
    #
    #      # Fetch Recent Trades (use the trade_crud dictionary)
    #      trades_db: List[Trade] = await trade_crud['get_recent_trades_by_user'](db, user_wallet, limit=50) # Use configured limit
    #      # Use Pydantic's model_validate or from_orm for list items
    #      trades_data: List[TradeItem] = [TradeItem.model_validate(trade) for trade in trades_db] # Pydantic v2+
    #      # trades_data: List[TradeItem] = [TradeItem.from_orm(trade) for trade in trades_db] # Pydantic v1
    #
    #      # --- End Data Fetching Logic ---
    #
    #      # Replace Placeholder response with actual data:
    #      return DashboardDataResponse(
    #          bot_state=bot_state_data,
    #          holdings=holdings_data,
    #          recent_trades=trades_data
    #      )
    #      ```
    #    - Ensure all necessary CRUD functions (`get_bot_state`, `holding_crud.get_holdings_by_user`, `trade_crud['get_recent_trades_by_user']`) and models (`Holding`, `Trade`, Pydantic models) are imported correctly.
    ```
*   **Files Involved:** `app/routers/dashboard_router.py`.
*   **Testing:**
    1.  **Prerequisite:** Ensure you have some data in your database for a test user (`YourTestWalletPG1`):
        *   A `BotState` record (e.g., set status to `RUNNING` via the API).
        *   At least one `Holding` record (created via `POST /api/trades/report` in Phase 4 testing, or manually added). Make sure its `current_price_sol` column is populated (you might need to run the Price Monitor service from Phase 6 briefly or set it manually in the DB for this test).
        *   At least one `Trade` record (created via `POST /api/trades/report`).
    2.  Run `python run.py`.
    3.  Use `curl` or Postman to make a `GET` request to `/api/dashboard/data?user_wallet=YourTestWalletPG1`.
    4.  Verify the response status code is 200 OK.
    5.  **Verify Response Body:**
        *   Check that `bot_state` reflects the current status from the DB.
        *   Check that the `holdings` list contains items matching the `HoldingItem` schema for the records in the DB. Verify the `live_pnl_percent` is calculated correctly based on `current_price_sol` and `average_buy_price_sol`.
        *   Check that the `recent_trades` list contains items matching the `TradeItem` schema for the records in the DB, ordered by timestamp descending.

---

This breaks Phase 7 into creating the endpoint structure/schemas and then implementing the data fetching logic, with tests for each step. After successfully implementing and testing Step 7.2, the backend part of the dashboard functionality will be complete.

---

Okay, let's integrate the Sign-In with Solana (SIWS) standard properly into Phase 8. This requires modifying Step 8.3 significantly to perform the specific SIWS message parsing and verification steps on the backend using Python libraries.

---

**Phase 8: Real Authentication (SIWS & JWT) - Revised**

*(Goal: Replace placeholder authentication with secure SIWS flow, verified on the backend, issuing JWTs for session management.)*

---

**Step 8.1: Install Dependencies & Configure JWT**

*   **Goal:** Install necessary libraries for JWT handling and SIWS signature verification, and set up basic JWT configuration.
*   **Instructions for AI:**
    ```bash
    # Goal: Install JWT & SIWS verification libraries and set up JWT configuration.

    # 1. Ensure necessary libraries are installed in requirements.txt and install:
    #    - `python-jose[cryptography]` (for JWT)
    #    - `passlib` (for potential future hashing needs)
    #    - `solders` (likely already installed, provides Pubkey, Signature)
    #    - `pynacl` (likely already installed via solders/solana-py, provides VerifyKey)
    # Example command:
    # pip install "python-jose[cryptography]" passlib solders pynacl

    # 2. Modify/Create `.env`:
    #    - Ensure JWT settings are present:
    #      ```dotenv
    #      # ... other vars ...
    #      JWT_SECRET_KEY=YOUR_RANDOMLY_GENERATED_32_BYTE_HEX_SECRET
    #      JWT_ALGORITHM=HS256
    #      JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60 # e.g., 1 hour
    #      ```
    #    - **IMPORTANT:** Replace placeholder with a real secure key.

    # 3. Modify/Create `solbot/config.py` (or `app/core/config.py`):
    #    - Import `os`, `logging`.
    #    - Load JWT settings from environment variables:
    #      ```python
    #      JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")
    #      JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
    #      JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "60"))
    #
    #      logger = logging.getLogger(__name__) # Ensure logger exists
    #      if not JWT_SECRET_KEY:
    #          logger.critical("FATAL ERROR: JWT_SECRET_KEY not set in environment variables!")
    #          raise ValueError("JWT_SECRET_KEY must be set for application security.")
    #      ```
    ```
*   **Files Involved:** `requirements.txt`, `.env`, `solbot/config.py` or `app/core/config.py`.
*   **Testing:**
    1.  Update `requirements.txt` if needed. Run `pip install -r requirements.txt`.
    2.  Generate and set a strong `JWT_SECRET_KEY` in `.env`.
    3.  Run `python run.py`. Verify startup success and no errors related to JWT config.

---

**Step 8.2: SIWS Challenge/Nonce Generation Endpoint**

*   **Goal:** Create the `/api/auth/challenge` endpoint to provide a unique nonce for the SIWS message. Store the nonce server-side temporarily.
*   **Instructions for AI:**
    ```python
    # Goal: Create endpoint to generate SIWS nonce and manage nonce lifecycle.

    # 1. Modify `app/__init__.py`:
    #    - In `CustomFastAPI.__init__`:
    #        - Ensure nonce state exists:
    #          ```python
    #          import uuid
    #          from typing import Dict
    #          # ... other state ...
    #          self.state.siws_nonces: Dict[str, float] = {} # nonce -> expiry_timestamp
    #          self.state.siws_nonce_lock = asyncio.Lock()
    #          ```

    # 2. Modify `app/routers/auth_router.py`:
    #    - Import `uuid`, `time`, `Request`.
    #    - Add the endpoint `GET /challenge`:
    #      ```python
    #      @router.get("/challenge")
    #      async def get_siws_challenge(request: Request):
    #          logger.debug("SIWS challenge requested.")
    #          app = request.app
    #          nonce = str(uuid.uuid4().hex) # Use hex for alphanumeric nonce
    #          # Make nonce validity shorter, e.g., 2 minutes
    #          expiry_seconds = 120
    #          expiry = time.time() + expiry_seconds
    #
    #          async with app.state.siws_nonce_lock:
    #              # --- Nonce Cleanup (More Robust) ---
    #              current_time = time.time()
    #              # Create a new dict comprehension for non-expired nonces
    #              valid_nonces = {n: exp for n, exp in app.state.siws_nonces.items() if exp >= current_time}
    #              # Assign the cleaned dict back
    #              app.state.siws_nonces = valid_nonces
    #              # ------------------------------------
    #
    #              # Store new nonce
    #              app.state.siws_nonces[nonce] = expiry
    #              logger.info(f"Generated SIWS nonce: {nonce}, valid for {expiry_seconds}s. Stored nonces: {len(app.state.siws_nonces)}")
    #
    #          return {"nonce": nonce}
    #      ```
    ```
*   **Files Involved:** `app/__init__.py`, `app/routers/auth_router.py`.
*   **Testing:**
    1.  Run `python run.py`.
    2.  Call `GET /api/auth/challenge` multiple times. Verify unique alphanumeric nonces are returned.
    3.  Check logs for nonce generation and count of stored nonces.
    4.  (Optional) Wait longer than the expiry time (e.g., > 2 minutes), call again, and check logs to see if the count decreased, indicating cleanup.

---

**Step 8.3: Implement SIWS Verification Logic & Update Verify Endpoint**

*   **Goal:** Implement the core SIWS message parsing and signature verification logic in Python and update the `/api/auth/verify` endpoint to use it, issuing a JWT on success.
*   **Instructions for AI:**
    ```python
    # Goal: Implement SIWS verification logic and update /api/auth/verify.

    # 1. Modify `app/core/security.py` (or create if it doesn't exist):
    #    - Add necessary imports: `logging`, `re`, `time`, `datetime`, `base64`, `asyncio`.
    #    - Import from `solders.pubkey`: `Pubkey`.
    #    - Import from `solders.signature`: `Signature`.
    #    - Import from `nacl.signing`: `VerifyKey`. (From pynacl library).
    #    - Import from `nacl.exceptions`: `BadSignatureError`.
    #    - Ensure `create_access_token` function (from Step 8.1) is defined here.
    #    - Add the `parse_siws_message` function (adapt regex/splitting as needed for robustness):
    #      ```python
    #      logger = logging.getLogger(__name__) # Ensure logger
    #
    #      def parse_siws_message(message: str) -> dict | None:
    #          # Regex approach might be more robust than simple split
    #          # Example EIP-4361 like structure (adjust based on actual frontend message)
    #          pattern = re.compile(
    #              r"^(?P<domain>[^\s]+) wants you to sign in with your Solana account:\n"
    #              r"(?P<address>[a-zA-Z0-9]{32,44})\n"
    #              r"(\n(?P<statement>[^\n]+)\n)?(?: # Optional statement group
    #              r"\n(?:" # Optional advanced fields group
    #              r"(?:URI: (?P<uri>[^\n]+)\n)?"
    #              r"(?:Version: (?P<version>[^\n]+)\n)?"
    #              r"(?:Chain ID: (?P<chain_id>[^\n]+)\n)?"
    #              r"(?:Nonce: (?P<nonce>[^\n]+)\n)?"
    #              r"(?:Issued At: (?P<issued_at>[^\n]+)\n)?"
    #              r"(?:Expiration Time: (?P<expiration_time>[^\n]+)\n)?"
    #              r"(?:Not Before: (?P<not_before>[^\n]+)\n)?"
    #              r"(?:Request ID: (?P<request_id>[^\n]+)\n)?"
    #              r"(?:Resources:\n(?P<resources>(?:- [^\n]+\n?)+))?"
    #              r")?)?$", re.MULTILINE
    #          )
    #          match = pattern.match(message)
    #          if not match:
    #              logger.warning("SIWS message did not match expected pattern.")
    #              return None
    #          data = match.groupdict()
    #          # Further process resources if needed
    #          if data.get('resources'):
    #              data['resources'] = [res.strip()[2:] for res in data['resources'].strip().split('\n')]
    #          # Check mandatory fields derived from pattern (address, domain should always match)
    #          if not data.get('address') or not data.get('domain'):
    #              return None
    #          return data
    #      ```
    #    - Add the **async** `verify_siws_message` function:
    #      ```python
    #      async def verify_siws_message(
    #          message: str,
    #          signature_b64: str,
    #          expected_address: str,
    #          app_state # Pass app state containing nonce store and lock
    #          ) -> bool:
    #          """Verifies a SIWS message and signature."""
    #          try:
    #              # 1. Parse the message
    #              parsed_data = parse_siws_message(message)
    #              if not parsed_data: return False
    #              logger.debug(f"Parsed SIWS data: {parsed_data}")
    #
    #              # 2. Check Domain, Address, URI, ChainID etc. against expectations
    #              # (Add checks based on your security requirements, e.g., check domain/URI)
    #              if parsed_data.get('address') != expected_address:
    #                  logger.warning("SIWS verification failed: Address mismatch.")
    #                  return False
    #
    #              # 3. Check Nonce (Existence and Expiry) - Use lock
    #              nonce = parsed_data.get('nonce')
    #              if not nonce:
    #                   logger.warning("SIWS verification failed: Nonce missing in message.")
    #                   return False
    #
    #              nonce_valid = False
    #              async with app_state.siws_nonce_lock:
    #                  nonce_expiry = app_state.siws_nonces.get(nonce)
    #                  if nonce_expiry is not None:
    #                      if time.time() <= nonce_expiry:
    #                          # Nonce is valid and not expired, consume it
    #                          del app_state.siws_nonces[nonce]
    #                          nonce_valid = True
    #                          logger.debug(f"SIWS nonce '{nonce}' validated and consumed.")
    #                      else:
    #                          logger.warning(f"SIWS verification failed: Nonce '{nonce}' expired.")
    #                          # Optionally remove expired nonce here too
    #                          app_state.siws_nonces.pop(nonce, None)
    #                  else:
    #                      logger.warning(f"SIWS verification failed: Nonce '{nonce}' not found or already used.")
    #
    #              if not nonce_valid: return False
    #
    #              # 4. Check Timestamps (issuedAt, expirationTime, notBefore) - Implement these checks!
    #              # Example: Check issuedAt (+- 5/10 min tolerance)
    #              # Example: Check expirationTime > now
    #              # Example: Check notBefore < now
    #              # Add logging for failures
    #              logger.info("TODO: Implement SIWS timestamp verification (issuedAt, expirationTime, notBefore)")
    #
    #              # 5. Verify Signature
    #              pubkey = Pubkey.from_string(expected_address)
    #              signature_bytes = base64.b64decode(signature_b64)
    #              # Ensure only the 64-byte signature is used
    #              if len(signature_bytes) != 64:
    #                  logger.error(f"Invalid signature length: {len(signature_bytes)}")
    #                  return False
    #              signature_obj = Signature(signature_bytes)
    #
    #              verify_key = VerifyKey(bytes(pubkey))
    #              signed_message_bytes = message.encode('utf-8')
    #
    #              verify_key.verify(signed_message_bytes, bytes(signature_obj)) # Throws BadSignatureError if invalid
    #              logger.info(f"SIWS signature verified successfully for {expected_address}")
    #              return True
    #
    #          except BadSignatureError:
    #              logger.warning(f"SIWS signature verification failed (BadSignatureError) for {expected_address}")
    #              return False
    #          except ImportError:
    #               logger.error("PyNaCl not installed. Cannot verify SIWS signature.")
    #               return False
    #          except ValueError as e: # E.g., invalid base64 or pubkey string
    #              logger.warning(f"SIWS value error during verification: {e}")
    #              return False
    #          except Exception as e:
    #              logger.exception(f"Unexpected error during SIWS verification: {e}")
    #              return False
    #      ```

    # 2. Modify `app/routers/auth_router.py`:
    #    - Update imports: `Request`, `verify_siws_message`, `create_access_token`.
    #    - Update `AuthVerifyPayload` schema definition:
    #      ```python
    #      class AuthVerifyPayload(BaseModel):
    #          walletAddress: str
    #          message: str # The full SIWS message that was signed by frontend
    #          signature: str # Base64 encoded signature from frontend
    #          # Nonce might be redundant if parsed from message, but can be passed for pre-check
    #      ```
    #    - Update `AuthVerifyResponse` schema definition:
    #       ```python
    #       class AuthVerifyResponse(BaseModel):
    #           access_token: str
    #           token_type: str = "bearer" # Default to bearer
    #           walletAddress: str
    #       ```
    #    - Modify the `POST /verify` endpoint logic:
    #      ```python
    #      @router.post("/verify", response_model=AuthVerifyResponse)
    #      async def verify_siws_login(
    #          payload: AuthVerifyPayload,
    #          request: Request, # Get request to access app state
    #          db: AsyncSession = Depends(get_db_session)
    #      ):
    #          logger.info(f"Attempting SIWS verification for {payload.walletAddress}")
    #          app = request.app
    #
    #          # Verify the signature and message details
    #          is_valid = await verify_siws_message(
    #              message=payload.message,
    #              signature_b64=payload.signature,
    #              expected_address=payload.walletAddress,
    #              app_state=app.state # Pass app state for nonce checking
    #          )
    #
    #          if not is_valid:
    #              logger.warning(f"SIWS verification failed for {payload.walletAddress}")
    #              raise HTTPException(
    #                   status_code=status.HTTP_401_UNAUTHORIZED,
    #                   detail="SIWS verification failed. Invalid signature, message, or nonce."
    #              )
    #
    #          # Verification successful, ensure user exists in DB
    #          user = await user_crud.get_or_create_user(db, payload.walletAddress)
    #
    #          # Generate JWT access token
    #          access_token = create_access_token(subject=user.wallet_address)
    #
    #          logger.info(f"SIWS verification successful, JWT issued for {user.wallet_address}")
    #          return AuthVerifyResponse(
    #              access_token=access_token,
    #              walletAddress=user.wallet_address
    #          )
    #      ```
    #    - Ensure `status` is imported from `fastapi`.
    ```
*   **Files Involved:** `app/core/security.py` (new/modified), `app/schemas/auth_schemas.py` (modified), `app/routers/auth_router.py` (modified), `app/__init__.py` (state access).
*   **Testing:**
    1.  Run `python run.py`.
    2.  **Manual SIWS Flow Simulation (as in previous step 8.3 testing):** ## TO-DO ##
        *   Get nonce from `GET /api/auth/challenge`.
        *   Construct the *exact* SIWS message string your frontend *will* construct.
        *   Sign the UTF-8 bytes of that message using an external tool/script with a known private key. Base64 encode the 64-byte signature.
        *   Send a `POST` request to `/api/auth/verify` with `walletAddress`, the full `message`, and the base64 `signature`.
        *   Verify you get a 200 OK response with the JWT (`access_token`, `token_type`, `walletAddress`).
        *   Check server logs for successful verification messages.
        *   Verify the nonce used was removed from `app.state.siws_nonces`.
        *   Test failure cases again (bad signature, used/invalid nonce, mismatched address). Verify 401 Unauthorized responses.

---

**Step 8.4: Create JWT Validation Dependency**

*   **Goal:** Create the FastAPI dependency (`get_current_user`) to validate JWTs from the `Authorization: Bearer` header.
*   **Instructions for AI:**
    ```python
    # Goal: Create FastAPI dependency for JWT validation.

    # 1. Create `app/dependencies.py` (or use `app/core/security.py`):
    #    - Add imports: `Optional`, `Union`, `HTTPException`, `status`, `Depends`, `Request`.
    #    - Import `OAuth2PasswordBearer` from `fastapi.security`.
    #    - Import `jwt`, `JWTError` from `jose`.
    #    - Import JWT config: `JWT_SECRET_KEY`, `JWT_ALGORITHM`.
    #    - Import DB components: `AsyncSession`, `get_db_session`.
    #    - Import user CRUD: `user_crud`.
    #    - Import `User` model.
    #    - Define the OAuth2 scheme. The `tokenUrl` should conceptually point to where a user logs in to *get* a token. `/api/auth/verify` is appropriate here.
    #      `oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/verify")`
    #    - Define `async def get_current_user(token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db_session)) -> User:`
    #      ```python
    #      credentials_exception = HTTPException(
    #          status_code=status.HTTP_401_UNAUTHORIZED,
    #          detail="Could not validate credentials",
    #          headers={"WWW-Authenticate": "Bearer"},
    #      )
    #      try:
    #          # Decode JWT
    #          payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
    #          # Extract subject (wallet address)
    #          wallet_address: str | None = payload.get("sub")
    #          if wallet_address is None:
    #              logger.warning("JWT token missing 'sub' (subject) field.")
    #              raise credentials_exception
    #
    #          # Optional: Check token expiry ('exp' claim is handled automatically by jwt.decode)
    #
    #      except JWTError as e: # Catches expired signature, invalid signature, etc.
    #          logger.warning(f"JWT validation error: {e}")
    #          raise credentials_exception from e
    #
    #      # Fetch user from database based on wallet address in token
    #      user = await user_crud.get_by_wallet_address(db, wallet_address=wallet_address)
    #      if user is None:
    #          # This case should be rare if tokens are only issued after user creation,
    #          # but handles scenarios where user might be deleted after token issuance.
    #          logger.warning(f"User associated with JWT token not found in DB: {wallet_address}")
    #          raise credentials_exception
    #
    #      # logger.debug(f"Authenticated user via JWT: {user.wallet_address}")
    #      return user
    #      ```
    #    - (Optional) Define `async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:` if you need active/inactive checks later. For now, it can just `return current_user`.
    ```
*   **Files Involved:** `app/dependencies.py` (new) or `app/core/security.py` (modified).
*   **Testing:** DONE
    1.  Unit Testing: Create `tests/test_dependencies.py`.
        *   Use `pytest-asyncio`. Mock DB session and `user_crud`.
        *   Generate a valid JWT using `create_access_token`.
        *   Call `await get_current_user(token=valid_token, db=mock_db)`. Assert the correct mock `User` object is returned.
        *   Test with an invalid token (bad signature, expired, missing 'sub'). Assert `HTTPException` with status 401 is raised.
        *   Test with a valid token but mock `user_crud.get_by_wallet_address` to return `None`. Assert `HTTPException` 401.

---

**Step 8.5: Protect API Endpoints & Adapt WebSocket Auth**

*   **Goal:** Secure the user-specific API endpoints using the `get_current_user` dependency and adapt WebSocket authentication to use a token passed as a query parameter.
*   **Instructions for AI:**
    ```python
    # Goal: Protect endpoints and adapt WebSocket authentication.

    # 1. Modify Routers (`config_router.py`, `bot_router.py`, `trade_router.py`, `dashboard_router.py`):
    #    - Import `User` from `app.db_models`.
    #    - Import `get_current_user` from `app.dependencies` (or `app.core.security`).
    #    - For **all HTTP endpoints** currently using a `user_wallet: str` parameter (query or path):
    #        - **Remove** the `user_wallet: str` parameter.
    #        - **Add** the dependency: `current_user: User = Depends(get_current_user)`.
    #        - **Replace** internal uses of `user_wallet` with `current_user.wallet_address`.
    #    - **Example (`config_router.py::get_config`):**
    #      ```python
    #      # Already shown in previous prompt, verify implementation.
    #      # Signature: async def get_config(db: AsyncSession = Depends(get_db_session), current_user: User = Depends(get_current_user)):
    #      # Usage: config = await config_crud.update_or_create_configuration(db, current_user.wallet_address, {})
    #      ```
    #    - **Example (`trade_router.py::report_trade_result`):**
    #       - The `payload: TradeReportPayload` likely still contains `user_wallet`. We should verify it matches the authenticated user.
    #       ```python
    #       async def report_trade_result(
    #           payload: TradeReportPayload,
    #           request: Request,
    #           db: AsyncSession = Depends(get_db_session),
    #           current_user: User = Depends(get_current_user) # Add dependency
    #       ):
    #           # Check if payload wallet matches authenticated user
    #           if payload.user_wallet != current_user.wallet_address:
    #               logger.error(f"Auth mismatch: Token for {current_user.wallet_address}, payload for {payload.user_wallet}")
    #               raise HTTPException(status_code=403, detail="Wallet address in payload does not match authenticated user")
    #           # ... rest of the logic using payload.user_wallet or current_user.wallet_address ...
    #       ```

    # 2. Modify WebSocket Auth (`app/routers/websocket_router.py`):
    #    - Import `Query`, `status` from `fastapi`.
    #    - Import `get_current_user` dependency, `User` model, `AsyncSession`, `get_db_session`.
    #    - Modify `@router.websocket("/ws/{user_wallet}")` to `@router.websocket("/ws")`.
    #    - Modify the `websocket_endpoint` function signature:
    #      `async def websocket_endpoint(websocket: WebSocket, token: str | None = Query(None), db: AsyncSession = Depends(get_db_session))`
    #    - **Add authentication logic at the beginning:**
    #      ```python
    #      user: User | None = None
    #      user_wallet: str | None = None
    #      try:
    #          if not token:
    #              raise HTTPException(status_code=status.WS_1008_POLICY_VIOLATION, detail="Missing authentication token")
    #          # Call dependency function directly with token and db session
    #          user = await get_current_user(token=token, db=db)
    #          user_wallet = user.wallet_address
    #          logger.info(f"WebSocket authenticated for user: {user_wallet}")
    #      except HTTPException as http_exc: # Catch auth errors from get_current_user
    #           logger.warning(f"WebSocket authentication failed: {http_exc.detail}")
    #           await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
    #           return
    #      except Exception as auth_err: # Catch other potential errors
    #           logger.error(f"Unexpected WebSocket auth error: {auth_err}", exc_info=True)
    #           await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
    #           return
    #
    #      # If auth successful, user and user_wallet are set
    #      await websocket.accept()
    #      logger.info(f"WebSocket accepted for authenticated user: {user_wallet}")
    #      # ... rest of the WebSocket handling logic using the authenticated user_wallet ...
    #      ```
    #    - Ensure `get_current_user` dependency is adapted to handle the explicitly passed `token` string correctly (as discussed in Step 8.4 prompt).

    # 3. Review all protected endpoints and the WebSocket endpoint to ensure authentication is enforced and `current_user.wallet_address` is used.
    ```
*   **Files Involved:** All router files (`auth_router.py`, `config_router.py`, `bot_router.py`, `trade_router.py`, `dashboard_router.py`, `websocket_router.py`), `app/dependencies.py` (or `app/core/security.py`).
*   **Testing:**
    1.  Run `python run.py`.
    2.  **Get Token:** Perform the SIWS flow (Steps 8.2 & 8.3 tests) to get a valid JWT `access_token`.
    3.  **Test Protected HTTP Endpoint (No Token):** Attempt to call `GET /api/config/` *without* the `Authorization` header. Verify it fails with 401 Unauthorized.
    4.  **Test Protected HTTP Endpoint (With Token):** Call `GET /api/config/` *with* the correct header: `Authorization: Bearer YOUR_ACCESS_TOKEN`. Verify you get a 200 OK response with the correct user's config.
    5.  **Test WebSocket (No Token):** Use a WebSocket client to connect to `ws://localhost:8000/ws`. Verify the connection is immediately closed by the server (likely code 1008).
    6.  **Test WebSocket (With Token):** Connect to `ws://localhost:8000/ws?token=YOUR_ACCESS_TOKEN`. Verify the connection is accepted and logs show the correct authenticated user wallet.

---

This detailed breakdown for Phase 8 covers JWT setup, SIWS nonce generation, SIWS verification, JWT dependency creation, and finally protecting the endpoints. Remember that the SIWS verification logic in Python (Step 8.3) is the most complex part and needs careful implementation based on the EIP-4361 standard and your chosen frontend message format.