import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium tracking-tighter transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "rounded-full bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl",
        destructive: "rounded-full bg-destructive text-white hover:bg-destructive/90 shadow-lg hover:shadow-xl",
        outline: "rounded-full border border-border/30 bg-background/20 backdrop-blur-sm text-foreground/90 hover:bg-foreground/10 hover:text-foreground hover:border-border/50",
        secondary: "rounded-full bg-secondary text-white hover:bg-secondary/90 shadow-lg hover:shadow-xl",
        ghost: "rounded-full text-foreground/70 hover:bg-foreground/10 hover:text-foreground backdrop-blur-sm",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        accent: "rounded-full bg-accent text-white hover:bg-accent/90 shadow-lg hover:shadow-xl",
        glass: "rounded-full bg-white/5 border border-white/10 text-white hover:bg-white/10 backdrop-blur-sm shadow-lg",
        neonPrimary: "rounded-full bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl",
        neonDestructive: "rounded-full bg-destructive text-white hover:bg-destructive/90 shadow-lg hover:shadow-xl",
        hero: "rounded-full bg-primary text-white hover:bg-primary/90 shadow-lg hover:shadow-xl relative",
      },
      size: {
        default: "h-10 px-6 py-2",
        sm: "h-9 px-4 text-xs",
        lg: "h-12 px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  showArrow?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, showArrow = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"

    // Show arrow for hero variant by default, or when explicitly requested
    const shouldShowArrow = variant === "hero" || showArrow

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {children}
        {shouldShowArrow && (
          <div className="ml-2 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
            <ChevronRight className="w-3 h-3" />
          </div>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
