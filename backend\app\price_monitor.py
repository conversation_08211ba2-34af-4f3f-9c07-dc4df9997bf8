import asyncio
import logging
import json
import aiohttp
from datetime import datetime, timezone
from fastapi import FastAPI
from sqlalchemy import select, and_
from app.database import async_session_factory
from app.db_models import Holding
from solbot.config import MORALIS_API_KEY, MORALIS_ENDPOINT_URL_TEMPLATE, MORALIS_BATCH_SIZE, MORALIS_BATCH_ENDPOINT, PRICE_CHECK_INTERVAL_SECONDS, moralis_api_key_manager
from app.crud.holding_crud import holding_crud
from app.crud.config_crud import config_crud
from app.websocket_utils import send_proposal_to_user

logger = logging.getLogger(__name__)


async def fetch_batch_prices_moralis(session: aiohttp.ClientSession, headers: dict, mint_addresses: list[str], batch_size: int = None) -> dict[str, dict]:
    """
    Fetch prices for multiple tokens using Moralis batch endpoint.
    Returns dict mapping mint_address -> price_data
    """
    if batch_size is None:
        batch_size = MORALIS_BATCH_SIZE

    all_results = {}

    # Split into batches if needed
    num_batches = (len(mint_addresses) + batch_size - 1) // batch_size
    logger.info(f"MORALIS BATCH API: Processing {len(mint_addresses)} tokens in {num_batches} batch(es) (batch size: {batch_size})")
    for i in range(0, len(mint_addresses), batch_size):
        batch = mint_addresses[i:i + batch_size]
        batch_results = await _fetch_single_batch_moralis(session, batch)
        all_results.update(batch_results)

    return all_results


async def _fetch_single_batch_moralis(session: aiohttp.ClientSession, mint_addresses: list[str]) -> dict[str, dict]:
    """
    Fetch prices for a single batch of tokens using Moralis batch endpoint with API key rotation.
    """
    url = MORALIS_BATCH_ENDPOINT
    payload = {"addresses": mint_addresses}

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Get API key and limiter from manager
            if moralis_api_key_manager:
                api_key, limiter = await moralis_api_key_manager.get_api_key_and_limiter()
            else:
                # Fallback to legacy single key
                api_key = MORALIS_API_KEY
                limiter = None

            if not api_key:
                logger.error("No Moralis API key available")
                return {}

            headers = {
                "accept": "application/json",
                "Content-Type": "application/json",
                "X-API-Key": api_key
            }

            # Use rate limiter if available
            if limiter:
                async with limiter:
                    async with session.post(url, headers=headers, json=payload, timeout=15) as response:
                        return await _process_moralis_batch_response(response, mint_addresses, api_key, attempt + 1, max_retries)
            else:
                async with session.post(url, headers=headers, json=payload, timeout=15) as response:
                    return await _process_moralis_batch_response(response, mint_addresses, api_key, attempt + 1, max_retries)

        except asyncio.TimeoutError:
            logger.warning(f"Timeout fetching batch prices from Moralis for {len(mint_addresses)} tokens (attempt {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            logger.error(f"Unexpected error fetching batch prices from Moralis (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)

    return {}


async def _process_moralis_batch_response(response, mint_addresses: list[str], api_key: str, attempt: int, max_retries: int) -> dict[str, dict]:
    """
    Process Moralis batch API response with proper error handling and key management.
    """
    try:
        text = await response.text()

        # Handle rate limiting and credit exhaustion
        if response.status == 429:
            logger.warning(f"MORALIS API: Rate limit (429) hit with key {api_key}. Marking temporarily unavailable.")
            if moralis_api_key_manager:
                await moralis_api_key_manager.mark_api_key_unavailable(api_key)
            return {}
        elif "credits exhausted" in text.lower() or "upgrade plan to continue" in text.lower():
            logger.warning(f"MORALIS API: Credits exhausted for key {api_key}. Marking unavailable with extended cooldown.")
            if moralis_api_key_manager:
                await moralis_api_key_manager.mark_api_key_unavailable(api_key, is_credit_exhausted=True)
            return {}

        if response.status == 200:
            try:
                data = await response.json()
                results = {}

                # Process each token in the response
                for token_data in data:
                    token_address = token_data.get('tokenAddress')
                    if not token_address:
                        continue

                    # Extract price information
                    price_info = {
                        'usd_price': None,
                        'sol_price': None,
                        'usd_price_24h': token_data.get('usdPrice24h'),
                        'usd_change_24h': token_data.get('usdPrice24hrUsdChange'),
                        'percent_change_24h': token_data.get('usdPrice24hrPercentChange'),
                        'exchange_name': token_data.get('exchangeName'),
                        'pair_address': token_data.get('pairAddress'),
                        'logo': token_data.get('logo'),
                        'name': token_data.get('name'),
                        'symbol': token_data.get('symbol')
                    }

                    # Get USD price
                    usd_price = token_data.get('usdPrice')
                    if usd_price is not None:
                        try:
                            price_info['usd_price'] = float(usd_price)
                        except (ValueError, TypeError):
                            logger.error(f"Could not convert Moralis usdPrice '{usd_price}' to float for {token_address}.")

                    # Get SOL price from nativePrice
                    native_price_data = token_data.get('nativePrice')
                    if native_price_data and 'value' in native_price_data and 'decimals' in native_price_data:
                        try:
                            price_str = native_price_data.get('value')
                            decimals = native_price_data.get('decimals', 9)  # Default to 9 for SOL
                            sol_price = float(price_str) / (10 ** decimals)
                            price_info['sol_price'] = sol_price
                        except (ValueError, TypeError):
                            logger.error(f"Could not convert Moralis nativePrice for {token_address}.")

                    results[token_address] = price_info
                    logger.debug(f"Moralis batch: {token_address} -> USD: ${price_info['usd_price']}, SOL: {price_info['sol_price']}")

                logger.info(f"MORALIS BATCH API: Batch successful with key {api_key} - fetched {len(results)} / {len(mint_addresses)} token prices")
                return results

            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON response from Moralis batch API: {e}")
                return {}
        else:
            logger.warning(f"Moralis batch API failed with key {api_key}: Status {response.status}, Response: {text}")
            return {}

    except Exception as e:
        logger.error(f"Error processing Moralis batch response: {e}")
        return {}


async def _fallback_individual_calls(session: aiohttp.ClientSession, mint_addresses: list[str]) -> dict[str, dict]:
    """
    Fallback to individual API calls when batch endpoint fails.
    Only used for small numbers of tokens to avoid excessive API usage.
    Uses API key rotation for individual calls too.
    """
    results = {}

    for mint in mint_addresses:
        try:
            # Get API key for individual call
            if moralis_api_key_manager:
                api_key, limiter = await moralis_api_key_manager.get_api_key_and_limiter()
            else:
                api_key = MORALIS_API_KEY
                limiter = None

            if not api_key:
                logger.error(f"No Moralis API key available for fallback call to {mint}")
                continue

            headers = {
                "accept": "application/json",
                "X-API-Key": api_key
            }

            url = MORALIS_ENDPOINT_URL_TEMPLATE.format(address=mint)

            # Use rate limiter if available
            if limiter:
                async with limiter:
                    async with session.get(url, headers=headers, params={"network": "mainnet"}, timeout=10) as response:
                        result = await _process_individual_fallback_response(response, mint, api_key)
                        if result:
                            results[mint] = result
            else:
                async with session.get(url, headers=headers, params={"network": "mainnet"}, timeout=10) as response:
                    result = await _process_individual_fallback_response(response, mint, api_key)
                    if result:
                        results[mint] = result

        except Exception as e:
            logger.warning(f"Fallback individual call failed for {mint}: {e}")
            continue

    logger.info(f"MORALIS INDIVIDUAL API: Fallback completed - fetched {len(results)} / {len(mint_addresses)} prices")
    return results


async def _process_individual_fallback_response(response, mint: str, api_key: str) -> dict:
    """
    Process individual Moralis API response for fallback calls.
    """
    try:
        text = await response.text()

        # Handle rate limiting and credit exhaustion
        if response.status == 429:
            logger.warning(f"Rate limited during fallback for {mint} with key {api_key}")
            if moralis_api_key_manager:
                await moralis_api_key_manager.mark_api_key_unavailable(api_key)
            return None
        elif "credits exhausted" in text.lower() or "upgrade plan to continue" in text.lower():
            logger.warning(f"Credits exhausted during fallback for {mint} with key {api_key}")
            if moralis_api_key_manager:
                await moralis_api_key_manager.mark_api_key_unavailable(api_key, is_credit_exhausted=True)
            return None

        if response.status == 200:
            data = await response.json()

            # Convert individual response to batch format
            price_info = {
                'usd_price': data.get('usdPrice'),
                'sol_price': None,
                'usd_price_24h': None,
                'usd_change_24h': None,
                'percent_change_24h': None,
                'exchange_name': None,
                'pair_address': None,
                'logo': None,
                'name': None,
                'symbol': None
            }

            # Get SOL price from nativePrice
            native_price_data = data.get('nativePrice')
            if native_price_data and 'value' in native_price_data and 'decimals' in native_price_data:
                try:
                    price_str = native_price_data.get('value')
                    decimals = native_price_data.get('decimals', 9)
                    sol_price = float(price_str) / (10 ** decimals)
                    price_info['sol_price'] = sol_price
                except (ValueError, TypeError):
                    pass

            return price_info
        else:
            logger.warning(f"Individual fallback failed for {mint} with key {api_key}: Status {response.status}")
            return None

    except Exception as e:
        logger.error(f"Error processing individual fallback response for {mint}: {e}")
        return None

async def check_holding_tp_sl(app: FastAPI, holding: Holding, current_price: float, user_config: dict):
    user_wallet = holding.user_wallet
    mint_address = holding.token_mint
    avg_buy_price = holding.average_buy_price_sol
    amount_held = holding.amount_held

    tp_percent = user_config.get('tp_percent')
    sl_percent = user_config.get('sl_percent')

    sell_reason = None

    # Check TP
    if tp_percent is not None and avg_buy_price > 0:
        tp_price = avg_buy_price * (1 + tp_percent / 100.0)
        if current_price >= tp_price:
            logger.info(f"[{user_wallet}] TAKE PROFIT triggered for {mint_address}. Current: {current_price}, TP: {tp_price}")
            sell_reason = "TP"

    # Check SL (only if TP not hit)
    if sell_reason is None and sl_percent is not None and avg_buy_price > 0:
        sl_price = avg_buy_price * (1 - sl_percent / 100.0)
        if current_price <= sl_price:
            logger.debug(f"[{user_wallet}] STOP LOSS triggered for {mint_address}. Current: {current_price}, SL: {sl_price}")
            sell_reason = "SL"

    # If sell triggered, prepare and send proposal (Phase 6.4)
    if sell_reason:
        logger.debug(f"[{user_wallet}] Preparing SELL proposal for {mint_address} due to {sell_reason}.")
        # Construct the SELL proposal
        sell_slippage = user_config.get('sell_slippage_bps', 150)  # Default 150 BPS (1.5%)
        sell_proposal = {
            'type': 'sell',
            'user_wallet': user_wallet,
            'mint_address': mint_address,
            'token_amount': amount_held, # Propose selling the entire current holding
            'slippage_bps': sell_slippage, # Use the configured value
            'reason': sell_reason
        }
        # Send the proposal
        send_ok = await send_proposal_to_user(app, user_wallet, sell_proposal)
        if send_ok:
            logger.debug(f"[{user_wallet}] Successfully sent SELL proposal for {mint_address}.")
            # TODO: Maybe mark holding as "sell pending" in DB to avoid resending?
        else:
            logger.error(f"[{user_wallet}] FAILED to send SELL proposal for {mint_address}.")

async def run_price_monitor(app: FastAPI):
    logger.info("Starting Price Monitor Service...")
    # Use configurable sleep interval
    check_interval_seconds = PRICE_CHECK_INTERVAL_SECONDS
    logger.info(f"Price check interval set to {check_interval_seconds} seconds.")

    while True:
        try:
            logger.debug("Price Monitor Service loop starting...")
            
            # --- Core Logic ---
            unique_mints_to_fetch = set()

            # 1. Get only distinct token mints with active monitoring
            try:
                async with async_session_factory() as db:
                    # Fetch only distinct token mints where monitoring_active is True
                    query = select(Holding.token_mint).where(Holding.monitoring_active == True).distinct()
                    result = await db.execute(query)
                    # Extract the unique mint strings
                    unique_mints_to_fetch = {row[0] for row in result.fetchall()}

                if not unique_mints_to_fetch:
                    logger.debug("No active holdings found to monitor.")
                    await asyncio.sleep(check_interval_seconds)
                    continue

                logger.info(f"Found {len(unique_mints_to_fetch)} unique mints with active monitoring to check prices for.")

            except Exception as db_err:
                logger.error(f"Error fetching token mints from DB: {db_err}", exc_info=True)
                await asyncio.sleep(check_interval_seconds * 2)  # Longer sleep on DB error
                continue  # Skip this cycle

            # 2. Fetch current prices for unique mints using Moralis batch API
            mint_prices: dict[str, float] = {}  # mint_address -> price_usd
            mint_price_data: dict[str, dict] = {}  # mint_address -> full_price_data
            failed_mints: set[str] = set()

            if unique_mints_to_fetch and (moralis_api_key_manager or MORALIS_API_KEY):  # Check if API keys exist
                try:
                    # Get the shared aiohttp session from app state
                    http_session = app.state.http_session
                    if not http_session:
                        logger.error("aiohttp ClientSession not found in app state.")
                        await asyncio.sleep(check_interval_seconds)
                        continue

                    # Use batch endpoint to fetch all prices in one or few API calls
                    mint_list = list(unique_mints_to_fetch)
                    logger.info(f"MORALIS API: Using BATCH endpoint for {len(mint_list)} tokens with key rotation")
                    batch_results = await fetch_batch_prices_moralis(http_session, mint_list)

                    # Only use fallback if batch endpoint is completely broken AND we have very few tokens
                    # (to avoid excessive individual API calls if batch service is down)
                    if not batch_results and len(mint_list) <= 5 and MORALIS_ENDPOINT_URL_TEMPLATE:
                        logger.warning(f"MORALIS API: Batch endpoint failed for {len(mint_list)} tokens, switching to INDIVIDUAL FALLBACK endpoint...")
                        batch_results = await _fallback_individual_calls(http_session, mint_list)
                        if batch_results:
                            logger.info(f"MORALIS API: INDIVIDUAL FALLBACK successful for {len(batch_results)} tokens")
                        else:
                            logger.error(f"MORALIS API: Both BATCH and INDIVIDUAL endpoints failed for {len(mint_list)} tokens")

                    # Process batch results
                    for mint in mint_list:
                        if mint in batch_results:
                            price_data = batch_results[mint]
                            usd_price = price_data.get('usd_price')
                            sol_price = price_data.get('sol_price')

                            # Store full price data for potential future use
                            mint_price_data[mint] = price_data

                            # For now, prioritize USD price, fallback to SOL price
                            if usd_price is not None:
                                mint_prices[mint] = usd_price
                            elif sol_price is not None:
                                # Could convert SOL to USD here if needed, for now use SOL price
                                mint_prices[mint] = sol_price
                                logger.debug(f"Using SOL price for {mint}: {sol_price} SOL")
                            else:
                                failed_mints.add(mint)
                                logger.warning(f"No valid price found for {mint} in batch response")
                        else:
                            failed_mints.add(mint)
                            logger.warning(f"Token {mint} not found in Moralis batch response")

                    endpoint_used = "BATCH" if not any("fallback" in str(batch_results).lower() for _ in [1]) else "MIXED"
                    logger.info(f"MORALIS API: Successfully fetched prices for {len(mint_prices)} / {len(unique_mints_to_fetch)} tokens using {endpoint_used} endpoint(s). {len(failed_mints)} failures.")

                except Exception as api_err:
                    logger.error(f"Error fetching prices from Moralis batch API: {api_err}", exc_info=True)
                    # Continue loop, TP/SL check will be skipped if no prices
            elif not MORALIS_API_KEY:
                logger.error("Cannot fetch prices, MORALIS_API_KEY is not set.")

            # 3. Update DB & Check TP/SL for all found prices
            if mint_prices or failed_mints:
                logger.debug(f"Processing {len(mint_prices)} successful price fetches and {len(failed_mints)} failed fetches")
                # Fetch configurations for all users just once (optimization)
                async with async_session_factory() as db:
                    # First, process mints with successful price fetches
                    for mint_address, current_price in mint_prices.items():
                        # Get all holding records for this mint where monitoring_active is True
                        holdings = await holding_crud.get_holdings_by_token_mint_with_active_monitoring(db, mint_address)
                        logger.debug(f"Found {len(holdings)} active holdings for {mint_address}")
                        
                        # Collect user wallets for this mint to fetch configs later
                        wallets_for_this_mint = {h.user_wallet for h in holdings}
                        user_configs = {}
                        
                        # Fetch configs for all users with holdings in this mint
                        for wallet in wallets_for_this_mint:
                            config = await config_crud.get_configuration_by_user(db, wallet)
                            user_configs[wallet] = config  # Store config or None if not found
                        
                        # Process each holding for the current mint
                        processing_tasks = []
                        for holding in holdings:
                            # Check if price has changed significantly
                            if holding.current_price_sol is None or abs(current_price - holding.current_price_sol) > 1e-12:
                                # Price changed or was not set before - update price and reset stagnant counter
                                logger.debug(f"Price changed for {mint_address}: {holding.current_price_sol} -> {current_price}. Resetting stagnant counter.")
                                await holding_crud.update_holding_with_price_change(db, holding.id, current_price)
                            else:
                                # Price hasn't changed - check if we should increment stagnant counter
                                should_increment = await holding_crud.should_increment_stagnant_count(db, holding.id)
                                
                                if should_increment:
                                    # Outside grace period and has previous successful fetches - increment counter
                                    logger.debug(f"Price unchanged for {mint_address} and outside grace period. Incrementing stagnant counter.")
                                    await holding_crud.increment_stagnant_check_count(db, holding.id)
                                else:
                                    # Within grace period or first successful fetch - update timestamp but don't increment
                                    logger.debug(f"Price unchanged for {mint_address} but in grace period or first fetch. Not incrementing counter.")
                                    await holding_crud.record_successful_price_fetch(db, holding.id, current_price)
                            
                            # Check TP/SL for all holdings regardless of price change
                            user_config = user_configs.get(holding.user_wallet)
                            if user_config:
                                # Convert config model to dict if needed
                                user_config_dict = {c.name: getattr(user_config, c.name) for c in user_config.__table__.columns}
                                # Queue up the TP/SL check task
                                processing_tasks.append(
                                    check_holding_tp_sl(app, holding, current_price, user_config_dict)
                                )
                            else:
                                logger.warning(f"No config found for user {holding.user_wallet}, skipping TP/SL check for {holding.token_mint}")
                        
                        # Run TP/SL checks for all holdings of this mint concurrently
                        if processing_tasks:
                            await asyncio.gather(*processing_tasks)
                    
                    # Now process mints that had API failures - these won't increment stagnant counters
                    for mint_address in failed_mints:
                        holdings = await holding_crud.get_holdings_by_token_mint_with_active_monitoring(db, mint_address)
                        logger.debug(f"Found {len(holdings)} active holdings for {mint_address} that had API fetch failures")
                        
                        for holding in holdings:
                            # Skip stagnant counter increment for API failures
                            grace_period_check = holding.last_acquired_at and (datetime.now(timezone.utc) - holding.last_acquired_at).total_seconds() < 20
                            
                            if grace_period_check:
                                logger.debug(f"Holding {mint_address} is within 20-second grace period. Skipping stagnant counter.")
                            else:
                                logger.debug(f"Price fetch failed for {mint_address}. Not incrementing stagnant counter.")
            else:
                logger.warning("No prices were successfully fetched in this cycle.")

            # --- End Core Logic ---
            
            logger.debug(f"Price Monitor Service loop finished. Sleeping for {check_interval_seconds}s...")
            await asyncio.sleep(check_interval_seconds)

        except asyncio.CancelledError:
            logger.info("Price Monitor Service shutting down...")
            break  # Exit the loop cleanly on cancellation
        except Exception as e:
            logger.exception(f"Error in Price Monitor Service loop: {e}")
            # Avoid tight loop on unexpected errors
            await asyncio.sleep(check_interval_seconds * 2)  # Sleep longer after error 