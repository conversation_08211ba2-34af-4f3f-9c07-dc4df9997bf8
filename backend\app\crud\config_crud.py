from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional, Dict, Any
from datetime import datetime

from app.db_models import Configuration
from app.crud.base_crud import BaseCRUD
from app.crud.user_crud import user_crud


class ConfigurationCRUD(BaseCRUD[Configuration]):
    """
    CRUD operations for the Configuration model.
    """
    
    def __init__(self):
        super().__init__(Configuration)
    
    async def get_configuration_by_user(self, db: AsyncSession, user_wallet: str) -> Optional[Configuration]:
        """
        Get a configuration by user wallet address.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The user's wallet address
        
        Returns:
            Configuration instance if found, None otherwise
        """
        query = select(Configuration).filter(Configuration.user_wallet == user_wallet)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def update_or_create_configuration(
        self, db: AsyncSession, user_wallet: str, config_data: Dict[str, Any]
    ) -> Configuration:
        """
        Update an existing configuration or create a new one.
        
        Args:
            db: AsyncSession for database operations
            user_wallet: The user's wallet address
            config_data: Dictionary with configuration field values
        
        Returns:
            Updated or newly created Configuration instance
        """
        # Ensure user exists
        await user_crud.get_or_create_user(db, user_wallet)
        
        # Try to get existing configuration
        config = await self.get_configuration_by_user(db, user_wallet)
        
        if config:
            # Update existing configuration
            for key, value in config_data.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            config.updated_at = datetime.utcnow()
            await db.commit()
            await db.refresh(config)
            return config
        else:
            # Create new configuration
            config_data["user_wallet"] = user_wallet
            new_config = Configuration(**config_data)
            db.add(new_config)
            await db.commit()
            await db.refresh(new_config)
            return new_config


# Create a singleton instance for reuse
config_crud = ConfigurationCRUD() 