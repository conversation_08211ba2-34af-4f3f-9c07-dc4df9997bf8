import { useState, useEffect } from 'react';

interface ScrollState {
  scrollY: number;
  scrollDirection: 'up' | 'down' | null;
  isAtTop: boolean;
  isScrolling: boolean;
}

export function useScroll() {
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollY: 0,
    scrollDirection: null,
    isAtTop: true,
    isScrolling: false,
  });

  useEffect(() => {
    let lastScrollY = window.scrollY;
    let scrollTimeout: NodeJS.Timeout;

    const updateScrollState = () => {
      const currentScrollY = window.scrollY;

      // More responsive direction detection - always update if there's any movement
      let direction: 'up' | 'down' | null = null;
      if (currentScrollY > lastScrollY + 1) {
        direction = 'down';
      } else if (currentScrollY < lastScrollY - 1) {
        direction = 'up';
      }

      setScrollState(prev => ({
        scrollY: currentScrollY,
        scrollDirection: direction || prev.scrollDirection,
        isAtTop: currentScrollY < 10,
        isScrolling: true,
      }));

      // Clear existing timeout
      clearTimeout(scrollTimeout);

      // Set scrolling to false after scroll stops
      scrollTimeout = setTimeout(() => {
        setScrollState(prev => ({
          ...prev,
          isScrolling: false,
        }));
      }, 100);

      lastScrollY = currentScrollY;
    };

    // More responsive scroll handling - no throttling for better responsiveness
    const handleScroll = () => {
      updateScrollState();
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Initial call
    updateScrollState();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  return scrollState;
}
