import asyncio
import os
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from configparser import ConfigPars<PERSON>
from aiolimiter import AsyncLimiter
import logging

logger = logging.getLogger(__name__)

class APIKeyManager:
    def __init__(self, keys, rate_limit=1, rate_period=1.5):
        self.api_keys = keys
        self.lock = asyncio.Lock()
        self.api_key_status = {key: True for key in keys}
        self.api_key_limiters = {key: AsyncLimiter(rate_limit, rate_period) for key in keys}
        self.current_key_index = 0
        self.api_key_cooldowns = {key: 0 for key in keys}
        self.credit_exhaustion_retries = {key: 0 for key in keys}

    async def get_api_key_and_limiter(self):
        async with self.lock:
            current_time = time.time()
            for key in self.api_keys:
                if not self.api_key_status[key] and self.api_key_cooldowns[key] <= current_time:
                    self.api_key_status[key] = True
                    logger.info(f"[API_MANAGER] Key {key} reactivated after cooldown.")

                    # Reset credit exhaustion counter after 7+ days of cooldown
                    # This helps with monthly credit renewals
                    cooldown_duration = current_time - (self.api_key_cooldowns[key] - (24 * 60 * 60))
                    if cooldown_duration >= (7 * 24 * 60 * 60):  # 7 days
                        if self.credit_exhaustion_retries[key] > 0:
                            logger.info(f"[API_MANAGER] Resetting credit exhaustion counter for {key} after long cooldown")
                            self.credit_exhaustion_retries[key] = 0
            available_keys = [key for key, available in self.api_key_status.items() if available]
            if not available_keys:
                min_cooldown = min(self.api_key_cooldowns.values())
                wait_time = max(0, min_cooldown - current_time)
                logger.debug(f"[API_MANAGER] All keys unavailable. Min cooldown ends at {min_cooldown}. Waiting {wait_time:.2f}s.")
                await asyncio.sleep(wait_time)
                return await self.get_api_key_and_limiter()
            key = available_keys[self.current_key_index % len(available_keys)]
            limiter = self.api_key_limiters[key]
            self.current_key_index += 1
            return key, limiter

    async def mark_api_key_unavailable(self, api_key, is_credit_exhausted=False):
        async with self.lock:
            if is_credit_exhausted:
                self.credit_exhaustion_retries[api_key] += 1
                logger.warning(f"[API_MANAGER] Marking key {api_key} UNAVAILABLE (Credits Exhausted). Retry count: {self.credit_exhaustion_retries[api_key]}.")

                # Credit exhaustion: Use longer cooldown instead of permanent unavailability
                # Credits typically renew every 30 days, so use 24-hour cooldown
                if self.credit_exhaustion_retries[api_key] >= 2:
                    # After 2 exhaustion attempts, use 24-hour cooldown
                    cooldown_duration = 24 * 60 * 60  # 24 hours in seconds
                    cooldown_end = time.time() + cooldown_duration
                    self.api_key_status[api_key] = False
                    self.api_key_cooldowns[api_key] = cooldown_end
                    logger.warning(f"[API_MANAGER] Key {api_key} unavailable for 24 hours due to credit exhaustion. Cooldown until: {cooldown_end:.2f}")
                else:
                    # First exhaustion attempt: Use 1-hour cooldown
                    cooldown_duration = 60 * 60  # 1 hour in seconds
                    cooldown_end = time.time() + cooldown_duration
                    self.api_key_status[api_key] = False
                    self.api_key_cooldowns[api_key] = cooldown_end
                    logger.warning(f"[API_MANAGER] Key {api_key} unavailable for 1 hour due to credit exhaustion. Cooldown until: {cooldown_end:.2f}")
            else:
                self.api_key_status[api_key] = False
                cooldown_end = time.time() + 15
                self.api_key_cooldowns[api_key] = cooldown_end
                logger.warning(f"[API_MANAGER] Marking key {api_key} UNAVAILABLE (Rate Limit/Other). Cooldown until: {cooldown_end:.2f} ({15}s).")

    async def add_api_keys(self, new_api_keys):
        async with self.lock:
            for key in new_api_keys:
                key = key.strip()
                if key and key not in self.api_keys:
                    self.api_keys.append(key)
                    self.api_key_status[key] = True
                    self.api_key_limiters[key] = AsyncLimiter(1, 1.1)
                    self.credit_exhaustion_retries[key] = 0
                    logger.info(f"Added new API key: {key}")

    async def reset_credit_exhaustion_counters(self):
        """Reset credit exhaustion counters for all keys (useful when credits are renewed)"""
        async with self.lock:
            for key in self.api_keys:
                if self.credit_exhaustion_retries[key] > 0:
                    logger.info(f"[API_MANAGER] Resetting credit exhaustion counter for key {key}")
                    self.credit_exhaustion_retries[key] = 0
                    # If key was unavailable due to credit exhaustion, make it available again
                    if not self.api_key_status[key] and self.api_key_cooldowns[key] > time.time():
                        self.api_key_status[key] = True
                        self.api_key_cooldowns[key] = 0
                        logger.info(f"[API_MANAGER] Reactivated key {key} after credit reset")

    async def get_api_key_status(self):
        """Get status of all API keys for monitoring"""
        async with self.lock:
            current_time = time.time()
            status = {}
            for key in self.api_keys:
                status[key] = {
                    'available': self.api_key_status[key],
                    'cooldown_until': self.api_key_cooldowns[key],
                    'cooldown_remaining': max(0, self.api_key_cooldowns[key] - current_time),
                    'credit_exhaustion_retries': self.credit_exhaustion_retries[key]
                }
            return status

class ConfigFileEventHandler(FileSystemEventHandler):
    def __init__(self, key_manager, config_path, loop):
        self.api_key_manager = key_manager
        self.config_path = config_path
        self.loop = loop

    def on_modified(self, event):
        if not event.is_directory and os.path.basename(event.src_path) == 'config.ini':
            logger.info(f"Config file modified: {event.src_path}")
            if self.loop.is_closed():
                logger.error("Event loop closed. Cannot reload API keys.")
                return
            asyncio.run_coroutine_threadsafe(self.reload_api_keys(), self.loop)

    async def reload_api_keys(self):
        try:
            local_config = ConfigParser()
            local_config.read(self.config_path)
            new_api_keys_str = local_config.get('SHYFT', 'API_KEYS')
            new_api_keys = [key.strip() for key in new_api_keys_str.split(',') if key.strip()]
            await self.api_key_manager.add_api_keys(new_api_keys)
            logger.info(f"Successfully reloaded API keys from {self.config_path}")
        except Exception as e:
            logger.error(f"Error reloading API keys: {str(e)}")

async def start_config_watcher(key_manager, watch_directory_path, loop=None):
    if loop is None:
        raise ValueError("An event loop must be provided to start_config_watcher.")
    
    # Check if the directory exists instead of creating it
    if not os.path.exists(watch_directory_path):
        logger.error(f"Config directory does not exist: {watch_directory_path}")
        return  # Early return if directory doesn't exist
    
    # Full path to the config.ini file
    config_file_path = os.path.join(watch_directory_path, 'config.ini')
    
    # Check if config file exists
    if not os.path.exists(config_file_path):
        logger.error(f"Config file does not exist: {config_file_path}")
        return  # Early return if config file doesn't exist
    
    event_handler = ConfigFileEventHandler(key_manager, config_file_path, loop)
    observer = Observer()
    
    # Schedule the observer to watch the directory containing the config file
    observer.schedule(event_handler, path=watch_directory_path, recursive=False)
    
    try:
        observer.start()
        logger.info(f"Started configuration file watcher on directory: {watch_directory_path}")
        
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        logger.info("Config watcher received cancellation signal")
        observer.stop()
    except Exception as e:
        logger.error(f"Error in config watcher: {str(e)}")
        observer.stop()
    
    observer.join()
    logger.info("Config watcher stopped")

def ensure_file_exists(file_path, default_content=None, headers=None):
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)
    if not os.path.exists(file_path):
        with open(file_path, 'w') as file:
            if default_content:
                file.write(default_content)
            elif headers:
                file.write(','.join(headers) + '\n')

def get_mint_address_from_transaction(tx):
    for action in tx.get("actions", []):
        action_type = action.get("type", "UNKNOWN")
        if action_type == "SWAP":
            swaps = action["info"].get("swaps", [])
            for swap in swaps:
                in_token = swap.get("in", {}).get("token_address")
                out_token = swap.get("out", {}).get("token_address")
                if in_token != "So11111111111111111111111111111111111111112":
                    return in_token
                elif out_token != "So11111111111111111111111111111111111111112":
                    return out_token
    return None

def token_type(token_address):
    sol_addresses = ["So11111111111111111111111111111111111111112"]
    return 0 if token_address in sol_addresses else 1