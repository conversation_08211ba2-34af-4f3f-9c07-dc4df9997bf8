# Model Files

This directory should contain:
- `xgboost.joblib` - The trained XGBoost model file

**Note**: The actual model file needs to be copied from the ml_pipeline artifacts directory.
The model file is a binary joblib file that cannot be recreated from text.

To complete the setup:
1. Copy `xgboost.joblib` from ml_pipeline/artifacts/run_20250626_135000/models/
2. Copy `feature_scaler.joblib` from ml_pipeline/artifacts/run_20250626_135000/preprocessing/

These are binary files that must be copied directly from the ml_pipeline.
