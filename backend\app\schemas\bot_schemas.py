from enum import Enum
from datetime import datetime
from pydantic import BaseModel, ConfigDict
from typing import Optional, Dict, Any


class BotStatusEnum(str, Enum):
    """Enumeration of possible bot status values."""
    STOPPED = 'Stopped'
    RUNNING = 'Running'
    ERROR = 'Error'


class BotStateResponse(BaseModel):
    """Pydantic model for bot state response in API."""
    user_wallet: str
    status: BotStatusEnum
    last_changed: datetime
    session_start_time: Optional[datetime] = None
    session_uptime_seconds: Optional[float] = None
    session_predicted_signals: Optional[int] = None
    session_buy_count: Optional[int] = None
    session_sell_count: Optional[int] = None
    total_open_positions: Optional[int] = None
    session_sol_invested: Optional[float] = None
    session_sol_received: Optional[float] = None
    session_pnl_sol: Optional[float] = None
    session_pnl_percent: Optional[float] = None
    
    model_config = ConfigDict(from_attributes=True)


class BotSettingsResponse(BaseModel):
    """Pydantic model for bot settings response."""
    user_wallet: str
    settings: Dict[str, Any]
    
    model_config = ConfigDict(from_attributes=True)


class BotStartResponse(BaseModel):
    """Pydantic model for bot start response."""
    user_wallet: str
    status: BotStatusEnum
    message: str = "Bot started successfully"
    
    model_config = ConfigDict(from_attributes=True)


class BotStopResponse(BaseModel):
    """Pydantic model for bot stop response."""
    user_wallet: str
    status: BotStatusEnum
    message: str = "Bot stopped successfully"
    
    model_config = ConfigDict(from_attributes=True) 