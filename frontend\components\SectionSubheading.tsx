import React from 'react';
import { cn } from '@/lib/utils';

interface SectionSubheadingProps {
  children: React.ReactNode;
  className?: string;
}

const SectionSubheading: React.FC<SectionSubheadingProps> = ({ children, className }) => {
  return (
    <h3 className={cn("subheading-text-style pb-2 border-b border-white/10 mb-4", className)}>
      {children}
    </h3>
  );
};

export default SectionSubheading; 