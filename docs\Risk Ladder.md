Okay, Phase 4 is complete! Now your backend should be packaging the live Risk Ladder stats with BUY proposals.

Here's a concise `README.md` section that outlines the key configurable parameters related to the new Dynamic Risk Ladder feature, where to find them, and a high-level description of how it works with relevant file names.

---

```markdown
# SolBot Windsurf - Dynamic Risk Ladder Feature

This document describes the Dynamic Risk Ladder feature, its configuration parameters, and how it operates within the backend system. The Risk Ladder provides users with historical performance context for BUY signal proposals, based on a dynamic pool of the model's recent resolved predictions.

## How It Works (High-Level Flow with Key Files)

1.  **BUY Signal Generation & Recording:**
    *   When the live ML model (XGBoost, logic in `backend/solbot/prediction.py` via `predict_internal` called by `run_global_prediction` in `backend/solbot/processing.py`) generates a BUY signal for a token:
        *   This proposal (mint address, timestamp, model version) is immediately recorded as a new entry in the `live_model_proposals` database table (defined in `backend/app/db_models.py` as `LiveModelProposals`). Initially, `is_resolved` is `False` and `actual_max_roi` is `NULL`.
        *   (File: `backend/solbot/processing.py` - within `run_global_prediction`)

2.  **Proposal Resolution (`ProposalResolverService`):**
    *   A background service (`ProposalResolverService` defined in `backend/app/proposal_resolver_service.py`) runs periodically.
    *   It queries the `live_model_proposals` table for entries where `is_resolved = False` and the `proposal_timestamp` is older than `PROPOSAL_RESOLUTION_DELAY_SECONDS` (`T_delay`).
    *   For each such "due" proposal, it calculates the `actual_max_roi` (profit factor) achieved by the token from its `proposal_timestamp` up to `proposal_timestamp + T_delay`.
        *   This calculation happens in `backend/solbot/roi_calculator.py` (`calculate_actual_max_roi` function), which involves fetching historical transaction data for the token during that specific window.
    *   The `live_model_proposals` record is then updated with `is_resolved = True`, the calculated `actual_max_roi`, and a `resolution_timestamp`.
    *   (Files: `backend/app/proposal_resolver_service.py`, `backend/solbot/roi_calculator.py`)

3.  **Risk Ladder Statistics Calculation (`RiskLadderStatsCalculatorService`):**
    *   Another background service (`RiskLadderStatsCalculatorService` defined in `backend/app/risk_ladder_stats_service.py`) runs frequently.
    *   It queries the `live_model_proposals` table for the `RISK_LADDER_POOL_SIZE` most recently resolved proposals that have a valid `actual_max_roi`. This forms a sliding window of recent model performance.
    *   Based on the `actual_max_roi` values in this pool, it calculates the percentages for each rung of the Risk Ladder:
        *   💀 RUG PULL (-100% Profit): `actual_max_roi < 0.001`
        *   😐 Small Gains (0-100% Profit): `0.001 <= actual_max_roi < 1.0`
        *   ✅ Good Profit (100-200% Profit): `1.0 <= actual_max_roi < 2.0`
        *   💎 Big Gains (200-500% Profit): `2.0 <= actual_max_roi < 5.0`
        *   🚀 TO THE MOON (500%+ Profit): `actual_max_roi >= 5.0`
    *   These calculated percentages, along with the pool sample size and last update time, are saved to a JSON file specified by `LIVE_RISK_LADDER_STATS_FILE_PATH`.
    *   (Files: `backend/app/risk_ladder_stats_service.py`)

4.  **Serving Stats with New Proposals:**
    *   When a new BUY signal is processed by `backend/solbot/prediction.py` (within `process_buy_signal`), it reads the latest statistics from the JSON file (`LIVE_RISK_LADDER_STATS_FILE_PATH`).
    *   These live Risk Ladder statistics are then included in the WebSocket message payload sent to the user's frontend when a new BUY transaction proposal is made.
    *   (Files: `backend/solbot/prediction.py`, `backend/app/websocket_utils.py`)

5.  **Frontend Display:**
    *   The frontend component `frontend/components/proposal-modal.tsx` receives these `risk_ladder_stats` and dynamically renders the Risk Ladder display for the user.

## Key Configuration Parameters

These parameters control the behavior of the Dynamic Risk Ladder feature. They are primarily defined in `backend/solbot/config.py` and can often be overridden by environment variables (check `config.py` for specific `os.getenv` calls and defaults).

1.  **`PROPOSAL_RESOLUTION_DELAY_SECONDS`**
    *   **File:** `backend/solbot/config.py`
    *   **Environment Variable:** `PROPOSAL_RESOLUTION_DELAY_SECONDS`
    *   **Description:** The delay (in seconds) after a BUY proposal is made before the `ProposalResolverService` attempts to calculate its `actual_max_roi`. This is your `T_delay`.
    *   **Default:** `3600` (1 hour)

2.  **`RISK_LADDER_POOL_SIZE`**
    *   **File:** `backend/solbot/config.py`
    *   **Environment Variable:** `RISK_LADDER_POOL_SIZE`
    *   **Description:** The number (`N`) of most recent *resolved* proposals to include in the dynamic pool for calculating the Risk Ladder statistics. This creates the sliding window effect.
    *   **Default:** `100`

3.  **`LIVE_RISK_LADDER_STATS_FILE_PATH`**
    *   **File:** `backend/solbot/config.py`
    *   **Environment Variable:** `LIVE_RISK_LADDER_STATS_FILE_PATH`
    *   **Description:** The absolute or relative file path where the `RiskLadderStatsCalculatorService` will store the periodically updated live Risk Ladder statistics as a JSON file.
    *   **Default:** `backend/data/live_risk_ladder_stats.json` (relative to the `solbot` directory's parent, which is `backend/`)

4.  **`MAX_SIGNATURES_TO_PROCESS_FOR_ROI` (within `roi_calculator.py`)**
    *   **File:** `backend/solbot/roi_calculator.py` (as a module-level constant)
    *   **Description:** The maximum number of *oldest* transaction signatures to fetch and process when calculating the `actual_max_roi` for a single resolved proposal. This is to replicate your training data's `max_roi` calculation which used the first ~8000 transactions.
    *   **Current Value (as per our plan):** `8000`

5.  **Service Intervals (Internal to service files):**
    *   **`ProposalResolverService` (`backend/app/proposal_resolver_service.py`):** The `asyncio.sleep()` at the end of its `while True` loop determines how often it checks for proposals to resolve. Currently set to check every 1 minute ( `await asyncio.sleep(60 * 1)`).
    *   **`RiskLadderStatsCalculatorService` (`backend/app/risk_ladder_stats_service.py`):** The `asyncio.sleep()` at the end of its `while True` loop determines how often it recalculates and saves the live stats. Currently set to 30 seconds (`await asyncio.sleep(30)`).

6.  **`FORCE_BUY_SIGNAL_MVP_BYPASS`**
    *   **File:** `backend/solbot/config.py`
    *   **Environment Variable:** `FORCE_BUY_SIGNAL_MVP_BYPASS`
    *   **Description:** A boolean toggle. If `True`, the system bypasses the actual ML model prediction and forces a BUY signal for testing downstream components like the Risk Ladder display and proposal recording. If `False` (default), actual model predictions are used.
    *   **Default:** `False`

This README section should provide a good overview for you or anyone else working on the system.
```

---

This README provides:
*   A step-by-step flow of how the dynamic Risk Ladder works, referencing the key Python modules involved.
*   A list of the most important configuration parameters, where they are defined/can be set, and what they do.

Let me know if you'd like any part of this expanded or clarified! Now you're ready to proceed with the frontend implementation (Phase 5, which will be just updating the `proposal-modal.tsx` and related types/hooks).



**After Implementing Phase 4:**

1.  **Backend Startup:**
    *   The `RiskLadderStatsCalculatorService` should start and periodically create/update `backend/data/live_risk_ladder_stats.json`.
    *   Verify this file is being created/updated by checking its content and timestamp. Initially, it might contain default/zero stats until the `ProposalResolverService` populates `LiveModelProposals` with enough resolved ROIs.

2.  **Live BUY Proposal:**
    *   When the live model (via `run_global_prediction` -> `process_buy_signal`) generates a BUY signal for a user:
        *   The `process_buy_signal` function should attempt to read `live_risk_ladder_stats.json`.
        *   The content of this file (the five percentages, pool size, timestamp) should be included in the WebSocket message sent to the frontend under the `risk_ladder_stats` key.

3.  **Testing:**
    *   You'll need `N` (e.g., 100, or your configured `RISK_LADDER_POOL_SIZE`) proposals to be recorded in `LiveModelProposals`.
    *   Then, these proposals need to age past `T_delay` (e.g., 1 hour, or your `PROPOSAL_RESOLUTION_DELAY_SECONDS`).
    *   The `ProposalResolverService` must then successfully run, fetch data, calculate their `actual_max_roi`, and update these records (`is_resolved=True`, `actual_max_roi=value`).
    *   Only after that, the `RiskLadderStatsCalculatorService` will have meaningful data to compute and store the live stats.
    *   Once `live_risk_ladder_stats.json` contains non-zero/non-default values, any *new* BUY proposal sent to the frontend should include these live stats.
