import asyncio
import logging
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from fastapi import FastAPI

from app.database import async_session_factory
from app.db_models import SniperBotSimulation
from solbot.roi_calculator import calculate_actual_max_roi
from solbot.config import SNIPER_BOT_ROI_DELAY_HOURS

logger = logging.getLogger(__name__)

async def resolve_pending_sniper_bot_simulations_task(app: FastAPI):
    """
    DEPRECATED: This service has been replaced by batch processing for cost optimization.

    The sniper bot now uses batch processing instead of continuous resolution:
    - Manual trigger via API: POST /api/risk-ladder/admin/trigger-sniper-bot-resolution
    - Automatic daily processing at configured hour

    This function is kept for backward compatibility but does nothing.
    """
    logger.info("SniperBotResolverService: DEPRECATED - Continuous resolution disabled.")
    logger.info("SniperBotResolverService: Sniper bot now uses batch processing for cost optimization.")
    logger.info("SniperBotResolverService: Use manual trigger API or wait for daily automatic processing.")

    # Just sleep indefinitely - this service is disabled
    while True:
        await asyncio.sleep(3600)  # Sleep for 1 hour


async def start_sniper_bot_resolver_service(app: FastAPI):
    """
    DEPRECATED: Start the sniper bot resolver service as a background task.

    This service is now disabled in favor of batch processing.
    """
    logger.warning("SniperBotResolverService: Starting deprecated resolver service (disabled).")
    await resolve_pending_sniper_bot_simulations_task(app)
