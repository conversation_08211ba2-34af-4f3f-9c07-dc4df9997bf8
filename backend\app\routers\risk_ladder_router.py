from fastapi import APIRouter, HTTPException, status, Depends, Request
import logging
import json
import os
from typing import Dict, Any

from app.dependencies import CustomRateLimiter, admin_required
from app.db_models import User
from solbot.config import LIVE_RISK_LADDER_STATS_FILE_PATH, _data_dir

logger = logging.getLogger(__name__)

# Path for sniper bot simulation stats (matches the stats service)
SNIPER_BOT_STATS_FILE_PATH = os.path.join(_data_dir, 'sniper_bot_simulation_stats.json')

router = APIRouter(
    prefix="/api/risk-ladder",
    tags=["risk-ladder"],
    dependencies=[Depends(CustomRateLimiter(times=30, seconds=60))]  # 30 requests per minute
)


@router.get("/live-signals")
async def get_live_signals_risk_ladder() -> Dict[str, Any]:
    """
    Get risk ladder statistics for live buy signals.
    
    Returns:
        Dict containing risk ladder percentages and metadata for live buy signals.
    """
    try:
        if not os.path.exists(LIVE_RISK_LADDER_STATS_FILE_PATH):
            logger.warning(f"Live risk ladder stats file not found: {LIVE_RISK_LADDER_STATS_FILE_PATH}")
            return {
                "rug_pull_pct": 0.0,
                "small_gains_pct": 0.0,
                "good_profit_pct": 0.0,
                "big_gains_pct": 0.0,
                "to_the_moon_pct": 0.0,
                "pool_sample_size": 0,
                "last_updated_utc": None,
                "status": "no_data"
            }
        
        with open(LIVE_RISK_LADDER_STATS_FILE_PATH, 'r') as f:
            stats_data = json.load(f)
        
        # Validate required keys
        required_keys = ["rug_pull_pct", "small_gains_pct", "good_profit_pct", "big_gains_pct", "to_the_moon_pct"]
        if not all(key in stats_data for key in required_keys):
            logger.error(f"Invalid live risk ladder stats file format. Missing required keys.")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid risk ladder data format"
            )
        
        # Add status indicator
        stats_data["status"] = "active"
        
        logger.debug(f"Served live signals risk ladder stats: {stats_data}")
        return stats_data
        
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing live risk ladder stats JSON: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error parsing risk ladder data"
        )
    except Exception as e:
        logger.error(f"Error reading live risk ladder stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving risk ladder data"
        )


@router.get("/sniper-bot-simulation")
async def get_sniper_bot_simulation_risk_ladder() -> Dict[str, Any]:
    """
    Get risk ladder statistics for sniper bot simulation.
    
    Returns:
        Dict containing risk ladder percentages and metadata for sniper bot simulation.
    """
    try:
        if not os.path.exists(SNIPER_BOT_STATS_FILE_PATH):
            logger.warning(f"Sniper bot simulation stats file not found: {SNIPER_BOT_STATS_FILE_PATH}")
            return {
                "rug_pull_pct": 0.0,
                "small_gains_pct": 0.0,
                "good_profit_pct": 0.0,
                "big_gains_pct": 0.0,
                "to_the_moon_pct": 0.0,
                "total_tokens": 0,
                "analysis_period_hours": 12,
                "grace_period_hours": 3,
                "last_updated_utc": None,
                "status": "no_data"
            }
        
        with open(SNIPER_BOT_STATS_FILE_PATH, 'r') as f:
            stats_data = json.load(f)
        
        # Validate required keys
        required_keys = ["rug_pull_pct", "small_gains_pct", "good_profit_pct", "big_gains_pct", "to_the_moon_pct"]
        if not all(key in stats_data for key in required_keys):
            logger.error(f"Invalid sniper bot simulation stats file format. Missing required keys.")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid risk ladder data format"
            )
        
        # Add status indicator
        stats_data["status"] = "active"
        
        logger.debug(f"Served sniper bot simulation risk ladder stats: {stats_data}")
        return stats_data
        
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing sniper bot simulation stats JSON: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error parsing risk ladder data"
        )
    except Exception as e:
        logger.error(f"Error reading sniper bot simulation stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving risk ladder data"
        )


@router.get("/comparison")
async def get_risk_ladder_comparison() -> Dict[str, Any]:
    """
    Get both live signals and sniper bot simulation risk ladder data for comparison.
    
    Returns:
        Dict containing both datasets for comparison view.
    """
    try:
        live_signals_data = await get_live_signals_risk_ladder()
        sniper_bot_data = await get_sniper_bot_simulation_risk_ladder()
        
        return {
            "live_signals": live_signals_data,
            "sniper_bot_simulation": sniper_bot_data,
            "comparison_timestamp": live_signals_data.get("last_updated_utc") or sniper_bot_data.get("last_updated_utc")
        }
        
    except Exception as e:
        logger.error(f"Error getting risk ladder comparison data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving comparison data"
        )


@router.post("/admin/trigger-sniper-bot-resolution")
async def trigger_sniper_bot_resolution(
    request: Request,
    admin_user: User = Depends(admin_required)
) -> Dict[str, Any]:
    """
    Manually trigger sniper bot batch resolution process.

    This endpoint allows admins to manually trigger the batch processing
    of sniper bot simulations, which normally runs automatically once per day.

    Requires admin authentication.

    Returns:
        Dict containing the batch processing results and statistics.
    """
    try:
        logger.info(f"Manual sniper bot resolution triggered by admin: {admin_user.wallet_address}")

        # Import here to avoid circular imports
        from app.sniper_bot_batch_resolver import resolve_sniper_bot_batch

        # Get the app instance from the request
        app = request.app

        # Trigger the batch resolution
        result = await resolve_sniper_bot_batch(app)

        logger.info(f"Manual sniper bot resolution completed. Result: {result}")

        return {
            "message": "Sniper bot batch resolution triggered successfully",
            "triggered_by": admin_user.wallet_address,
            "result": result
        }

    except Exception as e:
        logger.error(f"Error in manual sniper bot resolution trigger: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error triggering sniper bot resolution: {str(e)}"
        )
