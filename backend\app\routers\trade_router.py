from fastapi import APIRouter, Depends, HTTPException, Request, status
import logging
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.schemas.trade_schemas import TradeReportPayload, TradeReportResponse
from app.db_models import TradeTypeEnum, User
from app.crud.trade_crud import create_trade_record
from app.crud.holding_crud import holding_crud
from app.dependencies import get_current_user, CustomRateLimiter

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/trade",
    tags=["trade"],
    dependencies=[Depends(CustomRateLimiter(times=60, seconds=60))])  # 60 requests per minute (1 per second)

@router.post("/report", response_model=TradeReportResponse,
            dependencies=[Depends(CustomRateLimiter(times=60, seconds=60))])  # 60 requests per minute (1 per second)
async def report_trade_result(
    payload: TradeReportPayload, 
    request: Request, 
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Report the result of a trade (buy or sell).
    
    This endpoint handles reports of both successful and failed trades.
    For successful trades, it:
    - Records the trade in the database
    - Updates the user's holdings (for both buys and sells)
    - Calculates PNL for sells
    
    For failed trades, it only logs the failure.
    """
    logger.info(f"Received trade report: {payload.model_dump(exclude_none=True)}")
    
    # Check if payload wallet matches authenticated user
    if payload.user_wallet != current_user.wallet_address:
        logger.error(f"Auth mismatch: Token for {current_user.wallet_address}, payload for {payload.user_wallet}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Wallet address in payload does not match authenticated user")
    
    if payload.status == 'success':
        holding_updated = False
        trade_record = None
        try:
            # Convert string trade_type to TradeTypeEnum for the database
            trade_type_enum = TradeTypeEnum(payload.trade_type)
            
            # Important: Record trade BEFORE updating holding for PNL calc on sells
            trade_record = await create_trade_record(
                db, 
                user_wallet=current_user.wallet_address, 
                trade_type=trade_type_enum,
                token_mint=payload.mint_address, 
                amount_token=payload.amount_token, 
                price_sol=payload.price_sol, 
                total_sol=payload.total_sol, 
                tx_signature=payload.tx_signature,
                fee_amount_sol=payload.fee_amount_sol,
                fee_destination_wallet=payload.fee_destination_wallet
            )
            
            if payload.trade_type == 'BUY':
                # cost_sol for BUY is payload.total_sol
                holding = await holding_crud.update_holding_on_buy(
                    db, 
                    user_wallet=current_user.wallet_address, 
                    token_mint=payload.mint_address, 
                    amount_bought=payload.amount_token, 
                    cost_sol=payload.total_sol
                )
                holding_updated = True
                
                # Activate monitoring for this holding
                if holding:
                    await holding_crud.activate_monitoring_for_holding(db, holding.id)
                    logger.info(f"Activated price monitoring for {payload.mint_address} for user {current_user.wallet_address}")
                logger.info(f"PHASE 6 TODO: Signal Price Monitor to START polling for new BUY holding: {payload.mint_address} for user {current_user.wallet_address}")
            
            elif payload.trade_type == 'SELL':
                if payload.sell_full_balance:
                    # If frontend indicates full balance was sold, delete the record directly
                    logger.info(f"Selling full balance of {payload.mint_address} for user {current_user.wallet_address}")
                    holding_deleted = await holding_crud.delete_holding(
                        db,
                        user_wallet=current_user.wallet_address,
                        token_mint=payload.mint_address
                    )
                    
                    # Set the holding_updated flag based on whether we successfully deleted the holding
                    holding_updated = holding_deleted
                    logger.info(f"Delete holding result for {payload.mint_address}: {'Success' if holding_deleted else 'Failed'}")
                else:
                    # Otherwise, update (for potential future partial sells)
                    remaining = await holding_crud.update_holding_on_sell(
                        db,
                        user_wallet=current_user.wallet_address,
                        token_mint=payload.mint_address,
                        amount_sold=payload.amount_token
                    )
                    holding_updated = remaining is not None # Consider update successful if holding existed
                    
                    # Only activate monitoring if it was a partial sell (not deleted)
                    if holding_updated:
                        await holding_crud.activate_monitoring_for_holding_by_details(
                            db,
                            user_wallet=current_user.wallet_address,
                            token_mint=payload.mint_address
                        )
                        logger.info(f"Activated price monitoring for partially sold holding {payload.mint_address} for user {current_user.wallet_address}")
            
            return TradeReportResponse(
                message="Trade recorded successfully", 
                trade_id=trade_record.id if trade_record else None, 
                holding_updated=holding_updated
            )
        
        except Exception as db_err:
            logger.exception(f"Database error processing successful trade report for {current_user.wallet_address}/{payload.mint_address}: {db_err}")
            raise HTTPException(status_code=500, detail="Database error processing trade report")
    
    elif payload.status == 'failure':
        logger.warning(f"Received failed trade report for {current_user.wallet_address} - {payload.mint_address}: {payload.error_message}")
        return TradeReportResponse(message="Failed trade reported", holding_updated=False)
    
    else:
        raise HTTPException(status_code=400, detail="Invalid status in trade report")