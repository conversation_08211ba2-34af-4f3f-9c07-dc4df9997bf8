import asyncio
import logging
import time
from solbot.data_fetching import fetch_transaction_batch
from solbot.utilities import token_type
from solbot.websocket import ensure_completion_processor_running

logger = logging.getLogger(__name__)

async def process_realtime_batches(app):
    """
    Background task that processes batches of mint addresses with pending signatures.
    Runs continuously to periodically check for and process any mint addresses
    that have pending real-time signatures.
    
    Args:
        app: The FastAPI application instance with shared state
    """
    logger.info("Starting Real-time Batch Processor Task...")
    
    # Initialize dictionary to track first signature timestamp for each mint
    if not hasattr(app.state, 'first_signature_time'):
        app.state.first_signature_time = {}
    
    try:
        while True:
            try:
                logger.debug("Starting new batch processing cycle")
                
                # Get a list of mint addresses with pending signatures
                mint_addresses_with_pending = []
                async with app.state.pending_realtime_signatures_lock:
                    mint_addresses_with_pending = [
                        mint_address for mint_address, signatures in 
                        app.state.pending_realtime_signatures.items() 
                        if signatures
                    ]
                
                if mint_addresses_with_pending:
                    logger.debug(f"Found {len(mint_addresses_with_pending)} mint addresses with pending signatures")
                    current_time = time.time()
                    
                    # Process each mint address that has pending signatures
                    for mint_address in mint_addresses_with_pending:
                        # Check the current price count for this mint
                        async with app.state.price_counters_lock:
                            price_count = app.state.price_counters.get(mint_address, 0)
                        
                        # Skip if we already have enough prices
                        if price_count >= 100:
                            logger.debug(f"Price count for {mint_address} already at {price_count}/100, skipping")
                            continue
                        
                        # Record the first signature time if not already set
                        if mint_address not in app.state.first_signature_time:
                            app.state.first_signature_time[mint_address] = current_time
                            logger.debug(f"[FIRST_SIG_TIME] Mint {mint_address}: First signature recorded at {current_time}")
                        
                        # Check if 55 seconds have passed since the first signature
                        first_sig_time = app.state.first_signature_time.get(mint_address)
                        time_since_first_sig = current_time - first_sig_time
                        first_batch_ready = time_since_first_sig >= 55  # 55 seconds threshold
                        
                        # Implement dynamic batch sizing based on price count
                        if price_count < 50:
                            batch_size = 100  # Maximize efficiency initially
                        elif price_count < 80:
                            batch_size = 25
                        elif price_count < 95:
                            batch_size = 10
                        else:  # price_count >= 95
                            batch_size = 5  # Smaller batches for precision near the end
                        
                        logger.debug(f"[BATCH_SIZE] Mint {mint_address}: Price count={price_count}, Calculated batch_size={batch_size}, Time since first signature={time_since_first_sig:.1f}s")
                        
                        # Get the pending signatures for this mint address
                        async with app.state.pending_realtime_signatures_lock:
                            # Check if mint_address is still in pending_realtime_signatures
                            if mint_address not in app.state.pending_realtime_signatures:
                                continue
                                
                            pending_sigs = app.state.pending_realtime_signatures.get(mint_address, [])
                            
                            # Process a batch if:
                            # 1. We have enough signatures based on the dynamic batch size, OR
                            # 2. It's the first batch and 55 seconds have passed, OR
                            # 3. We have 100+ signatures regardless of timing (immediate processing)
                            if (len(pending_sigs) >= batch_size or 
                                (first_batch_ready and len(pending_sigs) > 0) or
                                len(pending_sigs) >= 100):
                                
                                # If we have 100+ signatures, log it specially
                                if len(pending_sigs) >= 100:
                                    logger.debug(f"[IMMEDIATE_BATCH] Mint {mint_address}: Processing immediately with {len(pending_sigs)} signatures (>=100 threshold)")
                                # If it's the first batch after 55 seconds, log it specially
                                elif first_batch_ready and price_count == 0:
                                    logger.debug(f"[FIRST_BATCH] Mint {mint_address}: Processing first batch after {time_since_first_sig:.1f}s with {len(pending_sigs)} signatures")
                                
                                # Limit the batch to the calculated size or all pending if it's the first timed batch
                                actual_batch_size = min(batch_size, len(pending_sigs))
                                batch_to_process = pending_sigs[:actual_batch_size]
                                
                                # Update the pending list by removing the processed batch
                                app.state.pending_realtime_signatures[mint_address] = pending_sigs[actual_batch_size:]
                                logger.debug(f"[BATCH_PROCESS] Mint {mint_address}: Processing batch of {actual_batch_size} signatures (Pending: {len(pending_sigs) - actual_batch_size})")
                                
                                # Release the lock before making API calls
                                # so we don't block other operations
                            else:
                                # Not enough pending signatures for a batch and not yet time for first batch
                                logger.debug(f"Not enough signatures for {mint_address}: {len(pending_sigs)}/{batch_size}, Time since first: {time_since_first_sig:.1f}s/55s")
                                continue
                        
                        # Now that we've released the lock, process the batch
                        try:
                            transactions = await fetch_transaction_batch(app, batch_to_process, batch_size=batch_size)
                            
                            if transactions:
                                logger.debug(f"[BATCH_FETCH] Mint {mint_address}: Fetched {len(transactions)} transactions for batch.")
                                
                                # Also add these transactions to the accumulator
                                async with app.state.mint_lock_manager.acquire(mint_address):
                                    # Initialize accumulator if needed
                                    from .accumulator import initialize_accumulator, process_transaction_for_accumulator
                                    if mint_address not in app.state.accumulators:
                                        app.state.accumulators[mint_address] = initialize_accumulator()
                                        logger.debug(f"Initialized accumulator for {mint_address} in batch processor")
                                
                                    # Add all transactions to the accumulator and process them
                                    for tx in transactions:
                                        if 'transactions' not in app.state.accumulators[mint_address]:
                                            app.state.accumulators[mint_address]['transactions'] = []
                                        app.state.accumulators[mint_address]['transactions'].append(tx)
                                        
                                        # Process each transaction in the accumulator to maintain consistency
                                        # This updates prices, volumes, and other accumulator data
                                        process_result = process_transaction_for_accumulator(
                                            tx, mint_address, app.state.accumulators[mint_address]
                                        )
                                        # If the accumulator signals it's finalized, break the loop
                                        if process_result == 2:  # Special value indicating "exactly 100 prices"
                                            logger.debug(f"Accumulator for {mint_address} reached exactly 100 prices during batch processing")
                                            break
                                
                                # Process fetched transactions here (for price counter)
                                prices_found_in_batch = 0
                                completion_triggered_for_mint = False  # Flag for this mint address in this batch cycle
                                for transaction in transactions:
                                    if completion_triggered_for_mint:
                                        break  # Break transaction loop for this batch
                                    
                                    for action in transaction.get("actions", []):
                                        action_type = action.get("type", "UNKNOWN")
                                        if action_type == "SWAP":
                                            # Try to get data from tokens_swapped first
                                            tokens_swapped = action.get("info", {}).get("tokens_swapped", {})
                                            if tokens_swapped:
                                                in_token = tokens_swapped.get("in", {}).get("token_address")
                                                out_token = tokens_swapped.get("out", {}).get("token_address")
                                                in_amount = float(tokens_swapped.get("in", {}).get("amount", 0))
                                                out_amount = float(tokens_swapped.get("out", {}).get("amount", 0))
                                            else:
                                                # Fall back to swaps data if tokens_swapped is not available
                                                swaps = action["info"].get("swaps", [])
                                                if not swaps:
                                                    continue
                                                swap = swaps[0]  # Take the first swap
                                                in_token = swap.get("in", {}).get("token_address")
                                                out_token = swap.get("out", {}).get("token_address")
                                                in_amount = float(swap.get("in", {}).get("amount", 0))
                                                out_amount = float(swap.get("out", {}).get("amount", 0))
                                            
                                            if not in_token or not out_token:
                                                continue
                                            in_token_type = token_type(in_token)
                                            out_token_type = token_type(out_token)
                                            if in_token_type == out_token_type:
                                                continue
                                            price = None
                                            if in_token_type == 0:
                                                price = in_amount / out_amount if out_amount != 0 else None
                                            else:
                                                price = out_amount / in_amount if in_amount != 0 else None
                                            
                                            if price is not None:
                                                prices_found_in_batch += 1
                                                async with app.state.price_counters_lock:
                                                    if mint_address not in app.state.price_counters:
                                                        app.state.price_counters[mint_address] = 0
                                                    app.state.price_counters[mint_address] += 1
                                                    current_count = app.state.price_counters[mint_address]
                                                    
                                                    if current_count == 100:
                                                        detection_time = time.time()
                                                        # Store detection time
                                                        if not hasattr(app.state, 'detection_times'):
                                                            app.state.detection_times = {}
                                                        app.state.detection_times[mint_address] = detection_time

                                                        logger.debug(f"BATCH DETECTION: 100th price found for {mint_address} via batch processing at {detection_time}. Signature: {transaction.get('signature', 'N/A')}") # Use the signature from the current transaction

                                                        # Add to high-priority completion queue
                                                        # Use negative time as priority to process oldest detections first
                                                        # Include the signature that triggered the 100th price
                                                        trigger_signature = transaction.get('signature', 'N/A')
                                                        await app.state.completion_queue.put(
                                                            (-detection_time, mint_address, trigger_signature)
                                                        )
                                                        logger.debug(f"Added {mint_address} to high-priority completion queue.")

                                                        # Ensure the completion processor is running
                                                        await ensure_completion_processor_running(app)

                                                        # CRITICAL: Break the inner loop processing transactions within this *batch*
                                                        #           No need to count more prices for this mint address from this batch.
                                                        completion_triggered_for_mint = True
                                                        break  # Exit the loop iterating through actions in the current transaction
                                                    else:
                                                        logger.debug(f"[PRICE_COUNT_UPDATE] Mint {mint_address}: Updated price count to {current_count}/100")
                                                
                                                if not completion_triggered_for_mint:
                                                    break  # Found price in this transaction's actions
                                
                                logger.debug(f"[BATCH_PRICE_FOUND] Mint {mint_address}: Found {prices_found_in_batch} prices in the current batch.")
                            else:
                                logger.warning(f"Failed to fetch transactions or empty result for {mint_address}")
                                continue
                        except Exception as e:
                            logger.error(f"Error fetching transaction batch for {mint_address}: {e}", exc_info=True)
                            continue
                
                # Process batches every 2 seconds
                await asyncio.sleep(2)
                
            except asyncio.CancelledError:
                logger.debug("Real-time Batch Processor Task cancelled, shutting down")
                raise
            except Exception as e:
                logger.error(f"Error in Real-time Batch Processor Task: {e}", exc_info=True)
                # Brief pause after error before retrying
                await asyncio.sleep(1)
    
    except asyncio.CancelledError:
        logger.debug("Real-time Batch Processor Task shutting down")
        raise
    except Exception as e:
        logger.critical(f"Fatal error in Real-time Batch Processor Task: {e}", exc_info=True)
        # We still want to raise the exception so it's properly logged at the application level
        raise 