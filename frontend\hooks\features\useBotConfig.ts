import { useState, useEffect, FormEvent } from 'react';
import { ConfigurationResponse, ValidationErrorItem, FastAPIErrorResponse, ErrorWithMessage } from '@/types/api';
import { fetchAuthenticatedApi } from '@/lib/api';
import { useAuth } from '@/components/auth-context';
import { toast } from 'sonner';

/**
 * Custom hook for managing bot configuration state and operations
 */
export function useBotConfig() {
  const { isAuthenticated, isLoadingAuth } = useAuth();
  
  // Configuration data states
  const [configData, setConfigData] = useState<ConfigurationResponse | null>(null);
  const [isConfigLoading, setIsConfigLoading] = useState(true);
  const [configError, setConfigError] = useState<string | null>(null);
  const [isSavingConfig, setIsSavingConfig] = useState(false);
  const [configSaveStatus, setConfigSaveStatus] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [configChanged, setConfigChanged] = useState(false);

  // Fetch configuration data
  useEffect(() => {
    async function fetchConfig() {
      if (!isAuthenticated || isLoadingAuth) return;
      
      setIsConfigLoading(true);
      setConfigError(null);
      
      try {
        const response = await fetchAuthenticatedApi<ConfigurationResponse>('/api/config');
        
        if (response.success && response.data) {
          setConfigData(response.data);
        } else {
          setConfigError(typeof response.error === 'string' ? response.error : 'Failed to load configuration data');
        }
      } catch (error) {
        setConfigError('An unexpected error occurred');
        console.error('Configuration data fetch error:', error);
      } finally {
        setIsConfigLoading(false);
      }
    }
    
    fetchConfig();
  }, [isAuthenticated, isLoadingAuth]);

  // Function to clear config save status
  const clearConfigSaveStatus = () => setConfigSaveStatus(null);

  // Handle configuration form submission
  const handleConfigSave = async (event: FormEvent) => {
    event.preventDefault();
    setIsSavingConfig(true);
    setConfigSaveStatus(null);
    
    try {
      const formData = new FormData(event.currentTarget as HTMLFormElement);
      const payload: Partial<ConfigurationResponse> = {};
      
      // Helper function to check if a string is a valid number
      const isValidNumber = (value: string): boolean => {
        // Empty is considered valid (we'll handle empty values separately)
        if (value === '') return true;
        
        // Check if it's a valid non-negative number
        const numValue = parseFloat(value);
        return !isNaN(numValue) && numValue >= 0 && Number.isFinite(numValue);
      };
      
      // Get and validate max buy amount
      const maxBuySol = formData.get('max_buy_sol') as string;
      if (maxBuySol === '') {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Max Buy Amount is required. Please enter a valid amount." 
        });
        setIsSavingConfig(false);
        return;
      }
      if (!isValidNumber(maxBuySol)) {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Invalid Max Buy Amount. Please enter a valid non-negative number." 
        });
        setIsSavingConfig(false);
        return;
      }
      
      // Get and validate take profit
      const tpPercent = formData.get('tp_percent') as string;
      if (tpPercent === '') {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Take Profit percentage is required. Please enter a valid percentage." 
        });
        setIsSavingConfig(false);
        return;
      }
      if (!isValidNumber(tpPercent)) {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Invalid Take Profit percentage. Please enter a valid non-negative number." 
        });
        setIsSavingConfig(false);
        return;
      }
      
      // Get and validate stop loss
      const slPercent = formData.get('sl_percent') as string;
      if (slPercent === '') {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Stop Loss percentage is required. Please enter a valid percentage." 
        });
        setIsSavingConfig(false);
        return;
      }
      if (!isValidNumber(slPercent)) {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Invalid Stop Loss percentage. Please enter a valid non-negative number." 
        });
        setIsSavingConfig(false);
        return;
      }
      
      // Get and validate buy slippage
      const buySlippage = formData.get('buy_slippage') as string;
      if (!isValidNumber(buySlippage)) {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Invalid Buy Slippage percentage. Please enter a valid non-negative number." 
        });
        setIsSavingConfig(false);
        return;
      }
      
      // Get and validate sell slippage
      const sellSlippage = formData.get('sell_slippage') as string;
      if (!isValidNumber(sellSlippage)) {
        setConfigSaveStatus({ 
          type: 'error', 
          message: "Invalid Sell Slippage percentage. Please enter a valid non-negative number." 
        });
        setIsSavingConfig(false);
        return;
      }
      
      // Build the payload
      payload.max_buy_sol = parseFloat(maxBuySol);
      payload.tp_percent = parseFloat(tpPercent);
      payload.sl_percent = parseFloat(slPercent);
      
      // Helper function to parse % input and convert to BPS, defaulting on error/empty
      const parseSlippagePercentageToBps = (value: string | null): number => {
        const defaultBps = 150; // Default 1.5% = 150 BPS
        if (value === null || value.trim() === '') {
          console.log(`Slippage input empty, using default BPS: ${defaultBps}`);
          return defaultBps;
        }
        const perc = parseFloat(value); // Parse the percentage input
        // Convert valid % to BPS (Basis Points), rounding to nearest integer
        const calculatedBps = Math.round(perc * 100); // Correct: % * 100 = BPS
        console.log(`Parsed slippage ${perc}% to ${calculatedBps} BPS.`);
        return calculatedBps;
      };
      
      // Add BPS values to payload
      payload.buy_slippage_bps = parseSlippagePercentageToBps(buySlippage);
      payload.sell_slippage_bps = parseSlippagePercentageToBps(sellSlippage);
      
      // Get and validate priority fee
      const priorityFeeStr = formData.get('priority_fee_sol') as string;
      if (priorityFeeStr === '' || priorityFeeStr === '0') {
        payload.priority_fee_microlamports = null; // Send null if blank or zero, backend default will apply
      } else {
        const priorityFeeMicrolamports = parseInt(priorityFeeStr, 10);
        if (!isNaN(priorityFeeMicrolamports) && priorityFeeMicrolamports >= 0) {
          console.log(`Using priority fee: ${priorityFeeMicrolamports} microlamports/CU`);
          payload.priority_fee_microlamports = priorityFeeMicrolamports;
        } else {
          setConfigSaveStatus({ 
            type: 'error', 
            message: "Invalid Priority Fee. Please enter a valid non-negative integer or leave blank." 
          });
          setIsSavingConfig(false);
          return;
        }
      }
      
      // Optional filters remain as they were
      // Get min unique wallets (handle checkbox state)
      const minWalletsCheck = formData.get('minWalletsEnabled') === 'on';
      if (minWalletsCheck) {
        const minWallets = formData.get('min_unique_wallets') as string;
        if (minWallets === '') {
          // If checkbox is checked but value is empty, show error
          setConfigSaveStatus({ 
            type: 'error', 
            message: 'Min Unique Wallets filter is enabled but no value provided.' 
          });
          setIsSavingConfig(false);
          return;
        }
        
        if (!isValidNumber(minWallets)) {
          setConfigSaveStatus({ 
            type: 'error', 
            message: "Invalid Min Unique Wallets value. Please enter a valid non-negative number." 
          });
          setIsSavingConfig(false);
          return;
        }
        
        payload.min_unique_wallets = parseInt(minWallets, 10);
      } else {
        payload.min_unique_wallets = null;
      }
      
      // Get min total volume (handle checkbox state)
      const minVolumeCheck = formData.get('minVolumeEnabled') === 'on';
      if (minVolumeCheck) {
        const minVolume = formData.get('min_total_volume') as string;
        if (minVolume === '') {
          // If checkbox is checked but value is empty, show error
          setConfigSaveStatus({ 
            type: 'error', 
            message: 'Min Total Volume filter is enabled but no value provided.' 
          });
          setIsSavingConfig(false);
          return;
        }
        
        if (!isValidNumber(minVolume)) {
          setConfigSaveStatus({ 
            type: 'error', 
            message: "Invalid Min Total Volume value. Please enter a valid non-negative number." 
          });
          setIsSavingConfig(false);
          return;
        }
        
        payload.min_total_volume = parseFloat(minVolume);
      } else {
        payload.min_total_volume = null;
      }
      
      // Get max holder percentage (handle checkbox state)
      const maxHolderCheck = formData.get('maxHolderEnabled') === 'on';
      if (maxHolderCheck) {
        const maxHolder = formData.get('max_holder1_percent') as string;
        if (maxHolder === '') {
          // If checkbox is checked but value is empty, show error
          setConfigSaveStatus({ 
            type: 'error', 
            message: 'Max Holder % filter is enabled but no value provided.' 
          });
          setIsSavingConfig(false);
          return;
        }
        
        if (!isValidNumber(maxHolder)) {
          setConfigSaveStatus({ 
            type: 'error', 
            message: "Invalid Max Holder % value. Please enter a valid non-negative number." 
          });
          setIsSavingConfig(false);
          return;
        }
        
        payload.max_holder1_percent = parseFloat(maxHolder);
      } else {
        payload.max_holder1_percent = null;
      }
      
      console.log('Sending config payload:', payload);
      
      const response = await fetchAuthenticatedApi<ConfigurationResponse>('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      if (response.success && response.data) {
        setConfigSaveStatus({ type: 'success', message: 'Configuration saved successfully!' });
        setConfigData(response.data);
        setConfigChanged(false);
        toast.success('Configuration saved successfully!');
      } else {
        // Extract error message from response
        const errorMessage = typeof response.error === 'string' 
          ? response.error 
          : 'Failed to save configuration';
          
        setConfigSaveStatus({ type: 'error', message: errorMessage });
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setConfigSaveStatus({ type: 'error', message: errorMessage });
      toast.error(errorMessage);
    } finally {
      setIsSavingConfig(false);
    }
  };

  return {
    configData,
    isConfigLoading,
    configError,
    isSavingConfig,
    configSaveStatus,
    configChanged,
    handleConfigSave,
    setConfigChanged,
    clearConfigSaveStatus
  };
} 