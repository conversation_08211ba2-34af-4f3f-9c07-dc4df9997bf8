import numpy as np
import pandas as pd
import pickle
import os
import logging
import xgboost as xgb
from sklearn.calibration import CalibratedClassifierCV, calibration_curve
from sklearn.model_selection import train_test_split
from sklearn.metrics import brier_score_loss

# Configure logging
logger = logging.getLogger(__name__)

class XGBoostCalibrator:
    """
    A class to calibrate XGBoost model probabilities using scikit-learn's calibration methods.
    This is particularly useful for imbalanced datasets where raw prediction scores
    may not accurately represent true probabilities.
    """
    
    def __init__(self, method='isotonic', cv=5):
        """
        Initialize the calibrator.
        
        Args:
            method (str): Calibration method ('isotonic' or 'sigmoid')
            cv (int): Number of cross-validation folds for calibration
        """
        self.method = method
        self.cv = cv
        self.calibrator = None
        
    def fit(self, model, X, y):
        """
        Fit the calibrator to the data.
        
        Args:
            model: Trained XGBoost model
            X: Feature matrix (pandas DataFrame)
            y: Target labels
            
        Returns:
            self: Fitted calibrator
        """
        logger.info(f"Fitting probability calibrator using {self.method} method with {self.cv}-fold CV")
        
        # Create DMatrix for prediction
        dmatrix = xgb.DMatrix(X)
        
        # Get raw predictions from XGBoost model
        raw_predictions = model.predict(dmatrix)
        
        # Ensure predictions are within valid range [0,1] before calibration
        if np.any(raw_predictions < 0) or np.any(raw_predictions > 1):
            logger.warning(f"Raw predictions contain values outside [0,1] range. Min: {np.min(raw_predictions):.4f}, Max: {np.max(raw_predictions):.4f}")
            logger.info("Clipping raw predictions to [0,1] range before calibration")
            raw_predictions = np.clip(raw_predictions, 0, 1)
        
        # Create and fit the calibrator
        self.calibrator = self._fit_calibrator(raw_predictions, y)
        
        # Evaluate calibration quality
        self._evaluate_calibration(raw_predictions, y)
        
        return self
    
    def _fit_calibrator(self, predictions, y):
        """
        Fit the actual calibration model.
        
        Args:
            predictions: Raw model predictions
            y: True labels
            
        Returns:
            Fitted calibrator
        """
        # Ensure predictions are within valid range [0,1]
        predictions = np.clip(predictions, 0, 1)
        
        # Reshape predictions for sklearn
        predictions_reshaped = predictions.reshape(-1, 1)
        
        # Create and fit calibrator
        calibrator = self._create_calibrator()
        try:
            calibrator.fit(predictions_reshaped, y)
        except Exception as e:
            logger.error(f"Error fitting calibrator: {e}")
            logger.warning("Falling back to a simpler calibration approach")
            # Try a simpler approach if the standard calibration fails
            if self.method == 'isotonic':
                from sklearn.isotonic import IsotonicRegression
                calibrator = IsotonicRegression(out_of_bounds='clip')
                calibrator.fit(predictions, y)
            else:
                from sklearn.linear_model import LogisticRegression
                calibrator = LogisticRegression(C=1.0, solver='lbfgs')
                calibrator.fit(predictions_reshaped, y)
        
        return calibrator
    
    def _create_calibrator(self):
        """
        Create the appropriate calibrator based on method.
        
        Returns:
            sklearn calibrator object
        """
        from sklearn.isotonic import IsotonicRegression
        from sklearn.linear_model import LogisticRegression
        
        if self.method == 'isotonic':
            base_estimator = IsotonicRegression(out_of_bounds='clip')
        elif self.method == 'sigmoid':
            base_estimator = LogisticRegression(C=1.0, solver='lbfgs')
        else:
            logger.warning(f"Unknown calibration method: {self.method}, defaulting to isotonic")
            base_estimator = IsotonicRegression(out_of_bounds='clip')
            
        return base_estimator
    
    def _evaluate_calibration(self, predictions, y):
        """
        Evaluate the quality of probability calibration.
        
        Args:
            predictions: Raw model predictions
            y: True labels
        """
        # Ensure predictions are within valid range [0,1]
        predictions = np.clip(predictions, 0, 1)
        
        # Calculate calibration curve
        try:
            prob_true, prob_pred = calibration_curve(y, predictions, n_bins=10)
            
            # Calculate Brier score (lower is better)
            brier_score = brier_score_loss(y, predictions)
            
            logger.info(f"Calibration Brier score: {brier_score:.4f} (lower is better)")
            
            # Log calibration curve data points
            logger.debug("Calibration curve (fraction_of_positives, mean_predicted_value):")
            for true_prob, pred_prob in zip(prob_true, prob_pred):
                logger.debug(f"  {true_prob:.4f}, {pred_prob:.4f}")
        except Exception as e:
            logger.error(f"Error evaluating calibration: {e}")
            logger.warning("Skipping calibration evaluation due to error")
    
    def calibrate_predictions(self, model, X):
        """
        Calibrate predictions from an XGBoost model.
        
        Args:
            model: Trained XGBoost model
            X: Feature matrix (pandas DataFrame)
            
        Returns:
            numpy.ndarray: Calibrated probabilities
        """
        if self.calibrator is None:
            logger.warning("Calibrator not fitted. Returning raw predictions.")
            dmatrix = xgb.DMatrix(X)
            return model.predict(dmatrix)
        
        # Get raw predictions
        dmatrix = xgb.DMatrix(X)
        raw_predictions = model.predict(dmatrix)
        
        # Ensure predictions are within valid range [0,1]
        raw_predictions = np.clip(raw_predictions, 0, 1)
        
        # Reshape for sklearn
        predictions_reshaped = raw_predictions.reshape(-1, 1)
        
        try:
            # Apply calibration
            calibrated_probs = self.calibrator.predict(predictions_reshaped)
            
            # Ensure calibrated probabilities are also within valid range
            calibrated_probs = np.clip(calibrated_probs, 0, 1)
            
            return calibrated_probs
        except Exception as e:
            logger.error(f"Error during calibration: {e}")
            logger.warning("Returning clipped raw predictions due to calibration error.")
            return raw_predictions
    
    def save(self, path):
        """
        Save the calibrator to a file.
        
        Args:
            path: Path to save the calibrator
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save calibrator
        with open(path, 'wb') as f:
            pickle.dump(self, f)
        
        logger.info(f"Saved probability calibrator to {path}")
    
    @classmethod
    def load(cls, path):
        """
        Load a calibrator from a file.
        
        Args:
            path: Path to the saved calibrator
            
        Returns:
            XGBoostCalibrator: Loaded calibrator
        """
        if not os.path.exists(path):
            logger.error(f"Calibrator file not found: {path}")
            return None
        
        try:
            with open(path, 'rb') as f:
                calibrator = pickle.load(f)
            
            logger.info(f"Loaded probability calibrator from {path}")
            return calibrator
        except Exception as e:
            logger.error(f"Error loading calibrator: {e}")
            return None


def calibrate_xgboost_model(model, X_train, y_train, method='isotonic', cv=5, save_path=None):
    """
    Calibrate an XGBoost model's probability predictions.
    
    Args:
        model: Trained XGBoost model
        X_train: Training feature matrix
        y_train: Training target labels
        method: Calibration method ('isotonic' or 'sigmoid')
        cv: Number of cross-validation folds
        save_path: Path to save the calibrator (optional)
        
    Returns:
        XGBoostCalibrator: Fitted calibrator
    """
    try:
        # Create DMatrix for prediction to check raw predictions
        dmatrix = xgb.DMatrix(X_train)
        raw_predictions = model.predict(dmatrix)
        
        # Check if predictions are within valid range [0,1]
        if np.any(raw_predictions < 0) or np.any(raw_predictions > 1):
            logger.warning(f"Raw predictions contain values outside [0,1] range. Min: {np.min(raw_predictions):.4f}, Max: {np.max(raw_predictions):.4f}")
            logger.info("Clipping raw predictions to [0,1] range before calibration")
            raw_predictions = np.clip(raw_predictions, 0, 1)
        
        # Create and fit calibrator
        calibrator = XGBoostCalibrator(method=method, cv=cv)
        
        # Ensure y_train is valid for calibration
        if len(np.unique(y_train)) < 2:
            logger.error("Cannot calibrate with less than 2 classes in y_train")
            return None
            
        # Fit the calibrator with additional error handling
        try:
            calibrator.fit(model, X_train, y_train)
        except Exception as inner_e:
            logger.error(f"Error during calibrator fitting: {str(inner_e)}")
            logger.warning("Attempting alternative calibration approach")
            
            # Try direct calibration with clipped values as fallback
            try:
                # Reshape for sklearn
                raw_predictions_reshaped = raw_predictions.reshape(-1, 1)
                
                if method == 'isotonic':
                    from sklearn.isotonic import IsotonicRegression
                    direct_calibrator = IsotonicRegression(out_of_bounds='clip')
                    direct_calibrator.fit(raw_predictions, y_train)
                else:
                    from sklearn.linear_model import LogisticRegression
                    direct_calibrator = LogisticRegression(C=1.0, solver='lbfgs')
                    direct_calibrator.fit(raw_predictions_reshaped, y_train)
                    
                # Create a new calibrator with the direct calibrator
                calibrator.calibrator = direct_calibrator
                logger.info("Successfully created alternative calibrator")
            except Exception as alt_e:
                logger.error(f"Alternative calibration also failed: {str(alt_e)}")
                return None
        
        # Save if path provided
        if save_path and calibrator.calibrator is not None:
            calibrator.save(save_path)
        
        return calibrator
    except Exception as e:
        logger.error(f"Error in calibrate_xgboost_model: {str(e)}")
        logger.warning("Returning None due to calibration error")
        return None


def get_calibrated_predictions(model, calibrator, X, threshold=0.5):
    """
    Get calibrated predictions from an XGBoost model.
    
    Args:
        model: Trained XGBoost model
        calibrator: Fitted XGBoostCalibrator
        X: Feature matrix
        threshold: Classification threshold
        
    Returns:
        tuple: (calibrated_probabilities, binary_predictions)
    """
    try:
        # Check if calibrator is valid
        if calibrator is None:
            logger.warning("Calibrator is None. Using raw predictions instead.")
            dmatrix = xgb.DMatrix(X)
            raw_probs = model.predict(dmatrix)
            # Ensure raw probabilities are within valid range
            raw_probs = np.clip(raw_probs, 0, 1)
            binary_preds = (raw_probs >= threshold).astype(int)
            return raw_probs, binary_preds
        
        # Get calibrated probabilities
        calibrated_probs = calibrator.calibrate_predictions(model, X)
        
        # Ensure calibrated probabilities are within valid range
        calibrated_probs = np.clip(calibrated_probs, 0, 1)
        
        # Apply threshold for binary predictions
        binary_preds = (calibrated_probs >= threshold).astype(int)
        
        return calibrated_probs, binary_preds
    except Exception as e:
        logger.error(f"Error in get_calibrated_predictions: {str(e)}")
        logger.warning("Falling back to raw predictions due to error")
        
        # Fallback to raw predictions
        dmatrix = xgb.DMatrix(X)
        raw_probs = model.predict(dmatrix)
        # Ensure raw probabilities are within valid range
        raw_probs = np.clip(raw_probs, 0, 1)
        binary_preds = (raw_probs >= threshold).astype(int)
        
        return raw_probs, binary_preds


def evaluate_calibration_performance(y_true, raw_probs, calibrated_probs):
    """
    Evaluate the performance improvement from probability calibration.
    
    Args:
        y_true: True labels
        raw_probs: Raw model probabilities
        calibrated_probs: Calibrated probabilities
        
    Returns:
        dict: Performance metrics
    """
    # Ensure probabilities are within valid range [0,1]
    raw_probs_clipped = np.clip(raw_probs, 0, 1)
    calibrated_probs_clipped = np.clip(calibrated_probs, 0, 1)
    
    # Log if clipping was necessary
    if np.any(raw_probs != raw_probs_clipped):
        logger.warning(f"Raw probabilities contained values outside [0,1] range. Min: {np.min(raw_probs):.4f}, Max: {np.max(raw_probs):.4f}")
    
    if np.any(calibrated_probs != calibrated_probs_clipped):
        logger.warning(f"Calibrated probabilities contained values outside [0,1] range. Min: {np.min(calibrated_probs):.4f}, Max: {np.max(calibrated_probs):.4f}")
    
    # Calculate Brier scores (lower is better) using clipped values
    raw_brier = brier_score_loss(y_true, raw_probs_clipped)
    calibrated_brier = brier_score_loss(y_true, calibrated_probs_clipped)
    
    # Calculate calibration curves using clipped values
    raw_prob_true, raw_prob_pred = calibration_curve(y_true, raw_probs_clipped, n_bins=10)
    cal_prob_true, cal_prob_pred = calibration_curve(y_true, calibrated_probs_clipped, n_bins=10)
    
    # Calculate calibration error (mean absolute difference between true and predicted probs)
    raw_cal_error = np.mean(np.abs(raw_prob_true - raw_prob_pred))
    cal_cal_error = np.mean(np.abs(cal_prob_true - cal_prob_pred))
    
    # Log results
    logger.info(f"Raw predictions Brier score: {raw_brier:.4f}")
    logger.info(f"Calibrated predictions Brier score: {calibrated_brier:.4f}")
    
    # Calculate improvement percentage safely
    if raw_brier > 0:
        improvement_pct = (raw_brier - calibrated_brier) / raw_brier * 100
        logger.info(f"Calibration improvement: {improvement_pct:.2f}%")
    else:
        improvement_pct = 0
        logger.warning("Cannot calculate improvement percentage: raw Brier score is zero or negative")
    
    logger.info(f"Raw calibration error: {raw_cal_error:.4f}")
    logger.info(f"Calibrated calibration error: {cal_cal_error:.4f}")
    
    # Return metrics
    return {
        'raw_brier_score': raw_brier,
        'calibrated_brier_score': calibrated_brier,
        'brier_improvement_pct': improvement_pct,
        'raw_calibration_error': raw_cal_error,
        'calibrated_calibration_error': cal_cal_error
    }