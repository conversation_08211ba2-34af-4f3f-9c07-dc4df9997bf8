import asyncio
import logging
import os
import pandas as pd
from datetime import datetime
from typing import Set, Optional
from fastapi import FastAP<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.database import async_session_factory
from app.db_models import SniperBotSimulation
from solbot.config import CSV_MINT_ADDRESSES_FILE_PATH

logger = logging.getLogger(__name__)

class CSVMonitorService:
    """
    Service that monitors the detected_mint_addresses.csv file for new entries
    and creates SniperBotSimulation records in the database.
    """
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.is_running = False
        self.processed_tokens: Set[str] = set()
        self.last_file_size = 0
        self.last_modified_time = 0
    
    async def start(self):
        """Start the CSV monitor service."""
        if self.is_running:
            logger.warning("CSVMonitorService: Service is already running.")
            return
        
        self.is_running = True
        logger.info("CSVMonitorService: Starting service.")
        
        # Load already processed tokens from database
        await self._load_processed_tokens()
        
        # Initial processing of existing CSV data
        await self._process_csv_file()
        
        # Start monitoring loop
        while self.is_running:
            try:
                await self._check_for_updates()
                await asyncio.sleep(10)  # Check every 10 seconds
            except Exception as e:
                logger.error(f"CSVMonitorService: Error in monitoring loop: {e}", exc_info=True)
                await asyncio.sleep(30)  # Wait longer on error
    
    async def stop(self):
        """Stop the CSV monitor service."""
        self.is_running = False
        logger.info("CSVMonitorService: Service stopped.")
    
    async def _load_processed_tokens(self):
        """Load already processed tokens from database to avoid duplicates."""
        try:
            async with async_session_factory() as db:
                stmt = select(SniperBotSimulation.mint_address)
                result = await db.execute(stmt)
                self.processed_tokens = set(result.scalars().all())
                
            logger.info(f"CSVMonitorService: Loaded {len(self.processed_tokens)} already processed tokens from database.")
        except Exception as e:
            logger.error(f"CSVMonitorService: Error loading processed tokens: {e}")
            self.processed_tokens = set()
    
    async def _check_for_updates(self):
        """Check if the CSV file has been updated and process new entries."""
        if not os.path.exists(CSV_MINT_ADDRESSES_FILE_PATH):
            return
        
        try:
            # Get file stats
            stat = os.stat(CSV_MINT_ADDRESSES_FILE_PATH)
            current_size = stat.st_size
            current_modified = stat.st_mtime
            
            # Check if file has changed
            if (current_size != self.last_file_size or 
                current_modified != self.last_modified_time):
                
                logger.debug(f"CSVMonitorService: CSV file updated. Processing new entries.")
                await self._process_csv_file()
                
                self.last_file_size = current_size
                self.last_modified_time = current_modified
                
        except Exception as e:
            logger.error(f"CSVMonitorService: Error checking file updates: {e}")
    
    async def _process_csv_file(self):
        """Process the CSV file and create database records for new tokens."""
        try:
            if not os.path.exists(CSV_MINT_ADDRESSES_FILE_PATH):
                logger.warning(f"CSVMonitorService: CSV file not found: {CSV_MINT_ADDRESSES_FILE_PATH}")
                return
            
            # Read the CSV file
            df = pd.read_csv(CSV_MINT_ADDRESSES_FILE_PATH)
            
            if df.empty:
                logger.debug("CSVMonitorService: CSV file is empty.")
                return
            
            # Convert timestamp column to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            new_tokens = []
            for _, row in df.iterrows():
                mint_address = row['mint_address']
                
                # Skip if already processed
                if mint_address in self.processed_tokens:
                    continue
                
                # Create new token record
                token_data = {
                    'mint_address': mint_address,
                    'launch_timestamp': row['timestamp'].to_pydatetime(),
                    'signature': row.get('signature', '')
                }
                new_tokens.append(token_data)
                self.processed_tokens.add(mint_address)
            
            if new_tokens:
                await self._create_database_records(new_tokens)
                logger.info(f"CSVMonitorService: Processed {len(new_tokens)} new tokens from CSV.")
            else:
                logger.debug("CSVMonitorService: No new tokens found in CSV.")
                
        except Exception as e:
            logger.error(f"CSVMonitorService: Error processing CSV file: {e}", exc_info=True)
    
    async def _create_database_records(self, tokens: list):
        """Create database records for new tokens."""
        try:
            async with async_session_factory() as db:
                for token_data in tokens:
                    # Check if record already exists (double-check)
                    stmt = select(SniperBotSimulation).where(
                        SniperBotSimulation.mint_address == token_data['mint_address']
                    )
                    result = await db.execute(stmt)
                    existing = result.scalar_one_or_none()
                    
                    if existing:
                        logger.debug(f"CSVMonitorService: Token {token_data['mint_address']} already exists in database.")
                        continue
                    
                    # Create new record
                    simulation = SniperBotSimulation(
                        mint_address=token_data['mint_address'],
                        launch_timestamp=token_data['launch_timestamp'],
                        signature=token_data['signature'],
                        is_resolved=False
                    )
                    
                    db.add(simulation)
                    logger.debug(f"CSVMonitorService: Created record for token {token_data['mint_address']}")
                
                await db.commit()
                logger.debug(f"CSVMonitorService: Successfully committed {len(tokens)} new records to database.")
                
        except Exception as e:
            logger.error(f"CSVMonitorService: Error creating database records: {e}", exc_info=True)
            # Rollback is automatic with async context manager


async def start_csv_monitor_service(app: FastAPI):
    """Start the CSV monitor service as a background task."""
    service = CSVMonitorService(app)
    await service.start()
