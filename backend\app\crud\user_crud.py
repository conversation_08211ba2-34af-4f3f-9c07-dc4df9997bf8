from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
from typing import Optional

from app.db_models import User
from app.crud.base_crud import BaseCRUD


class UserCRUD(BaseCRUD[User]):
    """
    CRUD operations for the User model.
    """
    
    def __init__(self):
        super().__init__(User)
    
    async def get_by_wallet_address(self, db: AsyncSession, wallet_address: str) -> Optional[User]:
        """
        Get a user by wallet address.
        
        Args:
            db: AsyncSession for database operations
            wallet_address: The wallet address to query
        
        Returns:
            User instance if found, None otherwise
        """
        query = select(User).filter(User.wallet_address == wallet_address)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_or_create_user(self, db: AsyncSession, wallet_address: str) -> User:
        """
        Get a user by wallet address or create a new one if not exists.
        
        Args:
            db: AsyncSession for database operations
            wallet_address: The wallet address to query or create
        
        Returns:
            User instance (either existing or newly created)
        """
        # Try to get existing user
        user = await self.get_by_wallet_address(db, wallet_address)
        
        # If user exists, return it
        if user:
            return user
        
        # Create new user if none exists
        try:
            new_user = User(wallet_address=wallet_address)
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            return new_user
        except IntegrityError:
            # Handle race condition where user might have been created in another session
            await db.rollback()
            # Try fetching again after rolling back transaction
            user = await self.get_by_wallet_address(db, wallet_address)
            if user:
                return user
            # If still no user, raise the error
            raise


# Create a singleton instance for reuse
user_crud = UserCRUD() 