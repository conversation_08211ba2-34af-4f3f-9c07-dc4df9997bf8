# new_token_listener_pumpfun.py
import json
import logging
import aiohttp
import websockets
from websockets.exceptions import ConnectionClosed, ConnectionClosedError
import asyncio
import signal
import sys
import time  # Added import for time measurement
import csv
import datetime
from solbot.solbot_main import starter
from config.logging_config import setup_logging
import os
import certifi
from solbot.config import (
    api_key_manager,
    SHYFT_API_BASE_URL,
    SHYFT_WS_URI_1,
    SHYFT_WS_URI_2,
    CSV_LOGGING_ENABLED,
    CSV_MINT_ADDRESSES_FILE_PATH
)

os.environ["SSL_CERT_FILE"] = certifi.where()

# Initialize logging
setup_logging()

# Now get the module-level logger
logger = logging.getLogger(__name__)

FLAG_FILE_PATH = os.path.join(os.path.dirname(__file__), 'process_new_tokens.flag')

# Add rate limiting configuration
RATE_LIMIT_INTERVAL = 1.1  # Minimum interval between token processing in seconds

# Define WebSocket endpoints using Shyft URIs from centralized config
ENDPOINTS = [SHYFT_WS_URI_1, SHYFT_WS_URI_2]

PING_INTERVAL = 30  # Reduced from 500 to 30 seconds for more responsive connection
PING_TIMEOUT = 60   # Reduced from 550 to 60 seconds

# Concurrency Configuration
MAX_CONCURRENT_TASKS = 20  # Adjust based on expected load and system capacity
MAX_MINT_ADDRESSES = 200000000 # Total number of mint addresses to process


def read_process_new_tokens_flag():
    # Always return True regardless of file content
    logger.debug("Forcing flag to return True regardless of file content")
    return True

    # try:
    #     with open(FLAG_FILE_PATH, 'r') as f:
    #         value = f.read().strip()
    #         # Log at INFO level so we definitely see it
    #         logger.info(f"Read flag value: '{value}' from {FLAG_FILE_PATH}")
    #         # Be more lenient with what we accept as "true"
    #         result = value.lower() in ('true', 't', 'yes', 'y', '1')
    #         logger.info(f"Flag value evaluated to: {result}")
    #         return result
    # except Exception as e:
    #     logger.error(f"Error reading flag file: {e}")
    #     # Default to True if there's an error
    #     logger.info("Error reading flag file, defaulting to True")
    #     return True


async def log_mint_address_to_csv(mint_address, signature):
    """
    Log detected mint address to CSV file with timestamp and signature.
    Only logs if CSV_LOGGING_ENABLED is True.
    """
    if not CSV_LOGGING_ENABLED:
        return

    try:
        # Ensure the data directory exists
        data_dir = os.path.dirname(CSV_MINT_ADDRESSES_FILE_PATH)
        os.makedirs(data_dir, exist_ok=True)

        # Check if file exists to determine if we need to write headers
        file_exists = os.path.exists(CSV_MINT_ADDRESSES_FILE_PATH)

        # Get current timestamp
        timestamp = datetime.datetime.now().isoformat()

        # Write to CSV file
        with open(CSV_MINT_ADDRESSES_FILE_PATH, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'mint_address', 'signature']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                logger.info(f"Created new CSV file for mint address logging: {CSV_MINT_ADDRESSES_FILE_PATH}")

            # Write the mint address data
            writer.writerow({
                'timestamp': timestamp,
                'mint_address': mint_address,
                'signature': signature
            })

        logger.debug(f"Logged mint address {mint_address} to CSV file")

    except Exception as e:
        logger.error(f"Error logging mint address to CSV: {e}")


async def fetch_parsed_transaction(signature, retry_count=0, max_retries=5, initial_delay=0, fixed_delay=2):
    logger.debug(f"Fetching parsed transaction for signature: {signature}")

    # Add initial delay before making the first attempt
    if retry_count == 0:
        logger.debug(f"Waiting for {initial_delay} seconds before first attempt.")
        await asyncio.sleep(initial_delay)

    url = f"{SHYFT_API_BASE_URL}/transaction/parse_selected"
    
    for attempt in range(max_retries):
        try:
            # Get a fresh API key from the pool for each attempt
            key, limiter = await api_key_manager.get_api_key_and_limiter()
            
            headers = {
                'Content-Type': 'application/json',
                'x-api-key': key,
            }

            payload = {
                "network": "mainnet-beta",
                "transaction_signatures": [signature],
                "enable_raw": True,
                "enable_events": False
            }

            async with limiter:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=payload) as response:
                        text = await response.text()
                        logger.debug(f"Shyft API response status: {response.status}, API Key: {key}")
                        
                        try:
                            response_json = json.loads(text)
                        except json.JSONDecodeError:
                            response_json = {}
                            
                        if response.status == 200:
                            if 'result' in response_json and len(response_json['result']) > 0:
                                return response_json['result'][0]  # Return the first transaction's parsed data
                            else:
                                logger.debug(f"Shyft API returned no result for signature {signature}. Retrying after delay...")
                                await asyncio.sleep(fixed_delay)
                                continue
                                
                        # Enhanced credit exhaustion detection
                        is_exhausted = False
                        exhaustion_trigger = "None"

                        # Check 1: Specific JSON error code
                        if 'error_code' in response_json:
                            error_code = response_json.get('error_code', '').lower()
                            if any(indicator in error_code for indicator in ["credits_exhausted", "quota_exceeded", "credit_limit_reached"]):
                                is_exhausted = True
                                exhaustion_trigger = f"JSON error_code: {response_json.get('error_code')}"

                        # Check 2: Text indicators in response body
                        if not is_exhausted:
                            specific_text_indicators = [
                                "credits exhausted",
                                "credits depleted",
                                "insufficient credits",
                                "credit limit reached",
                                "quota exceeded",
                                "upgrade your plan",
                                "upgrade plan to continue",
                                "please upgrade plan",
                                "api limit exceeded"
                            ]
                            response_lower = text.lower()
                            for indicator in specific_text_indicators:
                                if indicator in response_lower:
                                    is_exhausted = True
                                    exhaustion_trigger = f"Text indicator: '{indicator}'"
                                    break

                        # Check 3: 429 with specific exhaustion patterns (common case)
                        if not is_exhausted and response.status == 429:
                            # COMPREHENSIVE LOGGING FOR ANALYSIS
                            logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] 429 Response for key {key}")
                            logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] Status: {response.status}")
                            logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] Headers: {dict(response.headers)}")
                            logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] Raw Response Text: {text}")
                            logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] Response JSON: {response_json}")

                            # Check if 429 is actually credit exhaustion disguised as rate limit
                            exhaustion_patterns_429 = [
                                "limit exceeded",
                                "quota",
                                "credit",
                                "billing",
                                "subscription",
                                "plan"
                            ]
                            response_lower = text.lower()
                            for pattern in exhaustion_patterns_429:
                                if pattern in response_lower:
                                    is_exhausted = True
                                    exhaustion_trigger = f"429 with exhaustion pattern: '{pattern}'"
                                    logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] EXHAUSTION DETECTED via pattern: '{pattern}'")
                                    break

                            if not is_exhausted:
                                logger.warning(f"[SHYFT_RESPONSE_ANALYSIS] NO EXHAUSTION DETECTED - treating as rate limit")

                        if is_exhausted:
                            logger.warning(f"API Key {key} credits exhausted. Trigger: {exhaustion_trigger}. Marking key unavailable with extended cooldown.")
                            await api_key_manager.mark_api_key_unavailable(key, is_credit_exhausted=True)
                            continue
                        elif response.status == 429:
                            logger.warning(f"Rate limit (429) for key {key}. Marking temporarily unavailable.")
                            await api_key_manager.mark_api_key_unavailable(key)
                            await asyncio.sleep(fixed_delay)
                            continue
                        else:
                            logger.error(f"Shyft API request failed with status {response.status}: {text}")
                            await asyncio.sleep(fixed_delay)
                            continue
        except Exception as e:
            logger.error(f"Error in fetch_parsed_transaction: {e}")
            await asyncio.sleep(fixed_delay)
            
    logger.critical(f"Max retries reached for signature {signature}. Aborting further attempts.")
    return None


# Function to connect with retries and endpoint switching
async def connect_with_retries(endpoints, max_retries_per_endpoint=2, ping_timeout=PING_TIMEOUT):
    attempt = 0
    total_endpoints = len(endpoints)
    current_endpoint_index = 0

    while True:
        current_endpoint = endpoints[current_endpoint_index]
        logger.info(f"Attempting to connect to endpoint: {current_endpoint}")

        try:
            ws = await websockets.connect(
                current_endpoint,
                ping_interval=PING_INTERVAL,  # Automatically send a ping every ping_interval seconds
                ping_timeout=ping_timeout  # Close the connection if a ping is not responded to in ping_timeout seconds
            )
            logger.info(f"WebSocket connection established to {current_endpoint}.")
            return ws
        except Exception as e:
            logger.error(f"WebSocket connection attempt {attempt + 1} to {current_endpoint} failed: {e}")
            attempt += 1
            if attempt >= max_retries_per_endpoint:
                logger.warning(f"Exceeded max retries ({max_retries_per_endpoint}) for {current_endpoint}. Switching endpoint.")
                attempt = 0
                current_endpoint_index = (current_endpoint_index + 1) % total_endpoints
                # If we've cycled through all endpoints and failed, continue trying
                if current_endpoint_index == 0:
                    logger.warning("All endpoints have been tried and failed. Continuing to retry...")
            wait_time = min(2 ** attempt, 4)  # Exponential backoff with a cap at 4 seconds
            logger.info(f"Waiting for {wait_time} seconds before next connection attempt.")
            await asyncio.sleep(wait_time)


# Helper function to attempt non-blocking semaphore acquisition
async def try_acquire_semaphore(semaphore, timeout=0.1):
    try:
        await asyncio.wait_for(semaphore.acquire(), timeout=timeout)
        return True
    except asyncio.TimeoutError:
        return False


async def monitor_new_tokens(app):
    logger.info("Starting to monitor new Solana tokens using Shyft WebSocket...")
    consecutive_failures = 0
    max_consecutive_failures = 3
    uri_index = 0
    total_endpoints = len(ENDPOINTS)
    last_token_time = 0  # Track time of last token processing

    while True:
        current_uri = ENDPOINTS[uri_index]
        retries = 0
        max_retries_per_endpoint = 2

        while retries < max_retries_per_endpoint:
            try:
                logger.info(f"Attempting to connect to WebSocket URI: {current_uri} (Attempt {retries + 1}/{max_retries_per_endpoint})")
                
                # Initialize connection with ping interval
                async with websockets.connect(
                    current_uri, 
                    timeout=30,
                    ping_interval=20,  # Send ping every 20 seconds
                    ping_timeout=10,    # Wait 10 seconds for pong response
                    close_timeout=10    # Wait 10 seconds for close handshake
                ) as websocket:
                    logger.info(f"WebSocket connection established with {current_uri}")
                    consecutive_failures = 0  # Reset on successful connection
                    
                    # Send initial subscription request
                    subscription_request = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "logsSubscribe",
                        "params": [
                            {
                                "mentions": ["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"]
                            },
                            {
                                "commitment": "confirmed"
                            }
                        ]
                    }
                    await websocket.send(json.dumps(subscription_request))

                    subscription_response = await websocket.recv()
                    subscription_data = json.loads(subscription_response)
                    subscription_id = subscription_data.get("result")

                    if not subscription_id:
                        logger.error("Failed to obtain subscription ID from response.")
                        raise Exception("Failed to obtain subscription ID")

                    logger.info(f"Subscription successful, ID: {subscription_id}")

                    # Create message handler task
                    while True:
                        try:
                            async def message_handler():
                                nonlocal last_token_time
                                try:
                                    while True:
                                        message = await websocket.recv()
                                        message_data = json.loads(message)
                                        
                                        if 'params' in message_data and 'result' in message_data['params']:
                                            result = message_data['params']['result']
                                            if 'value' in result:
                                                logs = result['value'].get('logs', [])
                                                signature = result['value'].get('signature')

                                                if any("Instruction: MintTo" in log or "Instruction: InitializeMint" in log for log in logs):
                                                    # Always log what the flag check would return
                                                    flag_value = read_process_new_tokens_flag()
                                                    logger.debug(f"Flag check result: {flag_value}")
                                                    
                                                    if not flag_value:
                                                        logger.warning("Processing of new tokens is disabled via flag. Skipping.")
                                                        continue

                                                    # Check rate limit
                                                    current_time = time.monotonic()
                                                    time_since_last = current_time - last_token_time
                                                    if time_since_last < RATE_LIMIT_INTERVAL:
                                                        wait_time = RATE_LIMIT_INTERVAL - time_since_last
                                                        logger.debug(f"Rate limit: Waiting {wait_time:.2f} seconds before processing next token")
                                                        await asyncio.sleep(wait_time)

                                                    semaphore_acquired = await try_acquire_semaphore(app.state.concurrency_semaphore)
                                                    if not semaphore_acquired:
                                                        logger.debug(f"Concurrency limit reached ({MAX_CONCURRENT_TASKS}). Skipping mint address with signature: {signature}")
                                                        continue

                                                    async with app.state.counter_lock:
                                                        if app.state.total_mint_addresses_processed >= MAX_MINT_ADDRESSES:
                                                            logger.debug(f"Reached the maximum number of mint addresses ({MAX_MINT_ADDRESSES}). Skipping further processing.")
                                                            app.state.concurrency_semaphore.release()
                                                            continue
                                                        
                                                        app.state.total_mint_addresses_processed += 1
                                                        current_total = app.state.total_mint_addresses_processed
                                                        logger.debug(f"Processing mint address {current_total}/{MAX_MINT_ADDRESSES}")
                                                        
                                                        if current_total % 10 == 0:
                                                            current_time = time.monotonic()
                                                            elapsed_time = current_time - app.state.last_checkpoint_time
                                                            logger.info(f"Elapsed time for processing tokens {current_total - 9} to {current_total}: {elapsed_time:.2f} seconds.")
                                                            app.state.last_checkpoint_time = current_time

                                                    last_token_time = time.monotonic()  # Update last token time
                                                    # Create task for processing mint address
                                                    asyncio.create_task(process_mint_address(app, signature, logs))
                                except websockets.exceptions.ConnectionClosed as e:
                                    logger.warning(f"WebSocket connection closed in message handler: {e}")
                                    return  # Exit the message handler
                                except Exception as e:
                                    logger.error(f"Error in message handler: {e}")
                                    # Don't continue on other errors, let the outer handler deal with it
                                    raise

                            # Create and start the message handler task
                            message_handler_task = asyncio.create_task(message_handler())
                            
                            try:
                                # Wait for the message handler to complete
                                await message_handler_task
                            except asyncio.CancelledError:
                                logger.info("Message handler task was cancelled")
                                raise
                            finally:
                                # Ensure the task is properly cleaned up
                                if not message_handler_task.done():
                                    message_handler_task.cancel()
                                    try:
                                        await message_handler_task
                                    except asyncio.CancelledError:
                                        pass

                            # If we get here, the connection was closed, so break to reconnect
                            logger.info("Message handler completed, initiating reconnection...")
                            break

                        except (websockets.exceptions.ConnectionClosed, ConnectionClosedError) as e:
                            logger.warning(f"WebSocket connection closed: {e}. Initiating reconnection...")
                            await asyncio.sleep(1)  # Brief pause before reconnection
                            break  # Break out of the inner while loop to trigger reconnection
                        except Exception as e:
                            logger.error(f"Unexpected error occurred: {e}")
                            await asyncio.sleep(2)  # Longer pause on unexpected errors
                            continue

            except (ConnectionClosedError, ConnectionClosed) as e:
                retries += 1
                consecutive_failures += 1
                logger.warning(f"WebSocket connection closed: {e}. Retry {retries}/{max_retries_per_endpoint} in 1 second...")
                
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"Too many consecutive failures ({consecutive_failures}). Waiting 30 seconds before next attempt...")
                    await asyncio.sleep(30)
                    consecutive_failures = 0
                else:
                    await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                retries += 1
                await asyncio.sleep(1)

        # If we've exhausted retries for current endpoint, switch to next one
        logger.warning(f"Switching to next WebSocket endpoint after {max_retries_per_endpoint} failed attempts")
        uri_index = (uri_index + 1) % total_endpoints


async def process_mint_address(app, signature, logs):
    try:
        logger.debug(f"Processing signature: {signature}")

        # Add a small delay before calling Shyft API
        await asyncio.sleep(0)  # Delay to ensure Shyft API has processed the transaction

        # Call Shyft API
        parsed_tx = await fetch_parsed_transaction(signature)

        if parsed_tx and parsed_tx.get('status') == 'Success':
            logger.debug("Mint Address Extraction: Successfully parsed transaction using Shyft API.")

            # Filter for TOKEN_MINT with specific parent_protocol
            for action in parsed_tx.get('actions', []):
                if action['type'] == "TOKEN_MINT" and action.get(
                        'parent_protocol') == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                    logger.debug("Found matching TOKEN_MINT transaction.")
                    # Process and handle the token data here
                    token_info = action['info']
                    mint_address = token_info.get('token_address', '')

                    if mint_address:
                        logger.debug(f"Mint Address Extraction: New mint address detected: {mint_address}")

                        # Log mint address to CSV if enabled
                        await log_mint_address_to_csv(mint_address, signature)

                        # Call the starter function
                        await starter(app, mint_address)

                        # Wait until the mint address is unsubscribed
                        await wait_until_unsubscribed(app, mint_address)

                    else:
                        logger.warning("No mint address found in token info.")

                    break  # Stop further processing once we find the matching transaction
        else:
            logger.debug(f"Shyft API returned no result for signature {signature}.")
    except Exception as e:
        logger.error(f"Error processing signature {signature}: {e}")
    finally:
        # Release the semaphore
        app.state.concurrency_semaphore.release()
        logger.debug("Semaphore released.")


async def wait_until_unsubscribed(app, mint_address):
    logger.debug(f"Waiting for mint address {mint_address} to be unsubscribed.")

    # First, wait until the mint address is subscribed
    while True:
        async with app.state.subscriptions_lock:
            if mint_address in app.state.subscribed_addresses:
                logger.debug(f"Mint address {mint_address} is now subscribed.")
                break
        await asyncio.sleep(0.2)  # Adjust the sleep interval as needed

    # Then, wait until the mint address is unsubscribed
    while True:
        async with app.state.subscriptions_lock:
            if mint_address not in app.state.subscribed_addresses:
                logger.debug(f"Mint address {mint_address} is unsubscribed.")
                break
        await asyncio.sleep(0.2)  # Adjust the sleep interval as needed


# Register the signal handler for graceful shutdown
def signal_handler(sig, frame):
    logger.info('Received signal to terminate. Shutting down gracefully...')
    sys.exit(0)


signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# # Entry point to start monitoring
# async def main(app):
#     await monitor_new_tokens(app)
#
# # Example usage
# if __name__ == "__main__":
#     # Assuming 'app' is defined and has a 'state' attribute
#     class AppState:
#         pass
#
#     class App:
#         def __init__(self):
#             self.state = AppState()
#
#     app_instance = App()
#
#     try:
#         asyncio.run(main(app_instance))
#     except KeyboardInterrupt:
#         logger.info("Program interrupted by user.")
