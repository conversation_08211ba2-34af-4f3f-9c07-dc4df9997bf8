# Preprocessing Files

This directory contains:
- `selected_features.json` ✓ - Feature names and data types
- `preprocessing_params.json` ✓ - Preprocessing parameters
- `feature_scaler.joblib` ❌ - **MISSING** - Feature scaler (binary file)

**Note**: The `feature_scaler.joblib` file needs to be copied from the ml_pipeline artifacts directory.
This is a binary joblib file containing the fitted scaler that cannot be recreated from text.

To complete the setup:
Copy `feature_scaler.joblib` from ml_pipeline/artifacts/run_20250626_135000/preprocessing/
