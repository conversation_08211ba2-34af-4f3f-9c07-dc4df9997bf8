import { VersionedTransaction, Transaction, TransactionMessage, PublicKey, ComputeBudgetProgram, Connection } from "@solana/web3.js";

/**
 * Utility to build VersionedTransaction from legacy Transaction
 * @param connection - Solana connection
 * @param payer - The transaction fee payer public key
 * @param tx - Legacy transaction with instructions
 * @param priorityFeeMicroLamports - Optional priority fee in micro lamports per compute unit
 * @returns Promise resolving to a VersionedTransaction
 */
export async function buildVersionedTx(
  connection: Connection,
  payer: PublicKey,
  tx: Transaction,
  priorityFeeMicroLamports?: number | null
): Promise<VersionedTransaction> {
  const blockHash = (await connection.getLatestBlockhash('confirmed')).blockhash;

  // Add priority fee if specified
  if (priorityFeeMicroLamports && priorityFeeMicroLamports > 0) {
    console.log(`[buildVersionedTx] Applying priority fee: ${priorityFeeMicroLamports} microLamports/CU`);
    tx.add(ComputeBudgetProgram.setComputeUnitPrice({ microLamports: priorityFeeMicroLamports }));
    // Optional: You might also want to set a compute unit limit if not already set by specific instructions (e.g., Jupiter)
    // A general limit, ensure Pump.fun/Jupiter instructions fit.
    // tx.add(ComputeBudgetProgram.setComputeUnitLimit({ units: 400_000 })); // Example limit
  }

  tx.recentBlockhash = blockHash;
  tx.feePayer = payer;

  const messageV0 = new TransactionMessage({
    payerKey: payer,
    recentBlockhash: blockHash,
    instructions: tx.instructions,
  }).compileToV0Message();

  return new VersionedTransaction(messageV0);
} 