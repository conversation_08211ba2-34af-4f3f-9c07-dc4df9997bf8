import asyncio
import time
import logging
import json
import numpy as np
import decimal
from decimal import Decimal, getcontext
from typing import Optional, List, Tuple, Dict, Any
from fastapi import Fast<PERSON>I # For type hinting app
import pandas as pd
from datetime import datetime

# Assuming data_fetching functions are accessible and robust.
# These might need to be enhanced if they don't fully support exhaustive historical fetching.
from .data_fetching import fetch_signatures_for_address, fetch_transaction_batch
# from .utilities import token_type # Import if needed for price extraction

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"
MAX_SIGNATURES_TO_PROCESS_FOR_ROI = 8000 # As per your script

async def fetch_transaction_batch_for_price_extraction(app: FastAPI, batch_signatures: List[str]) -> List[Dict[str, Any]]:
    """
    Fetch transactions using proper API key management for price extraction.
    """
    import json
    import asyncio
    from aiohttp import ClientTimeout

    # Use the same API endpoint as external code
    url = 'https://api.shyft.to/sol/v1/transaction/parse_selected'

    # Get API key manager from config
    from .config import api_key_manager

    max_retries = 3
    initial_retry_delay = 1.0

    for attempt in range(max_retries):
        try:
            # Get a fresh API key for each attempt
            api_key, limiter = await api_key_manager.get_api_key_and_limiter()
            headers = {
                'Content-Type': 'application/json',
                'x-api-key': api_key.strip()
            }

            payload = json.dumps({
                "network": "mainnet-beta",
                "transaction_signatures": [str(sig) for sig in batch_signatures],
                "enable_raw": False,
                "enable_events": False
            })

            async with limiter:
                timeout = ClientTimeout(total=10)
                async with app.state.http_session.post(url, headers=headers, data=payload, timeout=timeout) as response:
                    text = await response.text()

                    # Handle rate limiting and credit exhaustion
                    if response.status == 429:
                        # Rate limiting - mark key temporarily unavailable
                        retry_after = int(response.headers.get('Retry-After', '5'))
                        logger.warning(f"Rate limit (429) hit for ROI calculation with {api_key}. Marking key temporarily unavailable and retrying after {retry_after}s...")
                        await api_key_manager.mark_api_key_unavailable(api_key)
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        # Check for credit exhaustion in non-429 responses
                        is_exhausted = False
                        exhaustion_indicators = [
                            "credits exhausted", "credits depleted", "insufficient credits",
                            "credit limit reached", "quota exceeded", "upgrade your plan",
                            "upgrade plan to continue", "please upgrade plan", "api limit exceeded"
                        ]
                        response_lower = text.lower()
                        for indicator in exhaustion_indicators:
                            if indicator in response_lower:
                                is_exhausted = True
                                logger.warning(f"Credits exhausted for {api_key} in ROI calculation (indicator: '{indicator}'). Marking key unavailable with extended cooldown.")
                                await api_key_manager.mark_api_key_unavailable(api_key, is_credit_exhausted=True)
                                break

                        if is_exhausted:
                            continue

                    if response.status == 200:
                        try:
                            data = json.loads(text)
                            # Only keep data needed for price extraction - match external code exactly
                            transactions = [
                                {
                                    'timestamp': tx.get('timestamp'),
                                    'status': tx.get('status'),
                                    'actions': tx.get('actions', [])
                                } for tx in data.get('result', [])
                            ]
                            return transactions
                        except json.JSONDecodeError:
                            logger.error(f"JSON decode error in ROI calculation: {text}")
                            await asyncio.sleep(initial_retry_delay * (attempt + 1))
                            continue
                    else:
                        logger.warning(f"Failed to fetch transactions for ROI calculation: Status {response.status}, Response: {text}")
                        await asyncio.sleep(initial_retry_delay * (attempt + 1))
                        continue

        except asyncio.TimeoutError:
            logger.warning(f"Timeout fetching transaction batch for ROI calculation. Attempt {attempt+1}/{max_retries}")
            await asyncio.sleep(initial_retry_delay * (attempt + 1))
        except Exception as e:
            logger.error(f"Error fetching transaction batch for price extraction: {e}")
            await asyncio.sleep(initial_retry_delay * (attempt + 1))

    # If all retries failed, return empty list
    logger.error(f"Failed to fetch transaction batch for ROI calculation after {max_retries} attempts")
    return []

def extract_signature_from_transaction(tx: Dict[str, Any]) -> str:
    """
    Robust signature extraction from a transaction object.
    Tries multiple common locations where signatures might be stored.
    
    Args:
        tx: Transaction object from Solana API
        
    Returns:
        Transaction signature string, or 'unknown_signature' if not found
    """
    if not tx or not isinstance(tx, dict):
        return 'unknown_signature'
    
    # List of potential signature locations in order of preference
    signature_paths = [
        # Direct fields
        ['signature'],
        ['tx_id'],
        ['transaction_id'],
        ['id'],
        ['txid'],
        ['hash'],
        
        # Nested in transaction object
        ['transaction', 'signature'],
        ['transaction', 'signatures', 0],  # First signature in array
        ['transaction', 'message', 'signature'],
        
        # Nested in raw object
        ['raw', 'signature'],
        ['raw', 'transaction', 'signature'],
        ['raw', 'transaction', 'signatures', 0],
        ['raw', 'signatures', 0],
        ['raw', 'txid'],
        ['raw', 'hash'],
        
        # Nested in meta object
        ['meta', 'signature'],
        ['meta', 'transaction_id'],
        
        # Other potential locations
        ['result', 'signature'],
        ['result', 'transaction', 'signature'],
        ['data', 'signature'],
        
        # Direct signatures array
        ['signatures', 0],
    ]
    
    # Try each path
    for path in signature_paths:
        try:
            value = tx
            for key in path:
                if isinstance(key, int):
                    # Array index
                    if isinstance(value, list) and len(value) > key:
                        value = value[key]
                    else:
                        value = None
                        break
                else:
                    # Dictionary key
                    if isinstance(value, dict) and key in value:
                        value = value[key]
                    else:
                        value = None
                        break
            
            # Check if we found a valid signature
            if value and isinstance(value, str) and len(value) >= 32:
                # Basic validation - Solana signatures are typically 88 characters (base58)
                # but let's be flexible and accept anything that looks like a signature
                if len(value) >= 32 and not value.startswith('{') and not value.startswith('['):
                    return value
                    
        except Exception as e:
            # Continue to next path if this one fails
            continue
    
    # If we couldn't find a signature through normal paths, try a more aggressive search
    try:
        # Look for any string field that might be a signature (length-based heuristic)
        def find_signature_recursively(obj, max_depth=3):
            if max_depth <= 0:
                return None
                
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, str) and 64 <= len(value) <= 128:
                        # Looks like it could be a signature or hash
                        if not value.startswith('{') and not value.startswith('[') and ' ' not in value:
                            return value
                    elif isinstance(value, (dict, list)):
                        result = find_signature_recursively(value, max_depth - 1)
                        if result:
                            return result
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, str) and 64 <= len(item) <= 128:
                        if not item.startswith('{') and not item.startswith('[') and ' ' not in item:
                            return item
                    elif isinstance(item, (dict, list)):
                        result = find_signature_recursively(item, max_depth - 1)
                        if result:
                            return result
            return None
        
        signature = find_signature_recursively(tx)
        if signature:
            return signature
            
    except Exception as e:
        logger.debug(f"Error in recursive signature search: {e}")
    
    return 'unknown_signature'

async def calculate_actual_max_roi(
    app: FastAPI,
    mint_address: str,
    proposal_timestamp_unix: float = None, # DEPRECATED: No longer used for time filtering
    resolution_period_seconds: int = None,  # DEPRECATED: No longer used for time filtering
    entry_price_index: int = 110  # Index of the price to use as entry (110 = 111th price from the beginning)
) -> Optional[float]:
    """
    Calculates the actual max_roi (as a profit factor) for a token based on price sequence analysis.

    Simple algorithm:
    1. Fetch oldest 8000 transactions for the token
    2. Extract prices from those transactions in chronological order
    3. Use price at index 110 (111th price) as entry price
    4. Find maximum price from index 110 onwards
    5. Calculate ROI = (max_price / entry_price) - 1

    - max_roi = 0.0 (0% profit) implies break-even from the entry_price.
    - max_roi = 1.0 (100% profit) implies price doubled from entry_price.
    - max_roi = -1.0 (-100% profit) implies total loss.
    Returns None if ROI cannot be determined.
    """
    logger.info(f"[{mint_address}] Initiating actual_max_roi calculation using price sequence analysis")
    
    # ===== IMPROVED TRANSACTION FETCHING LOGIC BASED ON fetch_chronological_transactions =====
    
    # Set parameters for signature fetching - ZERO TOLERANCE: Get ALL signatures
    fetch_limit = 1000  # RPC API limit per request - we'll use pagination for more
    batch_size = 50     # Match external code batch size exactly
    # ZERO TOLERANCE: Process ALL transactions to find true maximum ROI (use pagination)
    
    # Track processed signatures to avoid duplicates
    processed_signatures = set()
    all_transactions = []
    
    try:
        # 1. Fetch initial signatures - the function should handle pagination internally
        logger.debug(f"[{mint_address}] Fetching initial signatures for ROI calculation")
        initial_signatures = await fetch_signatures_for_address(app, mint_address, limit=fetch_limit, get_oldest=True)

        if not initial_signatures:
            logger.warning(f"[{mint_address}] No signatures found at all. Cannot calculate ROI.")
            return -1.0  # Consider total loss if no history
        
        # 2. Reverse signatures to get oldest first (since getSignaturesForAddress returns newest first)
        reversed_signatures = initial_signatures[::-1]
        logger.info(f"[{mint_address}] Found {len(initial_signatures)} signatures, processing oldest first")
        
        # 3. Process up to MAX_SIGNATURES_TO_PROCESS_FOR_ROI (8000) signatures to get enough prices
        signatures_to_process = reversed_signatures[:MAX_SIGNATURES_TO_PROCESS_FOR_ROI]
        remaining_signatures = signatures_to_process.copy()
        extracted_prices_count = 0

        # 4. Process ALL signatures in batches from oldest to newest to find true maximum ROI
        while remaining_signatures:
            # Take the next batch of oldest signatures
            current_batch = remaining_signatures[:batch_size]
            remaining_signatures = remaining_signatures[batch_size:]

            logger.debug(f"[{mint_address}] Processing batch of {len(current_batch)} signatures, remaining: {len(remaining_signatures)}")

            # Add to processed signatures set to avoid duplicates
            processed_signatures.update(sig['signature'] if isinstance(sig, dict) and 'signature' in sig else sig for sig in current_batch)

            # Fetch transactions for current batch using external methodology
            batch_signatures = [sig['signature'] if isinstance(sig, dict) else sig for sig in current_batch]
            transactions = await fetch_transaction_batch_for_price_extraction(app, batch_signatures)

            # Add rate limiting to match external code
            import asyncio
            await asyncio.sleep(1)  # 1 second delay between batches like external code
            
            if transactions:
                # Filter out None transactions if any failed to fetch
                valid_transactions = [tx for tx in transactions if tx is not None]

                # CRITICAL FIX: Do NOT sort transactions - use them in the exact order returned by API
                # The external code processes transactions in the exact order they come back from API
                # Any sorting can change the price sequence and cause discrepancies

                # Add to our chronological transaction list
                all_transactions.extend(valid_transactions)

                # Count prices extracted from this batch to check if we have enough
                batch_price_count = 0
                for tx in valid_transactions:
                    price = extract_price_from_transaction(tx, mint_address)
                    if price is not None:
                        batch_price_count += 1

                extracted_prices_count += batch_price_count
                logger.debug(f"[{mint_address}] Added {len(valid_transactions)} transactions, {batch_price_count} prices, total transactions: {len(all_transactions)}, total prices: {extracted_prices_count}")

                # Continue processing all transactions up to the 8000 limit to find true maximum ROI
        
        # 5. Verify we have transactions to process
        if not all_transactions:
            logger.warning(f"[{mint_address}] No valid transactions fetched. Cannot calculate ROI.")
            return -1.0  # Treat as total loss or unresolvable
        
        # Log detailed information about first few transactions for debugging
        logger.info(f"[{mint_address}] Successfully fetched {len(all_transactions)} transactions in chronological order.")
        
        # Enhanced debugging for first few transactions
        for i, tx in enumerate(all_transactions[:3]):  # Log first 3 transactions
            signature = extract_signature_from_transaction(tx)
            tx_timestamp = tx.get('blockTime') or tx.get('timestamp', 0)
            logger.info(f"[RiskLadder] [{mint_address}] Transaction #{i+1}: SIGNATURE={signature} | TIMESTAMP={tx_timestamp}")
            
            # Also log the complete structure of the first transaction for debugging
            if i == 0:
                logger.debug(f"[RiskLadder] [{mint_address}] First transaction keys: {list(tx.keys()) if isinstance(tx, dict) else 'Not a dict'}")
                
                # Log nested structure
                for key in (tx.keys() if isinstance(tx, dict) else []):
                    value = tx[key]
                    if isinstance(value, dict):
                        logger.debug(f"[RiskLadder] [{mint_address}] tx['{key}'] is dict with keys: {list(value.keys())}")
                    elif isinstance(value, list):
                        logger.debug(f"[RiskLadder] [{mint_address}] tx['{key}'] is list with length: {len(value)}")
                        if len(value) > 0:
                            logger.debug(f"[RiskLadder] [{mint_address}] tx['{key}'][0] type: {type(value[0])}")
                    else:
                        logger.debug(f"[RiskLadder] [{mint_address}] tx['{key}'] = {value} (type: {type(value)})")
        
        # Double-check the chronological ordering by logging a few timestamps
        if len(all_transactions) > 1:
            try:
                first_tx = all_transactions[0]
                last_tx = all_transactions[-1]
                first_time = first_tx.get('blockTime', first_tx.get('timestamp'))
                last_time = last_tx.get('blockTime', last_tx.get('timestamp'))
                
                if first_time and last_time:
                    logger.debug(f"[{mint_address}] Chronological check: First tx time: {first_time}, Last tx time: {last_time}")
                    if first_time <= last_time:
                        logger.debug(f"[{mint_address}] Transactions are in correct chronological order (oldest -> newest)")
                    else:
                        logger.warning(f"[{mint_address}] Transactions may not be in correct order - first tx is newer than last tx!")
            except Exception as e:
                logger.error(f"[{mint_address}] Error checking chronological order: {e}")
    
    except Exception as e:
        logger.error(f"[{mint_address}] Error in chronological transaction fetching: {e}", exc_info=True)
        return None  # Cannot proceed
    
    # ===== END OF IMPROVED TRANSACTION FETCHING LOGIC =====
    
    # Extract time range for price extraction
    min_tx_time = min((tx.get('blockTime') or tx.get('timestamp', float('inf'))) for tx in all_transactions) if all_transactions else 0
    max_tx_time = max((tx.get('blockTime') or tx.get('timestamp', float('-inf'))) for tx in all_transactions) if all_transactions else 0
    
    logger.debug(f"[{mint_address}] Extracting prices from transactions between {min_tx_time} and {max_tx_time}.")
    
    # Extract prices from the chronologically ordered transactions
    prices_with_ts: List[Tuple[float, float, str]] = await extract_prices_from_transactions(
        all_transactions,
        mint_address,
        min_tx_time,  # effectively process all prices from this batch
        max_tx_time   # effectively process all prices from this batch
    )

    if not prices_with_ts:
        logger.warning(f"[{mint_address}] No prices could be extracted from the fetched early transactions.")
        return -1.0 # Total loss if no price data from early txs

    logger.info(f"[{mint_address}] Extracted {len(prices_with_ts)} price points from early transactions.")

    if not prices_with_ts: # This check might be redundant if already handled, but safe
        logger.warning(f"[{mint_address}] No prices_with_ts available for ROI calculation.")
        return -1.0

    # 4. Determine Entry Price:
    # Use the price at the specified absolute index (entry_price_index) from the beginning of price history
    entry_price: Optional[float] = None
    entry_price_ts: Optional[float] = None
    entry_price_sig: Optional[str] = None

    # Check if we have enough prices to satisfy the requested index
    if prices_with_ts and entry_price_index < len(prices_with_ts):
        # Get the price at the specified absolute index from the beginning
        entry_timestamp, entry_price, entry_price_sig = prices_with_ts[entry_price_index]
        # Add more detail to the entry price signature logging
        if entry_price_sig == 'unknown_signature':
            logger.warning(f"[{mint_address}] Entry price signature is unknown. This may affect ROI analysis.")
        logger.info(f"[{mint_address}] Entry price: {entry_price:.12f} SOL at timestamp {entry_timestamp}, signature: {entry_price_sig}")
        logger.info(f"[RiskLadder] [{mint_address}] Entry price transaction: SIGNATURE={entry_price_sig} | PRICE={entry_price:.12f} SOL | TIMESTAMP={entry_timestamp} | INDEX={entry_price_index}")
        entry_price_ts = entry_timestamp
    elif prices_with_ts:
        # FIXED: If we don't have enough prices, use the FIRST available price instead of the last
        # This is more logical as the first price represents the earliest entry point
        entry_price_ts, entry_price, entry_price_sig = prices_with_ts[0]
        logger.warning(f"[{mint_address}] Requested entry_price_index {entry_price_index} exceeds available prices {len(prices_with_ts)}. Using FIRST available price instead.")
        logger.info(f"[{mint_address}] Entry price (index 0, the FIRST price) for ROI calc identified: {entry_price:.12f} SOL at ts {entry_price_ts}, signature: {entry_price_sig}")
        # Additional detailed logging for testing purposes
        logger.info(f"[RiskLadder] [{mint_address}] Entry price transaction (fallback): SIGNATURE={entry_price_sig} | PRICE={entry_price:.12f} SOL | TIMESTAMP={entry_price_ts} | INDEX=0")
    else:
        # No prices found at all
        logger.warning(f"[{mint_address}] No prices found within the fetched transactions. Cannot determine entry price.")
        return -1.0  # Consider this unresolvable or a failure scenario for ROI.

    if entry_price is None or entry_price <= 1e-18: # Effectively zero, cannot be a valid entry price for ROI calculation
        logger.warning(f"[{mint_address}] Identified entry price {entry_price:.12f} is too low or zero. Cannot calculate ROI.")
        return -1.0 # Total loss or invalid

    # 5. Find Max Price from entry_price_index onwards (no time window constraints)
    # Simple algorithm: scan all prices from entry index onwards and find the maximum

    max_observed_price = entry_price # Initialize with entry price
    max_price_ts = entry_price_ts
    max_price_sig = entry_price_sig

    # Find maximum price from entry_price_index onwards (NO FILTERING - process all prices)
    # ZERO TOLERANCE: Process identical blockchain data identically
    for i in range(entry_price_index, len(prices_with_ts)):
        ts, price_val, sig = prices_with_ts[i]

        # No filtering - consider ALL prices to ensure zero discrepancy
        if price_val > max_observed_price:
            max_observed_price = price_val
            max_price_ts = ts
            max_price_sig = sig
    
    # Add more detail to the max price signature logging
    if max_price_sig == 'unknown_signature':
        logger.warning(f"[{mint_address}] Max price signature is unknown. This may affect ROI analysis.")

    logger.info(f"[{mint_address}] Max price observed (from entry index {entry_price_index} onwards): {max_observed_price:.12f} SOL at ts {max_price_ts}, signature: {max_price_sig}")

    # Additional detailed logging for testing purposes
    logger.info(f"[RiskLadder] [{mint_address}] Max price transaction: SIGNATURE={max_price_sig} | PRICE={max_observed_price:.12f} SOL | TIMESTAMP={max_price_ts}")

    # Print a summary of the ROI calculation for testing
    roi_factor = (max_observed_price / entry_price) - 1.0
    logger.info(f"[RiskLadder] ROI CALCULATION SUMMARY for {mint_address}:\n" +
                f"  - Entry Price: {entry_price:.12f} SOL (Index: {entry_price_index}, Signature: {entry_price_sig})\n" +
                f"  - Max Price: {max_observed_price:.12f} SOL (Signature: {max_price_sig})\n" +
                f"  - ROI Factor: {roi_factor:.4f} ({roi_factor*100:.2f}%)")


    # 6. Calculate actual_max_roi (as profit factor)
    # profit_factor = (final_value / initial_value) - 1.0
    # Here, initial_value = entry_price, final_value = max_observed_price

    if max_observed_price < 1e-18: # Effectively zero or rugged after entry
        actual_max_roi = -1.0 # Total loss relative to entry
    else:
        actual_max_roi = (max_observed_price / entry_price) - 1.0

    # Clip to sensible bounds, e.g. -1.0 to a very large number, handle NaNs/Infs
    if pd.isna(actual_max_roi) or np.isinf(actual_max_roi):
        logger.warning(f"[{mint_address}] Calculated ROI is NaN or Inf. Entry: {entry_price}, Max: {max_observed_price}. Defaulting to -1.0.")
        actual_max_roi = -1.0
    else:
        actual_max_roi = float(np.clip(actual_max_roi, -1.0, 1_000_000.0)) # Clip extreme positive ROI

    logger.info(f"[{mint_address}] Final calculated actual_max_roi (profit factor): {actual_max_roi:.4f}")
    return actual_max_roi

async def extract_prices_from_transactions(
    transactions: List[Dict[str, Any]],
    mint_address: str,
    window_start_time: float = None,  # DEPRECATED: No longer used for filtering
    window_end_time: float = None     # DEPRECATED: No longer used for filtering
) -> List[Tuple[float, float, str]]:
    """
    Extracts prices from transaction data for a specific mint_address token.

    Args:
        transactions: List of transaction objects from Solana API
        mint_address: The token mint address to extract prices for
        window_start_time: DEPRECATED - No longer used for time filtering
        window_end_time: DEPRECATED - No longer used for time filtering

    Returns:
        List of tuples, each containing (timestamp, price_in_sol_per_token, signature)
        in chronological order (preserving input order)
    """
    logger.debug(f"[{mint_address}] Extracting prices from {len(transactions)} transactions (no time filtering)")
    
    # Initialize results list
    prices_with_ts: List[Tuple[float, float, str]] = []
    
    # IMPORTANT: Do NOT sort transactions by blockTime here as timestamps only have 1-second precision.
    # Instead, we maintain the order from fetch_transaction_batch which preserves chronology
    # based on the signature order from fetch_signatures_for_address
    # This matches the approach in fetch_chronological_transactions
    sorted_transactions = transactions
    
    # Process each transaction (no time window filtering)
    for i, tx in enumerate(sorted_transactions):
        # Get the transaction timestamp
        tx_timestamp = tx.get('blockTime') or tx.get('timestamp', 0)

        # Extract the signature first using our robust function
        signature = extract_signature_from_transaction(tx)

        # Extract the price from this transaction
        price = extract_price_from_transaction(tx, mint_address)
        
        if price is not None and price > 0:
            # Log detailed information for the first few transactions that yield prices
            if len(prices_with_ts) < 5:  # Log first 5 price-yielding transactions
                logger.info(f"[RiskLadder] [{mint_address}] Transaction #{i+1} yielded price: SIGNATURE={signature} | PRICE={price:.12f} SOL | TIMESTAMP={tx_timestamp}")
                
                # For the very first price-yielding transaction, log even more detail
                if len(prices_with_ts) == 0:
                    logger.info(f"[RiskLadder] [{mint_address}] FIRST PRICE-YIELDING TRANSACTION DETAILS:")
                    logger.info(f"[RiskLadder] [{mint_address}]   - Transaction Index: {i+1}")
                    logger.info(f"[RiskLadder] [{mint_address}]   - Signature: {signature}")
                    logger.info(f"[RiskLadder] [{mint_address}]   - Price: {price:.12f} SOL")
                    logger.info(f"[RiskLadder] [{mint_address}]   - Timestamp: {tx_timestamp}")
                    logger.info(f"[RiskLadder] [{mint_address}]   - Transaction Keys: {list(tx.keys()) if isinstance(tx, dict) else 'Not a dict'}")
                    
                    # Log the actions that yielded the price
                    actions = tx.get("actions", [])
                    logger.info(f"[RiskLadder] [{mint_address}]   - Number of actions: {len(actions)}")
                    for j, action in enumerate(actions):
                        action_type = action.get("type", "UNKNOWN")
                        logger.info(f"[RiskLadder] [{mint_address}]   - Action {j+1}: {action_type}")
                        if action_type == "SWAP":
                            info = action.get("info", {})
                            tokens_swapped = info.get("tokens_swapped", {})
                            if tokens_swapped:
                                in_token = tokens_swapped.get("in", {})
                                out_token = tokens_swapped.get("out", {})
                                logger.info(f"[RiskLadder] [{mint_address}]     - In: {in_token.get('amount', 0)} {in_token.get('token_address', 'unknown')}")
                                logger.info(f"[RiskLadder] [{mint_address}]     - Out: {out_token.get('amount', 0)} {out_token.get('token_address', 'unknown')}")
            
            # Add timestamp, price, and signature to the results
            prices_with_ts.append((tx_timestamp, price, signature))
    
    # CRITICAL FIX: Do NOT sort by timestamp! This changes the order of transactions
    # with identical timestamps, causing price sequence discrepancies with reference system.
    # The transactions are already in correct chronological order from the API.
    # Sorting by timestamp reorders transactions within the same second, breaking price sequence alignment.
    
    # Log comprehensive details of the first price for debugging
    if prices_with_ts:
        first_price_ts, first_price_value, first_price_sig = prices_with_ts[0]
        logger.info(f"[{mint_address}] FIRST PRICE EXTRACTED: {first_price_value:.12f} SOL at timestamp {first_price_ts}, signature: {first_price_sig}")
        logger.info(f"[RiskLadder] [{mint_address}] First price summary: SIGNATURE={first_price_sig} | PRICE={first_price_value:.12f} SOL | TIMESTAMP={first_price_ts}")
        
        # Also log the last price for comparison
        if len(prices_with_ts) > 1:
            last_price_ts, last_price_value, last_price_sig = prices_with_ts[-1]
            logger.info(f"[RiskLadder] [{mint_address}] Last price summary: SIGNATURE={last_price_sig} | PRICE={last_price_value:.12f} SOL | TIMESTAMP={last_price_ts}")
    else:
        logger.warning(f"[{mint_address}] No prices extracted from transactions")
    
    logger.debug(f"[{mint_address}] Extracted {len(prices_with_ts)} price points")
    return prices_with_ts


def extract_price_from_transaction(tx: Dict[str, Any], mint_address: str) -> Optional[float]:
    """
    Extract a price (in SOL per token) from a single transaction.
    Updated to match the external price extraction methodology exactly.

    Args:
        tx: Transaction object with timestamp, status, actions fields (external format)
        mint_address: The token mint address to extract price for

    Returns:
        Price as SOL per token unit, or None if price can't be determined
    """
    try:
        # Check if transaction was successful - match external code exactly
        status = tx.get('status', '')
        if status != 'Success':
            return None

        # Look for SWAP actions - match external methodology exactly
        actions = tx.get('actions', [])
        for action in actions:
            if action.get('type') == 'SWAP':
                info = action.get('info', {})
                tokens_swapped = info.get('tokens_swapped', {})

                if tokens_swapped:
                    in_token = tokens_swapped.get('in', {})
                    out_token = tokens_swapped.get('out', {})

                    # Extract amounts and addresses
                    in_amount_str = in_token.get('amount', '0')
                    out_amount_str = out_token.get('amount', '0')
                    in_address = in_token.get('token_address', '')
                    out_address = out_token.get('token_address', '')

                    # Use high precision Decimal arithmetic to match reference system exactly
                    from decimal import Decimal, getcontext

                    # Set high precision for exact calculations
                    getcontext().prec = 50  # Use 50 decimal places for maximum precision

                    try:
                        # Convert to Decimal for high precision arithmetic
                        in_amount = Decimal(str(in_amount_str))
                        out_amount = Decimal(str(out_amount_str))
                    except (ValueError, TypeError, decimal.InvalidOperation):
                        continue

                    # CRITICAL: Filter out tiny transactions to match reference system exactly
                    # Reference system excludes very small SOL amounts that create noise
                    # Transaction 8 had 1.02e-06 SOL (0.00000102) - this should be filtered
                    # But 0.001 SOL is too aggressive, try smaller threshold
                    MIN_SOL_AMOUNT_FOR_PRICE = Decimal('0.000005')  # 0.000005 SOL (5 microSOL) minimum threshold

                    # Check if SOL amount meets minimum threshold
                    sol_amount = None
                    if in_address == SOL_MINT_ADDRESS:
                        sol_amount = in_amount
                    elif out_address == SOL_MINT_ADDRESS:
                        sol_amount = out_amount

                    if sol_amount is None or sol_amount < MIN_SOL_AMOUNT_FOR_PRICE:
                        continue  # Skip tiny transactions that reference system filters out

                    # Calculate price based on swap direction using high precision arithmetic
                    if (in_address == SOL_MINT_ADDRESS and out_address == mint_address and
                        in_amount > 0 and out_amount > 0):
                        # SOL -> Token: price = SOL_amount / token_amount
                        # Use Decimal division for exact precision matching reference system
                        price_decimal = in_amount / out_amount
                        return float(price_decimal)  # Convert back to float for compatibility
                    elif (out_address == SOL_MINT_ADDRESS and in_address == mint_address and
                          in_amount > 0 and out_amount > 0):
                        # Token -> SOL: price = SOL_amount / token_amount
                        # Use Decimal division for exact precision matching reference system
                        price_decimal = out_amount / in_amount
                        return float(price_decimal)  # Convert back to float for compatibility

        return None

    except Exception as e:
        logger.debug(f"Error extracting price from transaction: {e}")
        return None
