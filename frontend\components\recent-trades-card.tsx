"use client";

import React, { useState } from 'react';
import { History, TrendingUp, TrendingDown, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { TradeItem } from '@/types/api';
import { useAuth } from '@/components/auth-context';
import { cn } from '@/lib/utils';

interface RecentTradesCardProps {
    trades: TradeItem[] | undefined;
    isLoading?: boolean;
    activeView?: "open_positions" | "history";
    setActiveView?: (view: "open_positions" | "history") => void;
}

const RecentTradesCard: React.FC<RecentTradesCardProps> = ({ 
    trades, 
    isLoading = false,
    activeView = "open_positions",
    setActiveView
}) => {
    const { isAuthenticated } = useAuth();
    const [localActiveView, setLocalActiveView] = useState<"open_positions" | "history">(activeView);
    
    // Handler for tab change
    const handleViewChange = (view: "open_positions" | "history") => {
        if (setActiveView) {
            setActiveView(view);
        } else {
            setLocalActiveView(view);
        }
    };

    // Use either the prop or local state
    const currentView = setActiveView ? activeView : localActiveView;
    
    // Filter to only show SELL transactions (closed positions) and limit to last 10
    const completedTrades = trades 
        ? trades
            .filter(trade => trade.trade_type === 'SELL')
            .slice(0, 10)
        : [];

    // Calculate amount invested for a SELL trade
    const getAmountInvested = (trade: TradeItem): number => {
        // For a sell transaction, amount invested = amount received - profit
        // Or equivalently: amount received - pnl_sol
        if (trade.pnl_sol !== null) {
            return trade.total_sol - trade.pnl_sol;
        }
        // If pnl_sol is null, we can't calculate the investment amount
        return 0;
    };

    // Calculate stats
    const totalProfit = completedTrades.reduce((sum, trade) => sum + (trade.pnl_sol || 0), 0);
    const profitableTradesCount = completedTrades.filter(trade => (trade.pnl_sol || 0) > 0).length;
    const winRate = completedTrades.length > 0 
        ? (profitableTradesCount / completedTrades.length) * 100 
        : 0;

    // Calculate trade history stats
    const allTradesProfit = trades
        ? trades
            .filter(trade => trade.trade_type === 'SELL' && trade.pnl_sol !== null)
            .reduce((sum, trade) => sum + (trade.pnl_sol || 0), 0)
        : 0;
    
    const allProfitableTradesCount = trades
        ? trades
            .filter(trade => trade.trade_type === 'SELL' && (trade.pnl_sol || 0) > 0)
            .length
        : 0;
    
    const allWinRate = trades && trades.filter(trade => trade.trade_type === 'SELL').length > 0 
        ? (allProfitableTradesCount / trades.filter(trade => trade.trade_type === 'SELL').length) * 100 
        : 0;

    return (
        <Card className="glass-card glass-card-hover">
            <CardHeader className="p-0">
                <CardTitle>Your Trades</CardTitle>
            </CardHeader>
            
            <CardContent>
                {/* Tab Navigation - Spans full width */}
                <div className="border-b border-white/10 mb-5">
                    <div className="flex">
                        <button
                            onClick={() => handleViewChange("open_positions")}
                            className={cn(
                                "subheading-text-style relative px-4 py-2 transition-all duration-200 flex-1 text-center",
                                currentView === "open_positions"
                                    ? "text-white"
                                    : "text-white/30 hover:text-white"
                            )}
                        >
                            Recent Trades
                            {currentView === "open_positions" && (
                                <div className="absolute bottom-0 left-0 w-full h-[1px] bg-white"></div>
                            )}
                        </button>
                        <button
                            onClick={() => handleViewChange("history")}
                            className={cn(
                                "subheading-text-style relative px-4 py-2 transition-all duration-200 flex-1 text-center",
                                currentView === "history"
                                    ? "text-white"
                                    : "text-white/30 hover:text-white"
                            )}
                        >
                            Trade History
                            {currentView === "history" && (
                                <div className="absolute bottom-0 left-0 w-full h-[1px] bg-white"></div>
                            )}
                        </button>
                    </div>
                </div>
                
                {/* Stats Section */}
                <div className="flex justify-end mb-4">
                    <div className="flex gap-2">
                        {completedTrades.length > 0 && isAuthenticated && currentView === "open_positions" && (
                            <>
                                <div className="bg-[#0D0F1A] px-3 py-1 rounded-full text-xs border border-[rgba(75,75,75,0.15)] flex items-center gap-1">
                                    <span className="text-white/60 mr-1">Total P&L:</span>
                                    <span className={`text-white/90 font-medium ${totalProfit >= 0 ? 'text-status-success' : 'text-[#E64C4C]'}`}>
                                        {totalProfit >= 0 ? '+' : ''}{totalProfit.toFixed(4)} SOL
                                    </span>
                                </div>
                                <div className="bg-[#0D0F1A] px-3 py-1 rounded-full text-xs border border-[rgba(75,75,75,0.15)]">
                                    <span className="text-white/60 mr-1">Win Rate:</span>
                                    <span className="text-white/90 font-medium">{winRate.toFixed(0)}%</span>
                                </div>
                            </>
                        )}
                        {trades && trades.length > 0 && isAuthenticated && currentView === "history" && (
                            <>
                                <div className="bg-[#0D0F1A] px-3 py-1 rounded-full text-xs border border-[rgba(75,75,75,0.15)] flex items-center gap-1">
                                    <span className="text-white/60 mr-1">Total P&L:</span>
                                    <span className={`text-white/90 font-medium ${allTradesProfit >= 0 ? 'text-status-success' : 'text-[#E64C4C]'}`}>
                                        {allTradesProfit >= 0 ? '+' : ''}{allTradesProfit.toFixed(4)} SOL
                                    </span>
                                </div>
                                <div className="bg-[#0D0F1A] px-3 py-1 rounded-full text-xs border border-[rgba(75,75,75,0.15)]">
                                    <span className="text-white/60 mr-1">Win Rate:</span>
                                    <span className="text-white/90 font-medium">{allWinRate.toFixed(0)}%</span>
                                </div>
                            </>
                        )}
                    </div>
                </div>
                
                {/* Content based on active view */}
                {currentView === "open_positions" ? (
                    // Recent Trades View
                    <div className="p-0">
                        {!isAuthenticated ? (
                            <div className="flex flex-col items-center justify-center p-10 text-center">
                                <Shield className="h-16 w-16 text-foreground/20 mb-4" />
                                <h3 className="text-xl font-semibold mb-2 text-foreground/20">Authentication Required</h3>
                                <p className="text-foreground/20 max-w-md">
                                    Please connect wallet and sign in to view recent trades.
                                </p>
                            </div>
                        ) : isLoading ? (
                            <div className="flex justify-center items-center h-64">
                                <div className="bg-[#111111] animate-pulse w-full h-32"></div>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader className="bg-[#0A0A0A] border-[rgba(75,75,75,0.15)]">
                                        <TableRow className="hover:bg-transparent border-[rgba(75,75,75,0.15)]">
                                            <TableHead className="text-white/60">Timestamp</TableHead>
                                            <TableHead className="text-white/60">Token</TableHead>
                                            <TableHead className="text-white/60">Amount Invested</TableHead>
                                            <TableHead className="text-white/60">Amount Received</TableHead>
                                            <TableHead className="text-white/60">P&L</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {!completedTrades || completedTrades.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={5} className="text-center py-8 text-white/40">
                                                    No completed trades
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            completedTrades.map((trade) => (
                                                <TableRow key={trade.id} className="hover:bg-[#111111] border-[rgba(75,75,75,0.15)]">
                                                    <TableCell className="font-mono text-xs text-white/80">
                                                        {new Date(trade.timestamp).toLocaleString()}
                                                    </TableCell>
                                                    <TableCell className="font-mono text-accent">
                                                        {trade.token_mint.length > 8
                                                            ? `${trade.token_mint.substring(0, 4)}...${trade.token_mint.substring(trade.token_mint.length - 3)}`
                                                            : trade.token_mint}
                                                    </TableCell>
                                                    <TableCell className="font-mono text-white/80">{getAmountInvested(trade).toFixed(4)}</TableCell>
                                                    <TableCell className="font-mono text-white/80">{trade.total_sol.toFixed(4)}</TableCell>
                                                    <TableCell className="font-mono">
                                                        {trade.pnl_sol !== null ? (
                                                            <div className="flex items-center gap-1">
                                                                {trade.pnl_sol >= 0 
                                                                    ? <TrendingUp className="h-3 w-3" /> 
                                                                    : <TrendingDown className="h-3 w-3" />}
                                                                <span className={trade.pnl_sol >= 0 ? "text-status-success" : "text-status-error"}>
                                                                    {trade.pnl_sol >= 0 ? '+' : ''}{trade.pnl_sol.toFixed(4)}
                                                                </span>
                                                            </div>
                                                        ) : (
                                                            '-'
                                                        )}
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </div>
                ) : (
                    // Trade History View
                    <div className="p-0">
                        {!isAuthenticated ? (
                            <div className="flex flex-col items-center justify-center p-10 text-center">
                                <Shield className="h-16 w-16 text-foreground/20 mb-4" />
                                <h3 className="text-xl font-semibold mb-2 text-foreground/20">Authentication Required</h3>
                                <p className="text-foreground/20 max-w-md">
                                    Please connect wallet and sign in to view trade history.
                                </p>
                            </div>
                        ) : isLoading ? (
                            <div className="flex justify-center items-center h-64">
                                <div className="bg-[#111111] animate-pulse w-full h-32"></div>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader className="bg-[#0A0A0A] border-[rgba(75,75,75,0.15)]">
                                        <TableRow className="hover:bg-transparent border-[rgba(75,75,75,0.15)]">
                                            <TableHead className="text-white/60">Timestamp</TableHead>
                                            <TableHead className="text-white/60">Type</TableHead>
                                            <TableHead className="text-white/60">Token</TableHead>
                                            <TableHead className="text-white/60">Amount</TableHead>
                                            <TableHead className="text-white/60">Price (SOL)</TableHead>
                                            <TableHead className="text-white/60">Total (SOL)</TableHead>
                                            <TableHead className="text-white/60">P&L (SOL)</TableHead>
                                            <TableHead className="text-white/60">Tx</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {!trades || trades.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={8} className="text-center py-8 text-white/40">
                                                    No trade history found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            trades.map((trade) => (
                                                <TableRow key={trade.id} className="hover:bg-[#111111] border-[rgba(75,75,75,0.15)]">
                                                    <TableCell className="font-mono text-xs text-white/80">
                                                        {new Date(trade.timestamp).toLocaleString()}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={trade.trade_type === 'BUY' ? 'success' : 'destructive'} 
                                                        className="flex items-center gap-1">
                                                        {trade.trade_type === 'BUY' 
                                                            ? <><TrendingUp className="h-3 w-3" /> BUY</>
                                                            : <><TrendingDown className="h-3 w-3" /> SELL</>}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell className="font-mono text-accent">
                                                        {trade.token_mint.length > 8 
                                                        ? `${trade.token_mint.substring(0, 4)}...${trade.token_mint.substring(trade.token_mint.length - 3)}` 
                                                        : trade.token_mint}
                                                    </TableCell>
                                                    <TableCell className="font-mono text-white/80">{trade.amount_token.toLocaleString()}</TableCell>
                                                    <TableCell className="font-mono text-white/80">{trade.price_sol.toFixed(8)}</TableCell>
                                                    <TableCell className="font-mono text-white/80">{trade.total_sol.toFixed(4)}</TableCell>
                                                    <TableCell className="font-mono">
                                                        {trade.pnl_sol !== null ? (
                                                        <div className="flex items-center gap-1">
                                                            {trade.pnl_sol >= 0 
                                                            ? <TrendingUp className="h-3 w-3" /> 
                                                            : <TrendingDown className="h-3 w-3" />}
                                                            <span className={trade.pnl_sol >= 0 ? "text-status-success" : "text-status-error"}>
                                                            {trade.pnl_sol >= 0 ? '+' : ''}{trade.pnl_sol.toFixed(4)}
                                                            </span>
                                                        </div>
                                                        ) : (
                                                        '-'
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        {trade.tx_signature ? (
                                                        <a 
                                                            href={`https://solscan.io/tx/${trade.tx_signature}`} 
                                                            target="_blank" 
                                                            rel="noopener noreferrer" 
                                                            className="text-accent hover:text-accent/80 hover:underline font-mono text-xs"
                                                        >
                                                            {trade.tx_signature.substring(0, 4)}...{trade.tx_signature.substring(trade.tx_signature.length - 3)}
                                                        </a>
                                                        ) : (
                                                        <span className="text-white/30">-</span>
                                                        )}
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </div>
                )}
                
                {/* Pagination for Trade History View */}
                {currentView === "history" && trades && trades.length > 10 && (
                    <div className="border-t border-[rgba(75,75,75,0.15)] p-4 flex justify-center mt-4">
                        <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm" className="bg-[#0A0A0A] text-white/70 border-[rgba(75,75,75,0.15)]">
                                Previous
                            </Button>
                            <span className="text-sm text-white/70">Page 1 of {Math.ceil(trades.length / 10)}</span>
                            <Button variant="outline" size="sm" className="bg-[#0A0A0A] text-white/70 border-[rgba(75,75,75,0.15)]">
                                Next
                            </Button>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default RecentTradesCard; 