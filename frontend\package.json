{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coral-xyz/anchor": "^0.31.0", "@hookform/resolvers": "^3.9.1", "@jup-ag/api": "^6.0.40", "@pump-fun/pump-sdk": "^1.3.8", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.24", "@solana/wallet-adapter-phantom": "^0.9.25", "@solana/wallet-adapter-react": "^0.15.36", "@solana/wallet-adapter-react-ui": "^0.9.36", "@solana/web3.js": "^1.98.0", "autoprefixer": "^10.4.20", "bn.js": "^5.2.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.0.0", "embla-carousel-react": "8.5.1", "glob": "^11.0.2", "gsap": "^3.12.5", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "maath": "^0.10.8", "next": "15.2.4", "next-themes": "^0.4.4", "ogl": "^1.0.11", "react": "^19", "react-day-picker": "^9.1.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@types/bn.js": "^5.1.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.177.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}