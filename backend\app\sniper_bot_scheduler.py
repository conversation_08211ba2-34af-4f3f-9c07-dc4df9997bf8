import asyncio
import logging
from datetime import datetime, time, timedelta
from typing import Optional
from fastapi import FastAP<PERSON>

from solbot.config import (
    SNIPER_BOT_AUTO_RESOLUTION_ENABLED,
    SNIPER_BOT_DAILY_RESOLUTION_HOUR
)

logger = logging.getLogger(__name__)

class SniperBotScheduler:
    """
    Simple asyncio-based scheduler for sniper bot batch resolution.
    
    Runs daily at the configured hour and provides manual trigger capability.
    """
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Start the scheduler service."""
        if self.is_running:
            logger.warning("SniperBotScheduler: Scheduler is already running.")
            return
        
        if not SNIPER_BOT_AUTO_RESOLUTION_ENABLED:
            logger.info("SniperBotScheduler: Auto resolution is disabled. Scheduler will not start.")
            return
        
        self.is_running = True
        logger.info(f"SniperBotScheduler: Starting daily scheduler. Will run at {SNIPER_BOT_DAILY_RESOLUTION_HOUR}:00 daily.")
        
        # Start the scheduler loop
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
    
    async def stop(self):
        """Stop the scheduler service."""
        self.is_running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("SniperBotScheduler: Scheduler stopped.")
    
    async def _scheduler_loop(self):
        """Main scheduler loop that runs daily at the configured time."""
        try:
            while self.is_running:
                # Calculate next run time
                next_run = self._get_next_run_time()
                now = datetime.now()
                
                # Calculate sleep duration
                sleep_duration = (next_run - now).total_seconds()
                
                logger.info(f"SniperBotScheduler: Next batch resolution scheduled for {next_run} "
                           f"(in {sleep_duration/3600:.1f} hours)")
                
                # Sleep until next run time
                if sleep_duration > 0:
                    await asyncio.sleep(sleep_duration)
                
                # Check if we're still running after sleep
                if not self.is_running:
                    break
                
                # Execute the batch resolution
                await self._execute_scheduled_resolution()
                
                # Small delay to avoid immediate re-execution
                await asyncio.sleep(60)
                
        except asyncio.CancelledError:
            logger.info("SniperBotScheduler: Scheduler loop cancelled.")
        except Exception as e:
            logger.error(f"SniperBotScheduler: Error in scheduler loop: {e}", exc_info=True)
    
    def _get_next_run_time(self) -> datetime:
        """Calculate the next run time based on the configured hour."""
        now = datetime.now()
        
        # Create target time for today
        target_time = time(hour=SNIPER_BOT_DAILY_RESOLUTION_HOUR, minute=0, second=0)
        today_target = datetime.combine(now.date(), target_time)
        
        # If target time has already passed today, schedule for tomorrow
        if now >= today_target:
            next_run = today_target + timedelta(days=1)
        else:
            next_run = today_target
        
        return next_run
    
    async def _execute_scheduled_resolution(self):
        """Execute the scheduled batch resolution."""
        try:
            logger.info("SniperBotScheduler: Executing scheduled batch resolution.")
            
            # Import here to avoid circular imports
            from app.sniper_bot_batch_resolver import resolve_sniper_bot_batch
            
            # Execute the batch resolution
            result = await resolve_sniper_bot_batch(self.app)
            
            logger.info(f"SniperBotScheduler: Scheduled batch resolution completed. Result: {result}")
            
        except Exception as e:
            logger.error(f"SniperBotScheduler: Error executing scheduled resolution: {e}", exc_info=True)


# Global scheduler instance
_scheduler: Optional[SniperBotScheduler] = None

async def start_sniper_bot_scheduler(app: FastAPI):
    """Start the sniper bot scheduler as a background service."""
    global _scheduler
    
    if _scheduler is not None:
        logger.warning("SniperBotScheduler: Scheduler already exists.")
        return
    
    _scheduler = SniperBotScheduler(app)
    await _scheduler.start()

async def stop_sniper_bot_scheduler():
    """Stop the sniper bot scheduler."""
    global _scheduler
    
    if _scheduler is not None:
        await _scheduler.stop()
        _scheduler = None
