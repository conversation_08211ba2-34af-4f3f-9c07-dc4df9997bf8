from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
from sqlalchemy import select, func, and_, update
import logging
import time
from typing import List, Dict, Optional

from app.database import get_db_session
from app.schemas.dashboard_schemas import DashboardDataResponse
from app.schemas.bot_schemas import BotStateResponse, BotStatusEnum
from app.schemas.dashboard_schemas import HoldingItem, TradeItem
from app.db_models import Holding, Trade, User, TradeTypeEnum
from app.crud.bot_state_crud import get_bot_state
from app.crud.holding_crud import holding_crud
from app.crud.trade_crud import trade_crud
from app.dependencies import get_current_user, CustomRateLimiter
from app.periodic_tasks import cleanup_stale_holdings
import asyncio

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


@router.get("/data", response_model=DashboardDataResponse)
async def get_dashboard_data(request: Request, db: AsyncSession = Depends(get_db_session), current_user: User = Depends(get_current_user)):
    logger.debug(f"Fetching dashboard data for user: {current_user.wallet_address}")
    
    try:
        # --- Data Fetching Logic ---
        # Fetch Bot State
        bot_state_db = await get_bot_state(db, current_user.wallet_address)
        
        # Create a standard Python dictionary to hold all bot state data
        bot_state_dict = {}
        if not bot_state_db:
            bot_state_dict = {
                "user_wallet": current_user.wallet_address,
                "status": BotStatusEnum.STOPPED,
                "last_changed": datetime.utcnow(),
                "total_open_positions": 0, # Default if no state
                # Initialize other session fields to None/0
                "session_start_time": None,
                "session_uptime_seconds": None,
                "session_predicted_signals": 0,
                "session_buy_count": 0,
                "session_sell_count": 0,
                "session_sol_invested": 0.0,
                "session_sol_received": 0.0,
                "session_pnl_sol": 0.0,
                "session_pnl_percent": 0.0,
            }
            # Fetch total open positions even if bot state missing
            try:
                open_positions_query = select(func.count(Holding.id)).where(Holding.user_wallet == current_user.wallet_address)
                open_positions_res = await db.execute(open_positions_query)
                bot_state_dict["total_open_positions"] = open_positions_res.scalar_one_or_none() or 0
            except Exception as db_exc:
                logger.error(f"Error fetching open positions for {current_user.wallet_address} (no bot state): {db_exc}")
                bot_state_dict["total_open_positions"] = 0
        else:
            # Populate from DB object
            bot_state_dict = {
                "user_wallet": bot_state_db.user_wallet,
                "status": bot_state_db.status,
                "last_changed": bot_state_db.last_changed,
                "session_start_time": bot_state_db.session_start_time,
                # Initialize session fields - they will be overwritten if bot is running
                "session_uptime_seconds": None,
                "session_predicted_signals": 0,
                "session_buy_count": 0,
                "session_sell_count": 0,
                "session_sol_invested": 0.0,
                "session_sol_received": 0.0,
                "session_pnl_sol": 0.0,
                "session_pnl_percent": 0.0,
            }
            # Fetch total open positions
            try:
                open_positions_query = select(func.count(Holding.id)).where(Holding.user_wallet == current_user.wallet_address)
                open_positions_res = await db.execute(open_positions_query)
                bot_state_dict["total_open_positions"] = open_positions_res.scalar_one_or_none() or 0
            except Exception as db_exc:
                logger.error(f"Error fetching open positions for {current_user.wallet_address}: {db_exc}")
                bot_state_dict["total_open_positions"] = 0 # Default on error

            # If bot is running, calculate and add session stats to the DICTIONARY
            if bot_state_db.status == BotStatusEnum.RUNNING and bot_state_db.session_start_time:
                logger.info(f"[API Dashboard] Bot is RUNNING for {current_user.wallet_address}. Calculating session stats.")
                start_time = bot_state_db.session_start_time
                # Uptime
                uptime_seconds = (datetime.utcnow() - start_time).total_seconds()
                bot_state_dict["session_uptime_seconds"] = uptime_seconds
                
                # Get session trade stats
                # First get the BUY transactions
                try:
                    buy_stats = await db.execute(
                        select(
                            func.count().label('count'),
                            func.sum(Trade.total_sol).label('total_sol')
                        ).where(
                            and_(
                                Trade.user_wallet == current_user.wallet_address,
                                Trade.trade_type == TradeTypeEnum.BUY,
                                Trade.timestamp >= start_time
                            )
                        )
                    )
                    buy_stats_result = buy_stats.first()
                    
                    # Then get the SELL transactions
                    sell_stats = await db.execute(
                        select(
                            func.count().label('count'),
                            func.sum(Trade.total_sol).label('total_sol'),
                            func.sum(Trade.pnl_sol).label('pnl_sol')
                        ).where(
                            and_(
                                Trade.user_wallet == current_user.wallet_address,
                                Trade.trade_type == TradeTypeEnum.SELL,
                                Trade.timestamp >= start_time
                            )
                        )
                    )
                    sell_stats_result = sell_stats.first()
                    
                    # Process buy stats
                    buy_count = buy_stats_result.count or 0
                    sol_invested = buy_stats_result.total_sol or 0.0
                    
                    # Process sell stats
                    sell_count = sell_stats_result.count or 0
                    sol_received = sell_stats_result.total_sol or 0.0
                    pnl_sol = sell_stats_result.pnl_sol or 0.0
                    
                    # Update the dictionary with the values
                    bot_state_dict["session_buy_count"] = buy_count
                    bot_state_dict["session_sell_count"] = sell_count
                    bot_state_dict["session_sol_invested"] = sol_invested
                    bot_state_dict["session_sol_received"] = sol_received
                    bot_state_dict["session_pnl_sol"] = pnl_sol
                    
                    # Calculate PNL %
                    if sol_invested > 1e-9:
                        bot_state_dict["session_pnl_percent"] = (pnl_sol / sol_invested) * 100.0
                    else:
                        bot_state_dict["session_pnl_percent"] = 0.0
                    
                except Exception as db_exc:
                    logger.error(f"[API Dashboard] Error querying session stats from DB for {current_user.wallet_address}: {db_exc}", exc_info=True)
                    # Ensure fields are None if query fails
                    bot_state_dict["session_buy_count"] = None
                    bot_state_dict["session_sell_count"] = None
                    bot_state_dict["session_sol_invested"] = None
                    bot_state_dict["session_sol_received"] = None
                    bot_state_dict["session_pnl_sol"] = None
                    bot_state_dict["session_pnl_percent"] = None
                
                # Predicted Signals (In-Memory)
                predicted_signals = 0 # Default
                if hasattr(request.app.state, 'bot_manager'):
                    bot_manager = request.app.state.bot_manager
                    user_context = bot_manager.user_contexts.get(current_user.wallet_address)
                    if user_context:
                        predicted_signals = user_context.session_predicted_signals
                        logger.info(f"[API Dashboard] Context - Predicted Signals value from context: {predicted_signals}")
                    else:
                        logger.warning(f"[API Dashboard] No active UserBotContext found for running bot {current_user.wallet_address}!")
                else:
                    logger.warning("Bot manager not found in app.state")
                bot_state_dict["session_predicted_signals"] = predicted_signals

        # --- Final Pydantic Validation ---
        # Now, validate the complete dictionary into the Pydantic model
        try:
            bot_state_data_final = BotStateResponse.model_validate(bot_state_dict)
            logger.debug(f"[API Dashboard] Final bot_state_data model: {bot_state_data_final.model_dump()}") # Log the final model
        except Exception as validation_err:
            logger.error(f"Pydantic validation failed for BotStateResponse: {validation_err}", exc_info=True)
            logger.error(f"Data dictionary causing validation error: {bot_state_dict}")
            # Handle validation error - maybe return default stopped state?
            raise HTTPException(status_code=500, detail="Internal server error processing bot state")

        # Fetch Holdings
        holdings_db: List[Holding] = await holding_crud.get_holdings_by_user(db, current_user.wallet_address)
        logger.debug(f"[{current_user.wallet_address}] Fetched {len(holdings_db)} holdings from DB via CRUD.")
        # Log the actual mints fetched for verification
        fetched_holding_mints = [h.token_mint for h in holdings_db]
        logger.debug(f"[{current_user.wallet_address}] Holding mints fetched: {fetched_holding_mints}")
        holdings_data: List[HoldingItem] = []
        for holding in holdings_db:
            live_pnl = None
            if holding.current_price_sol is not None and holding.average_buy_price_sol > 1e-12: # Avoid division by zero
                live_pnl = ((holding.current_price_sol / holding.average_buy_price_sol) - 1) * 100.0

            # Use Pydantic's model_validate or from_orm
            holding_item = HoldingItem.model_validate(holding) # Pydantic v2+
            # holding_item = HoldingItem.from_orm(holding) # Pydantic v1
            holding_item.live_pnl_percent = live_pnl # Assign calculated PNL
            holdings_data.append(holding_item)

        # Fetch Recent Trades (use the trade_crud dictionary)
        trades_db: List[Trade] = await trade_crud['get_recent_trades_by_user'](db, current_user.wallet_address, limit=50) # Use configured limit
        # Use Pydantic's model_validate or from_orm for list items
        trades_data: List[TradeItem] = [TradeItem.model_validate(trade) for trade in trades_db] # Pydantic v2+
        # trades_data: List[TradeItem] = [TradeItem.from_orm(trade) for trade in trades_db] # Pydantic v1

        # Return using the finally validated Pydantic model
        return DashboardDataResponse(
            bot_state=bot_state_data_final, # Use the validated model
            holdings=holdings_data,
            recent_trades=trades_data
        )

    except Exception as e:
        logger.exception(f"Error fetching dashboard data for {current_user.wallet_address}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch dashboard data")

@router.get("/prices/live", response_model=Dict[str, Optional[float]],
            dependencies=[Depends(CustomRateLimiter(times=120, seconds=60))])  # 120 requests per minute (2 per second)
async def get_live_prices(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Get the latest known prices for the user's current holdings.
    """
    logger.debug(f"Fetching live prices for user: {current_user.wallet_address}")
    live_prices: Dict[str, Optional[float]] = {}
    try:
        holdings: List[Holding] = await holding_crud.get_holdings_by_user(db, current_user.wallet_address)

        if not holdings:
            logger.info(f"No holdings found for user {current_user.wallet_address} to fetch live prices.")
            return {} # Return empty dict if no holdings

        for holding in holdings:
            live_prices[holding.token_mint] = holding.current_price_sol # Will be None if not updated yet

        logger.debug(f"Returning {len(live_prices)} live prices for user {current_user.wallet_address}")
        return live_prices

    except Exception as e:
        logger.exception(f"Error fetching live prices for {current_user.wallet_address}: {e}")
        # In case of error, return an empty dict or raise an HTTPException
        # Returning empty might be safer for the frontend
        # raise HTTPException(status_code=500, detail="Failed to fetch live prices")
        return {}

@router.post("/manual-cleanup", status_code=status.HTTP_200_OK)
async def manual_cleanup(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger cleanup of stale holdings (for debugging/admin use)
    """
    # Check if user is an admin
    if current_user.is_admin != True:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin users can perform this action"
        )
    
    # Find all holdings that meet the cleanup criteria
    from app.periodic_tasks import MAX_STAGNANT_CHECKS
    
    query = select(Holding.id).where(
        and_(
            Holding.monitoring_active == True,
            Holding.stagnant_check_count >= MAX_STAGNANT_CHECKS
        )
    )
    
    result = await db.execute(query)
    stale_holding_ids = result.scalars().all()
    
    if not stale_holding_ids:
        return {"message": "No stale holdings found that need deactivation", "count": 0}
    
    # Prepare update statement to deactivate all stale holdings at once
    update_stmt = update(Holding).where(
        Holding.id.in_(stale_holding_ids)
    ).values(
        monitoring_active=False,
        updated_at=datetime.utcnow()
    )
    
    # Execute the update
    await db.execute(update_stmt)
    await db.commit()
    
    return {
        "message": f"Successfully deactivated monitoring for {len(stale_holding_ids)} stagnant holdings",
        "count": len(stale_holding_ids)
    }

@router.get("/stagnant-holdings", status_code=status.HTTP_200_OK)
async def get_stagnant_holdings(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
    min_stagnant_count: int = Query(0, description="Minimum stagnant check count to filter by")
):
    """
    Get information about stagnant holdings for debugging
    """
    # Check if user is an admin
    if current_user.is_admin != True:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin users can perform this action"
        )
    
    # Query holdings with significant stagnant check counts
    query = select(Holding).where(
        Holding.stagnant_check_count >= min_stagnant_count
    ).order_by(Holding.stagnant_check_count.desc())
    
    result = await db.execute(query)
    holdings = result.scalars().all()
    
    # Count holdings that should be marked inactive
    from app.periodic_tasks import MAX_STAGNANT_CHECKS
    should_be_inactive_count = sum(1 for h in holdings if h.stagnant_check_count >= MAX_STAGNANT_CHECKS and h.monitoring_active)
    
    # Format response
    holdings_data = []
    for h in holdings:
        holdings_data.append({
            "id": h.id,
            "token_mint": h.token_mint,
            "user_wallet": h.user_wallet,
            "monitoring_active": h.monitoring_active,
            "stagnant_check_count": h.stagnant_check_count,
            "last_successful_price_fetch": h.last_successful_price_fetch,
            "current_price_sol": h.current_price_sol,
            "last_acquired_at": h.last_acquired_at,
            "updated_at": h.updated_at
        })
    
    return {
        "holdings": holdings_data,
        "total_count": len(holdings),
        "pending_inactive_count": should_be_inactive_count,
        "max_stagnant_threshold": MAX_STAGNANT_CHECKS
    }

@router.get("/monitoring-status", response_model=Dict[str, bool],
            dependencies=[Depends(CustomRateLimiter(times=120, seconds=60))])
async def get_monitoring_status(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Get the monitoring_active status for user's holdings efficiently.
    Returns a mapping of token_mint → monitoring_active status.
    """
    logger.debug(f"Fetching monitoring status for user: {current_user.wallet_address}")
    monitoring_status: Dict[str, bool] = {}
    
    try:
        # Optimize query to only fetch the token_mint and monitoring_active columns
        query = select(Holding.token_mint, Holding.monitoring_active).where(
            Holding.user_wallet == current_user.wallet_address
        )
        result = await db.execute(query)
        status_rows = result.all()  # Returns list of tuples (token_mint, monitoring_active)

        if not status_rows:
            logger.info(f"No holdings found for user {current_user.wallet_address} to fetch monitoring status.")
            return {}  # Return empty dict if no holdings

        # Build the mapping
        for token_mint, is_active in status_rows:
            monitoring_status[token_mint] = is_active

        logger.debug(f"Returning monitoring status for {len(monitoring_status)} holdings for user {current_user.wallet_address}")
        return monitoring_status

    except Exception as e:
        logger.exception(f"Error fetching monitoring status for {current_user.wallet_address}: {e}")
        # Return empty dict on error for safer frontend handling
        return {} 