# holders.py

import os
import asyncio
import aiohttp
import pandas as pd
from aiolimiter import AsyncLimiter
import logging
import numpy as np
from solbot.config import SHYFT_RPC_STATE_ENDPOINT_URL

# Import the setup_holders_logger from logging_config
from config.logging_config import setup_holders_logger

# Initialize the holders logger
holders_logger = setup_holders_logger()

# Configuration
HOLDERS_LIMIT = 1

# Rate limiters
rpc_limiter = AsyncLimiter(25, 1)  # 25 RPC calls per second
api_limiter = AsyncLimiter(1, 1.2)  # 1 API call per second

# Retry Configuration
RETRY_ATTEMPTS = 2
INITIAL_DELAY = 1  # Initial delay in seconds
MAX_DELAY = 2      # Maximum delay in seconds

# Initialize caches and locks
holders_cache = {}
holders_fetch_futures = {}
holders_fetch_futures_lock = asyncio.Lock()


async def make_request(session, payload):
    headers = {"Content-Type": "application/json"}
    try:
        async with session.post(SHYFT_RPC_STATE_ENDPOINT_URL, json=payload, headers=headers, timeout=10) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 429:
                holders_logger.warning("Rate limited by RPC endpoint.")
                return None
            else:
                text = await response.text()
                holders_logger.error(f"RPC Request failed with status {response.status}: {text}")
                return None
    except Exception as e:
        holders_logger.error(f"RPC Request exception: {e}")
        return None


async def make_request_with_backoff(session, payload):
    attempt = 0
    delay = INITIAL_DELAY

    while attempt < RETRY_ATTEMPTS:
        async with rpc_limiter:
            response = await make_request(session, payload)
        if response:
            return response
        else:
            holders_logger.warning(f"Retrying in {delay} seconds... (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_DELAY)  # Exponential backoff capped at MAX_DELAY
            attempt += 1
    holders_logger.error("Max retries exceeded for RPC request.")
    return None


async def get_balance_sol(session, wallet_address):
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getBalance",
        "params": [wallet_address]
    }

    attempt = 0
    delay = INITIAL_DELAY

    while attempt < RETRY_ATTEMPTS:
        response = await make_request_with_backoff(session, payload)
        if response and 'result' in response and 'value' in response['result']:
            lamports = response['result']['value']
            if isinstance(lamports, int) and lamports >= 0:
                return lamports / 1e9
            else:
                holders_logger.error(f"Invalid lamports value: {lamports}")
        else:
            holders_logger.error(f"Could not retrieve SOL balance for wallet {wallet_address}.")

        attempt += 1
        if attempt < RETRY_ATTEMPTS:
            holders_logger.warning(f"Retrying get_balance_sol for {wallet_address} in {delay} seconds... (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_DELAY)
        else:
            holders_logger.error(f"Failed to retrieve valid SOL balance for wallet {wallet_address} after {RETRY_ATTEMPTS} attempts.")

    return None


async def get_token_largest_accounts(session, token_mint_address, limit=20):
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenLargestAccounts",
        "params": [
            token_mint_address,
            {"commitment": "finalized"}
        ]
    }

    attempt = 0
    delay = INITIAL_DELAY

    while attempt < RETRY_ATTEMPTS:
        response = await make_request_with_backoff(session, payload)
        if response and 'result' in response and 'value' in response['result']:
            largest_accounts = response['result']['value']
            if isinstance(largest_accounts, list) and all('address' in acc and 'amount' in acc for acc in largest_accounts):
                return largest_accounts[:limit] if len(largest_accounts) > limit else largest_accounts
            else:
                holders_logger.error(f"Invalid largest_accounts structure for {token_mint_address}.")
        else:
            holders_logger.error(f"Failed to fetch largest token accounts for {token_mint_address}.")

        attempt += 1
        if attempt < RETRY_ATTEMPTS:
            holders_logger.warning(f"Retrying get_token_largest_accounts for {token_mint_address} in {delay} seconds... (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_DELAY)
        else:
            holders_logger.error(f"Failed to retrieve largest token accounts for {token_mint_address} after {RETRY_ATTEMPTS} attempts.")

    return []


async def get_account_owner(session, token_account_address):
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getAccountInfo",
        "params": [
            token_account_address,
            {"encoding": "jsonParsed"}
        ]
    }

    attempt = 0
    delay = INITIAL_DELAY

    while attempt < RETRY_ATTEMPTS:
        response = await make_request_with_backoff(session, payload)
        if response and 'result' in response and response['result']['value']:
            try:
                owner = response['result']['value']['data']['parsed']['info']['owner']
                if isinstance(owner, str) and owner:
                    return owner
                else:
                    holders_logger.error(f"Invalid owner value: {owner}")
            except KeyError:
                holders_logger.error(f"Malformed response when fetching owner for {token_account_address}.")
        else:
            holders_logger.error(f"Failed to fetch account info for {token_account_address}.")

        attempt += 1
        if attempt < RETRY_ATTEMPTS:
            holders_logger.warning(f"Retrying get_account_owner for {token_account_address} in {delay} seconds... (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_DELAY)
        else:
            holders_logger.error(f"Failed to retrieve owner for token account {token_account_address} after {RETRY_ATTEMPTS} attempts.")

    return None


async def get_token_info(session, token_mint_address):
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenSupply",
        "params": [
            token_mint_address
        ]
    }

    attempt = 0
    delay = INITIAL_DELAY

    while attempt < RETRY_ATTEMPTS:
        response = await make_request_with_backoff(session, payload)
        if response and 'result' in response and 'value' in response['result']:
            value = response['result']['value']
            decimals = value.get('decimals')
            total_supply = value.get('uiAmount')
            if isinstance(decimals, int) and decimals >= 0 and (isinstance(total_supply, float) or isinstance(total_supply, int)):
                return int(decimals), float(total_supply)
            else:
                holders_logger.error(f"Invalid token info values: decimals={decimals}, uiAmount={total_supply}")
        else:
            holders_logger.warning("Could not retrieve token info. Defaulting to 6 decimals and unknown total supply.")

        attempt += 1
        if attempt < RETRY_ATTEMPTS:
            holders_logger.warning(f"Retrying get_token_info for {token_mint_address} in {delay} seconds... (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_DELAY)
        else:
            holders_logger.error(f"Failed to retrieve valid token info for {token_mint_address} after {RETRY_ATTEMPTS} attempts.")

    # Default values if all attempts fail
    return 6, None


async def process_top_holders(session, token_accounts, decimals, total_supply):
    holders_data = []

    if not token_accounts:
        holders_logger.warning("No token accounts to process.")
        return holders_data

    holders_logger.debug(f"Processing top {len(token_accounts)} holders:")

    for idx, account in enumerate(token_accounts, start=1):
        holders_logger.debug("-------------------")
        holders_logger.debug(f"Holder #{idx}")
        token_account_address = account.get('address')
        raw_amount = account.get('amount', '0')
        try:
            token_amount = float(raw_amount) / (10 ** decimals)
        except (ValueError, TypeError):
            token_amount = 0.0
            holders_logger.error(f"Invalid amount format for account {token_account_address}: {raw_amount}")

        if not token_account_address:
            holders_logger.error("Invalid token account data.")
            holders_data.append({
                'token_balance': 0.0,
                'percentage_of_total_supply': None,
                'sol_balance': None
            })
            continue

        owner = await get_account_owner(session, token_account_address)
        if not owner:
            holders_logger.error(f"Could not retrieve owner for token account {token_account_address}.")
            holders_data.append({
                'token_balance': token_amount,
                'percentage_of_total_supply': None,
                'sol_balance': None
            })
            continue

        try:
            holders_logger.debug(f"Owner: {owner}")
            holders_logger.debug(f"Token Balance: {token_amount}")

            if total_supply and total_supply > 0:
                percentage = (token_amount / total_supply) * 100
                holders_logger.debug(f"Percentage of Total Supply: {percentage:.6f}%")
            else:
                percentage = None
                holders_logger.debug("Percentage of Total Supply: Unknown")

            sol_balance = await get_balance_sol(session, owner)
            if sol_balance is not None:
                holders_logger.debug(f"SOL Balance: {sol_balance} SOL")
            else:
                holders_logger.error("SOL Balance: Unable to fetch")

            holders_data.append({
                'token_balance': token_amount,
                'percentage_of_total_supply': percentage,
                'sol_balance': sol_balance
            })
        except Exception as e:
            holders_logger.error(f"Error fetching balances for owner {owner}: {e}")
            holders_data.append({
                'token_balance': token_amount,
                'percentage_of_total_supply': None,
                'sol_balance': None
            })

    return holders_data


async def fetch_holders_data(mint_address: str) -> list:
    """
    Fetch and process holders data for a given mint address.
    Ensures that data is fetched and processed only once per mint address.
    Subsequent calls for the same mint address will return cached data.
    """
    async with holders_fetch_futures_lock:
        if mint_address in holders_cache:
            holders_logger.debug(f"Returning cached holders data for {mint_address}")
            return holders_cache[mint_address]
        if mint_address in holders_fetch_futures:
            holders_logger.debug(f"Awaiting ongoing fetch for {mint_address}")
            future = holders_fetch_futures[mint_address]
        else:
            # Start a new fetch
            holders_logger.debug(f"Starting new fetch for {mint_address}")
            future = asyncio.Future()
            holders_fetch_futures[mint_address] = future
            asyncio.create_task(_fetch_and_cache_holders_data(mint_address, future))

    # Wait for the fetch to complete
    try:
        result = await future
        return result
    except Exception as e:
        holders_logger.error(f"Error fetching holders data for {mint_address}: {e}")
        raise


async def _fetch_and_cache_holders_data(mint_address: str, future: asyncio.Future):
    """
    Internal helper function to fetch and cache holders data.
    """
    try:
        async with aiohttp.ClientSession() as session:
            decimals, total_supply = await get_token_info(session, mint_address)
            holders_logger.debug(f"Token Decimals: {decimals}")
            if total_supply is not None:
                holders_logger.debug(f"Total Supply: {total_supply}")
            else:
                holders_logger.debug("Total Supply: Unknown")

            holders_logger.debug("Fetching largest accounts...")
            top_token_accounts = await get_token_largest_accounts(session, mint_address, limit=HOLDERS_LIMIT)
            if not top_token_accounts:
                holders_data = [None] * HOLDERS_LIMIT
            else:
                holders_data = await process_top_holders(session, top_token_accounts, decimals, total_supply)

            final_holders_data = []
            for i in range(1, HOLDERS_LIMIT + 1):
                if i <= len(holders_data):
                    holder_info = holders_data[i - 1]
                    if holder_info:
                        final_holders_data.extend([
                            holder_info.get('token_balance', np.nan),
                            holder_info.get('percentage_of_total_supply', np.nan),
                            holder_info.get('sol_balance', np.nan)
                        ])
                        holders_logger.debug(f"Holder {i} data for {mint_address}: {holder_info}")
                    else:
                        final_holders_data.extend([np.nan, np.nan, np.nan])
                        holders_logger.debug(f"Holder {i} data for {mint_address}: Missing (NaN)")
                else:
                    final_holders_data.extend([np.nan, np.nan, np.nan])
                    holders_logger.debug(f"Holder {i} data for {mint_address}: Not available (NaN)")

            holders_cache[mint_address] = final_holders_data
            future.set_result(final_holders_data)
            holders_logger.debug(f"Successfully fetched and cached holders data for {mint_address}")

    except Exception as e:
        holders_logger.error(f"Exception while fetching holders data for {mint_address}: {e}")
        future.set_exception(e)
    finally:
        async with holders_fetch_futures_lock:
            if mint_address in holders_fetch_futures:
                del holders_fetch_futures[mint_address]
