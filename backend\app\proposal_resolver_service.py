import asyncio
import logging
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from fastapi import FastAPI # For type hinting app

from app.database import async_session_factory
from app.db_models import LiveModelProposals
from solbot.roi_calculator import calculate_actual_max_roi 
# Import config for PROPOSAL_RESOLUTION_DELAY_SECONDS
from solbot.config import PROPOSAL_RESOLUTION_DELAY_SECONDS 

logger = logging.getLogger(__name__)

async def resolve_pending_proposals_task(app: FastAPI):
    """
    Periodically checks for unresolved proposals, calculates their actual_max_roi,
    and updates them in the database.
    """
    if not hasattr(app.state, 'http_session') or app.state.http_session is None:
        logger.error("ProposalResolverService: aiohttp ClientSession not found in app state. Cannot run.")
        return

    logger.info("ProposalResolverService: Starting periodic check for unresolved proposals.")
    
    # Initial delay to allow other services to start, can be short
    await asyncio.sleep(30) 

    while True:
        try:
            async with async_session_factory() as db:
                # Calculate the cutoff time for proposals to be resolved
                cutoff_time_utc = datetime.now(timezone.utc) - timedelta(seconds=PROPOSAL_RESOLUTION_DELAY_SECONDS)
                
                # Convert to a naive datetime for PostgreSQL TIMESTAMP WITHOUT TIME ZONE column
                cutoff_time_naive = cutoff_time_utc.replace(tzinfo=None)
                
                stmt = select(LiveModelProposals).where(
                    LiveModelProposals.is_resolved == False,
                    LiveModelProposals.proposal_timestamp <= cutoff_time_naive
                ).order_by(LiveModelProposals.proposal_timestamp).limit(10) # Process in smaller batches

                result = await db.execute(stmt)
                proposals_to_resolve = result.scalars().all()

                if not proposals_to_resolve:
                    logger.debug("ProposalResolverService: No proposals currently due for resolution.")
                else:
                    logger.info(f"ProposalResolverService: Found {len(proposals_to_resolve)} proposals to resolve.")
                    for proposal in proposals_to_resolve:
                        logger.info(f"ProposalResolverService: Resolving proposal ID {proposal.id} for mint {proposal.mint_address}")
                        
                        # Ensure proposal_timestamp is timezone-aware (UTC) if it's naive, before converting to Unix timestamp
                        proposal_ts_db = proposal.proposal_timestamp
                        if proposal_ts_db.tzinfo is None:
                            proposal_ts_utc = proposal_ts_db.replace(tzinfo=timezone.utc)
                        else:
                            proposal_ts_utc = proposal_ts_db.astimezone(timezone.utc)
                        
                        proposal_timestamp_unix = proposal_ts_utc.timestamp()

                        actual_roi = await calculate_actual_max_roi(
                            app, # Pass the app instance
                            proposal.mint_address
                            # Note: Time window parameters removed - now uses pure price sequence analysis
                        )

                        # Use naive datetime for database consistency since PostgreSQL stores TIMESTAMP WITHOUT TIME ZONE
                        # First get current UTC time, then strip the timezone to maintain consistency
                        current_time_utc = datetime.now(timezone.utc)
                        naive_resolution_time = current_time_utc.replace(tzinfo=None)
                        
                        update_payload = {
                            "is_resolved": True,
                            "resolution_timestamp": naive_resolution_time
                        }

                        if actual_roi is not None:
                            update_payload["actual_max_roi"] = actual_roi
                            logger.info(f"ProposalResolverService: Resolved proposal ID {proposal.id} for {proposal.mint_address}. Actual Max ROI (profit factor): {actual_roi:.4f}")
                        else:
                            # Handle failed ROI calculation: mark as resolved with a specific error ROI value or keep actual_max_roi as NULL
                            update_payload["actual_max_roi"] = -2.0 # Or None, depending on how you want to treat failures
                            logger.warning(f"ProposalResolverService: Could not determine ROI for proposal ID {proposal.id} ({proposal.mint_address}). Marked as resolved with error ROI (-2.0).")
                        
                        # Update the specific proposal
                        update_stmt = update(LiveModelProposals).where(LiveModelProposals.id == proposal.id).values(**update_payload)
                        await db.execute(update_stmt)
                        await db.commit()
                        
                        await asyncio.sleep(2) # Small delay between processing each proposal in a batch

            await asyncio.sleep(60 * 1)  # Check every 1 minute for new proposals to resolve
        except asyncio.CancelledError:
            logger.info("ProposalResolverService task cancelled.")
            break
        except Exception as e:
            logger.error(f"ProposalResolverService: Error in main loop: {e}", exc_info=True)
            await asyncio.sleep(60 * 2) # Wait longer after an error before retrying
