from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import logging
from datetime import datetime

from app.db_models import Trade, Holding, TradeTypeEnum
from app.crud.holding_crud import holding_crud

logger = logging.getLogger(__name__)

async def create_trade_record(
    db: AsyncSession, 
    user_wallet: str, 
    trade_type: TradeTypeEnum, 
    token_mint: str, 
    amount_token: float, 
    price_sol: float, 
    total_sol: float, 
    tx_signature: str | None = None,
    fee_amount_sol: float | None = None,
    fee_destination_wallet: str | None = None
) -> Trade:
    pnl_sol = None
    
    if trade_type == TradeTypeEnum.SELL:
        logger.debug(f"Calculating PNL for SELL trade: {user_wallet}/{token_mint}")
        holding = await holding_crud.get_holding(db, user_wallet, token_mint)
        if holding:
            avg_buy_price = holding.average_buy_price_sol
            pnl_sol = (price_sol - avg_buy_price) * amount_token
            logger.debug(f"PNL Calculated: Price={price_sol}, AvgBuy={avg_buy_price}, Amount={amount_token}, PNL={pnl_sol}")
        else:
            logger.warning(f"Could not find holding for {token_mint} to calculate PNL for sell trade.")
    
    new_trade = Trade(
        user_wallet=user_wallet, 
        trade_type=trade_type, 
        token_mint=token_mint, 
        amount_token=amount_token, 
        price_sol=price_sol, 
        total_sol=total_sol, 
        pnl_sol=pnl_sol, 
        tx_signature=tx_signature,
        fee_amount_sol=fee_amount_sol,
        fee_destination_wallet=fee_destination_wallet
    )
    
    db.add(new_trade)
    await db.commit()
    await db.refresh(new_trade)
    
    return new_trade

async def get_recent_trades_by_user(
    db: AsyncSession, 
    user_wallet: str, 
    limit: int = 50
) -> list[Trade]:
    query = select(Trade).where(Trade.user_wallet == user_wallet).order_by(Trade.timestamp.desc()).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()

# Create a singleton instance for reuse
trade_crud = {
    "create_trade_record": create_trade_record,
    "get_recent_trades_by_user": get_recent_trades_by_user
} 