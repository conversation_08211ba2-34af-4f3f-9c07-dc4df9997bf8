from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import re

from app.database import get_db_session
from app.crud.config_crud import config_crud
from app.schemas.config_schemas import ConfigurationCreate, ConfigurationResponse
from app.db_models import User
from app.dependencies import get_current_user
from solbot.config import TREASURY_WALLET_ADDRESS

# Get the logger
logger = logging.getLogger(__name__)

# Create router for configuration endpoints
router = APIRouter(prefix="/api/config", tags=["config"])


@router.get("/", response_model=ConfigurationResponse)
async def get_config(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Get configuration for a user.
    
    Args:
        db: Database session dependency
        current_user: Current authenticated user
        
    Returns:
        ConfigurationResponse with the user's configuration
    """
    logger.debug(f"Getting configuration for user: {current_user.wallet_address}")
    
    try:
        # Try to get existing configuration first
        config = await config_crud.get_configuration_by_user(db, current_user.wallet_address)
        
        if config:
            logger.debug(f"Configuration retrieved successfully for user: {current_user.wallet_address}")
            return config
        else:
            # If no config exists, create one with default (None for these specific fields, others will take DB defaults)
            logger.debug(f"No existing configuration found for user: {current_user.wallet_address}. Creating new one.")
            config = await config_crud.update_or_create_configuration(
                db, current_user.wallet_address, {
                    # These are fields that the frontend expects to be present, 
                    # even if null initially. Other fields in Configuration model
                    # will use their default values specified in db_models.py or schemas.
                    "max_buy_sol": None,
                    "tp_percent": None, 
                    "sl_percent": None,
                    # It's important to ensure other fields also get their intended defaults
                    # If update_or_create_configuration doesn't handle this well when config_data is sparse,
                    # this initial creation payload might need to be more complete.
                    # However, given the current crud, this should be okay as it creates a new Configuration.
                }
            )
            logger.debug(f"New configuration created for user: {current_user.wallet_address}")
            return config
            
    except Exception as e:
        # Log the full exception and traceback
        logger.exception(f"Error getting configuration for user {current_user.wallet_address}: {str(e)}")
        
        # Create a more specific error message based on the type of exception
        error_message = str(e)
        status_code = 500
        
        # Check for specific error types for more helpful messages
        if "connection" in error_message.lower():
            error_message = f"Database connection error: {error_message}"
        
        raise HTTPException(
            status_code=status_code,
            detail=f"Error getting configuration: {error_message}"
        )


@router.post("/", response_model=ConfigurationResponse)
async def update_config(
    config_in: ConfigurationCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user)
):
    """
    Update or create configuration for a user.
    
    Args:
        config_in: The configuration data to update
        db: Database session dependency
        current_user: Current authenticated user
        
    Returns:
        ConfigurationResponse with the updated configuration
    """
    logger.debug(f"Updating configuration for user: {current_user.wallet_address}")
    
    try:
        # Convert Pydantic model to dict for the CRUD function
        config_data = config_in.dict(exclude_unset=True)
        # Use config_data only for Pydantic v1
        # config_data = config_in.model_dump(exclude_unset=True)  # For Pydantic v2+
        
        updated_config = await config_crud.update_or_create_configuration(
            db, current_user.wallet_address, config_data
        )
        
        logger.debug(f"Configuration updated successfully for user: {current_user.wallet_address}")
        return updated_config
    except Exception as e:
        # Log the full exception and traceback
        logger.exception(f"Error updating configuration for user {current_user.wallet_address}: {str(e)}")
        
        # Create a more specific error message based on the type of exception
        error_message = str(e)
        status_code = 500
        
        # Check for specific error types for more helpful messages
        if "connection" in error_message.lower():
            error_message = f"Database connection error: {error_message}"
        elif "validation" in error_message.lower():
            error_message = f"Validation error: {error_message}"
            status_code = 422
        
        raise HTTPException(
            status_code=status_code,
            detail=f"Error updating configuration: {error_message}"
        )


@router.get("/app-info")
async def get_app_info():
    """
    Get application information including treasury wallet address.
    
    Returns:
        A JSON object with application information
    """
    logger.debug("Getting application information")
    
    try:
        # Validate the treasury wallet address format
        if not TREASURY_WALLET_ADDRESS:
            logger.error("Treasury wallet address is empty or not set")
            return {"treasuryWalletAddress": None, "error": "Treasury wallet address not configured"}
        
        # Validate base58 format - Solana addresses should only contain base58 characters
        base58_pattern = re.compile(r'^[1-9A-HJ-NP-Za-km-z]+$')
        if not base58_pattern.match(TREASURY_WALLET_ADDRESS):
            logger.error(f"Invalid treasury wallet address format: {TREASURY_WALLET_ADDRESS}")
            return {"treasuryWalletAddress": None, "error": "Treasury wallet address is not in valid base58 format"}
            
        # Validate length - Solana addresses are 32-44 characters
        if not (32 <= len(TREASURY_WALLET_ADDRESS) <= 44):
            logger.error(f"Treasury wallet address has invalid length: {len(TREASURY_WALLET_ADDRESS)}")
            return {"treasuryWalletAddress": None, "error": "Treasury wallet address has invalid length"}
            
        return {
            "treasuryWalletAddress": TREASURY_WALLET_ADDRESS
        }
    except Exception as e:
        logger.exception(f"Error getting application information: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting application information: {str(e)}"
        ) 