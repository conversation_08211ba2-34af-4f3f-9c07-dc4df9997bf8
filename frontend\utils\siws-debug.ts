/**
 * Helper functions for debugging SIWS (Sign In With Solana) issues
 */

/**
 * Analyzes a SIWS message for proper format
 * @param message The SIWS message to analyze
 * @returns An object with validation results and debug information
 */
export function analyzeSiwsMessage(message: string): { 
  valid: boolean; 
  details: Record<string, any>;
  formattedMessage?: string;
} {
  console.log("Analyzing SIWS message:", message);
  
  // Check for expected structure in message
  const hasHeader = message.includes("wants you to sign in with your Solana account");
  const hasAddress = /[1-9A-HJ-NP-Za-km-z]{32,44}/.test(message); // Basic Solana address check
  const hasNonce = message.includes("Nonce:");
  const hasURI = message.includes("URI:");
  const hasVersion = message.includes("Version:");
  const hasChainId = message.includes("Chain ID:");
  const hasIssuedAt = message.includes("Issued At:");
  
  // Parse key components expected by backend verification
  const addressMatch = message.match(/\n([1-9A-HJ-NP-Za-km-z]{32,44})\n/);
  const nonceMatch = message.match(/Nonce: ([a-zA-Z0-9-_]+)/);
  const domainMatch = message.match(/^([^\s]+) wants you to sign in/);
  
  // Expected regex pattern similar to backend's parser
  const pattern = new RegExp(
    "^(?<domain>[^\\s]+) wants you to sign in with your Solana account:\\n" +
    "(?<address>[a-zA-Z0-9]{32,44})\\n" +
    "(\\n(?<statement>[^\\n]+)\\n)?" +
    "(\\n(?:" +
    "(?:URI: (?<uri>[^\\n]+)\\n)?" +
    "(?:Version: (?<version>[^\\n]+)\\n)?" +
    "(?:Chain ID: (?<chain_id>[^\\n]+)\\n)?" +
    "(?:Nonce: (?<nonce>[^\\n]+)\\n)?" +
    "(?:Issued At: (?<issued_at>[^\\n]+)\\n)?" +
    "(?:Expiration Time: (?<expiration_time>[^\\n]+)\\n)?" +
    "(?:Not Before: (?<not_before>[^\\n]+)\\n)?" +
    "(?:Request ID: (?<request_id>[^\\n]+)\\n)?" +
    "(?:Resources:\\n(?<resources>(?:- [^\\n]+\\n?)+))?" +
    ")?)?$", "m"
  );
  
  const match = pattern.exec(message);
  const regexValid = !!match;
  
  // Create debug details
  const details = {
    length: message.length,
    hasHeader,
    hasAddress,
    hasNonce,
    hasURI,
    hasVersion,
    hasChainId,
    hasIssuedAt,
    addressFound: addressMatch ? addressMatch[1] : null,
    nonceFound: nonceMatch ? nonceMatch[1] : null,
    domainFound: domainMatch ? domainMatch[1] : null,
    regexValid,
    regexGroups: match ? match.groups : null,
    lines: message.split('\n').map((line, i) => `${i+1}: ${line}`)
  };
  
  // Basic validation result - require more fields to match backend expectations
  const valid = hasHeader && hasAddress && hasNonce && hasURI && hasVersion && 
                hasChainId && hasIssuedAt && regexValid;
  
  // Format a correct message if we have the key components
  let formattedMessage = undefined;
  if (details.addressFound && details.nonceFound && details.domainFound && !valid) {
    const domain = details.domainFound || window.location.host;
    formattedMessage = 
`${domain} wants you to sign in with your Solana account:
${details.addressFound}

Sign in to SolBot Windsurf to manage your bot.

URI: ${window.location.protocol}//${domain}
Version: 1
Chain ID: solana:mainnet
Nonce: ${details.nonceFound}
Issued At: ${new Date().toISOString()}`;
  }
  
  return { valid, details, formattedMessage };
}

/**
 * Logs detailed diagnostic information about a SIWS verification attempt
 */
export function logSiwsVerificationDetails(
  input: any,
  output: any,
  message: string,
  signature: Uint8Array,
  walletAddress: string
): void {
  console.group("SIWS Verification Diagnostic Information");
  
  // 1. Analyze the message
  const messageAnalysis = analyzeSiwsMessage(message);
  console.log("Message analysis:", messageAnalysis);
  
  // 2. Log signature details
  console.log("Signature:", {
    byteLength: signature.byteLength,
    first8Bytes: Array.from(signature.slice(0, 8)),
    last8Bytes: Array.from(signature.slice(-8))
  });
  
  // 3. Check wallet address format
  const validWalletFormat = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(walletAddress);
  console.log("Wallet address check:", {
    address: walletAddress,
    validFormat: validWalletFormat,
    length: walletAddress.length
  });
  
  // 4. Check if the address in the message matches the wallet address
  const addressInMessage = messageAnalysis.details.addressFound;
  console.log("Address comparison:", {
    addressFromWallet: walletAddress,
    addressInMessage,
    matches: addressInMessage === walletAddress
  });
  
  // 5. Suggest fixes if issues detected
  const issues = [];
  if (!messageAnalysis.valid) {
    issues.push("Message format does not match expected SIWS format");
    
    // Add specific missing fields
    const details = messageAnalysis.details;
    if (!details.hasURI) issues.push("Missing URI field");
    if (!details.hasVersion) issues.push("Missing Version field");
    if (!details.hasChainId) issues.push("Missing Chain ID field");
    if (!details.hasIssuedAt) issues.push("Missing Issued At field");
    
    if (messageAnalysis.formattedMessage) {
      console.log("Suggested corrected message format:", messageAnalysis.formattedMessage);
    }
  }
  
  if (signature.byteLength !== 64) {
    issues.push(`Signature length is ${signature.byteLength} bytes, but should be exactly 64 bytes`);
  }
  
  if (!validWalletFormat) {
    issues.push("Wallet address format appears invalid");
  }
  
  if (addressInMessage !== walletAddress) {
    issues.push("Address in message doesn't match wallet address");
  }
  
  console.log("Detected issues:", issues.length > 0 ? issues : "None");
  console.groupEnd();
} 