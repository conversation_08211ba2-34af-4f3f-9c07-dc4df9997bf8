# Admin Wallet Setup Guide

This guide will help you set up admin privileges for your wallet to access the manual sniper bot resolution trigger.

## How the Risk Ladder Pools Work

### **Left Table: Sniper Bot Simulation Pool**
- **Strategy**: Simulates buying every new token as the 10th transaction
- **Data Source**: All tokens from `detected_mint_addresses.csv`
- **Entry Point**: 10th transaction price (very early entry)
- **Pool Size**: Last 300 resolved tokens
- **Update Method**: **Batch processing** (cost-optimized)
  - Processes only 300 most recent unresolved tokens
  - Runs automatically at 2 AM daily
  - Can be triggered manually via admin button
  - 95% API cost reduction vs continuous processing
- **Grace Period**: 3 hours after token launch before ROI calculation
- **Pool Refresh**: Daily or on manual trigger

### **Right Table: Live Signals Pool**
- **Strategy**: Only tokens that pass AI analysis and filtering
- **Data Source**: AI-generated BUY signals from predictive model
- **Entry Point**: 111th transaction price (after AI analysis)
- **Pool Size**: Last 300 resolved signals
- **Update Method**: **Continuous processing**
  - Resolves signals automatically after grace period
  - Updates every 30 seconds
  - Real-time pool updates as signals resolve
- **Grace Period**: 3 hours after signal generation before ROI calculation
- **Pool Refresh**: Real-time (every 30 seconds)

### **Key Differences**
| Aspect | Sniper Bot (Left) | Live Signals (Right) |
|--------|------------------|---------------------|
| **Frequency** | ~4 tokens/minute | ~1 token/hour |
| **Processing** | Batch (daily/manual) | Continuous (real-time) |
| **API Cost** | Low (300 tokens/day) | Low (24 tokens/day) |
| **Pool Updates** | Daily or manual | Every 30 seconds |
| **Time Span** | ~75 minutes of launches | ~12.5 days of signals |
| **Entry Point** | 10th transaction | 111th transaction |

### **Why Batch Processing for Sniper Bot?**
- **Cost Optimization**: Reduces API calls from 46M/day to 2.4M/day (95% savings)
- **Same Data Quality**: Still maintains 300-token pool for comparison
- **Flexibility**: Manual trigger for immediate updates when needed
- **Efficiency**: Only processes tokens that will actually be in the pool

## Prerequisites

- Access to your PostgreSQL database
- Your treasury wallet address (from `.env` file)
- Database admin credentials

## Step 1: Find Your Treasury Wallet Address

1. **Open your `.env` file** in the backend directory
2. **Look for the line**: `TREASURY_WALLET_ADDRESS=your_wallet_address_here`
3. **Copy the wallet address** (it should look like: `ABC123def456...`)

Example:
```env
TREASURY_WALLET_ADDRESS=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
```

## Step 2: Add Admin Column to Database

### Option A: Using SQL Script (Recommended)

1. **Navigate to the backend directory**:
   ```bash
   cd backend
   ```

2. **Run the SQL script**:
   ```bash
   psql $DATABASE_URL -f sql/add_admin_column.sql
   ```

3. **Manually set your treasury wallet as admin**:
   ```sql
   psql $DATABASE_URL -c "UPDATE users SET is_admin = TRUE WHERE wallet_address = 'YOUR_TREASURY_WALLET_ADDRESS';"
   ```

### Option B: Manual SQL Commands

1. **Connect to your database**:
   ```bash
   psql $DATABASE_URL
   ```

2. **Add the admin column**:
   ```sql
   ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;
   ```

3. **Set your treasury wallet as admin** (replace with your actual address):
   ```sql
   UPDATE users SET is_admin = TRUE WHERE wallet_address = 'YOUR_TREASURY_WALLET_ADDRESS';
   ```

4. **Create index for performance**:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);
   ```

5. **Exit database**:
   ```sql
   \q
   ```

## Step 3: Verify Admin Setup

1. **Check if your wallet is set as admin**:
   ```sql
   psql $DATABASE_URL -c "SELECT wallet_address, is_admin FROM users WHERE is_admin = TRUE;"
   ```

   You should see output like:
   ```
   wallet_address                               | is_admin
   -------------------------------------------- | --------
   9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM | t
   ```

## Step 4: Test Admin Access

1. **Start your backend server**:
   ```bash
   cd backend
   python run.py
   ```

2. **Open your frontend** in a web browser

3. **Connect your treasury wallet** using the wallet connection button

4. **Sign the authentication message** when prompted

5. **Look for the admin button** below the risk ladder comparison header:
   - You should see a blue "Update Sniper Bot Data" button
   - If you don't see it, check the browser console for errors

## Step 5: Test Manual Trigger

1. **Click the "Update Sniper Bot Data" button**

2. **Wait for processing** (you'll see a spinner and "Processing..." text)

3. **Check the result**:
   - **Success**: Green message with processing stats
   - **Error**: Red message with error details

4. **Verify data update**: The risk ladder tables should refresh with new data

## Troubleshooting

### Admin Button Not Showing

1. **Check if you're authenticated**:
   - Look for your wallet address in the top-right corner
   - If not connected, connect your wallet and sign the message

2. **Verify admin status in database**:
   ```sql
   psql $DATABASE_URL -c "SELECT wallet_address, is_admin FROM users WHERE wallet_address = 'YOUR_WALLET_ADDRESS';"
   ```

3. **Check browser console** for JavaScript errors

### "Admin privileges required" Error

1. **Verify your wallet address matches** the one in the database exactly
2. **Check that `is_admin = TRUE`** in the database
3. **Try logging out and logging back in** to refresh your JWT token

### Processing Fails

1. **Check backend logs** for detailed error messages
2. **Verify Shyft API keys** are working
3. **Check database connectivity**
4. **Ensure CSV file exists** with token data

## Adding Additional Admin Users

To give admin privileges to other wallet addresses:

```sql
-- Replace with the actual wallet address
UPDATE users SET is_admin = TRUE WHERE wallet_address = 'ANOTHER_WALLET_ADDRESS';

-- Or insert if user doesn't exist yet
INSERT INTO users (wallet_address, is_admin) VALUES ('ANOTHER_WALLET_ADDRESS', TRUE)
ON CONFLICT (wallet_address) DO UPDATE SET is_admin = TRUE;
```

## Security Notes

- **Admin privileges are powerful** - only give them to trusted wallet addresses
- **Admin actions are logged** with the triggering wallet address
- **Rate limiting applies** - don't spam the trigger button
- **Only one batch can run at a time** - concurrent triggers are prevented

## API Endpoint (Alternative)

If you prefer using the API directly:

```bash
# Get your JWT token from browser localStorage or login
curl -X POST "http://localhost:8000/api/risk-ladder/admin/trigger-sniper-bot-resolution" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Pool Update Schedules

### **Automatic Updates**
- **Sniper Bot Pool**: Updates daily at 2 AM (configurable via `SNIPER_BOT_DAILY_RESOLUTION_HOUR`)
- **Live Signals Pool**: Updates continuously every 30 seconds
- **Stats Calculation**: Both pools recalculate statistics every 30 seconds
- **Frontend Refresh**: Tables refresh every 5 minutes automatically

### **Manual Updates**
- **Sniper Bot Pool**: Use admin button for immediate updates
- **Live Signals Pool**: No manual trigger needed (always current)

### **What Happens During Manual Trigger**
1. **Finds 300 most recent unresolved tokens** that passed 3-hour grace period
2. **Calculates ROI** for each token using 10th transaction as entry price
3. **Updates database** with resolution results
4. **Recalculates statistics** from new pool of 300 resolved tokens
5. **Updates JSON file** for API consumption
6. **Frontend refreshes** automatically after 2 seconds

## Support

If you encounter issues:

1. **Check backend logs** for detailed error messages
2. **Verify database connection** and admin status
3. **Test with a simple database query** to ensure connectivity
4. **Check that all services are running** (CSV monitor, stats service, etc.)
5. **Monitor API usage** to ensure Shyft credits aren't exhausted

---

**That's it!** Your admin wallet should now be able to manually trigger sniper bot data updates using the simple frontend button.
