aiofiles==23.2.1
aiolimiter==1.1.0
aiosignal==1.3.1
aiosqlite==0.19.0
alembic==1.13.1
annotated-types==0.6.0
anyio==4.3.0
asyncpg>=0.29.0
attrs==23.2.0
base58==2.1.1
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
construct>=2.10.68
construct-typing==0.5.6
dnspython==2.6.1
email_validator==2.1.1
fastapi==0.111.0
fastapi-cli==0.0.2
frozenlist==1.4.1
h11==0.14.0
#h2o==********
httpcore==1.0.5
httptools>=0.6.1
httpx==0.27.0
idna==3.7
Jinja2==3.1.4
joblib==1.4.2
jsonalias==0.1.1
markdown-it-py==3.0.0
MarkupSafe>=2.1.5
mdurl==0.1.2
multidict==6.0.5
numpy>=1.26.0
--find-links https://pypi.org/simple/orjson/
orjson
packaging==24.2
pandas>=2.2.3
propcache==0.2.0
psutil==5.9.8
psycopg>=3.0.0
pyaes==1.6.1
pyasn1==0.6.0
pydantic>=2.0.0
pydantic-core>=2.0.0
Pygments==2.18.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.9
pytz==2024.2
# PyYAML needs compilation, use a pure Python version
pyyaml>=6.0
requests==2.31.0
rich==13.7.1
rsa==4.9
scikit-fuzzy==0.5.0
scikit-learn==1.6.1
scipy>=1.14.1
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
solana==0.34.3
solders==0.21.0
SQLAlchemy==2.0.30
starlette==0.37.2
telegram==0.0.1
Telethon==1.35.0
tenacity==9.0.0
threadpoolctl==3.5.0
typer==0.12.3
typing_extensions==4.11.0
tzdata==2024.1
# ujson requires Rust compilation
# ujson==5.9.0
urllib3==2.2.1
uvicorn==0.29.0
watchdog==5.0.3
# watchfiles requires Rust compilation
# watchfiles==0.21.0
websockets==11.0.3
yarl==1.9.4
python-jose[cryptography]==3.3.0
passlib==1.7.4
aiohttp
fakeredis
pynacl
xgboost

