// API route for app-info
import { NextResponse } from 'next/server';

export const GET = async () => {
  try {
    // Use the API base URL from environment or default to localhost:8000
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
    
    // Make request to backend API
    const response = await fetch(`${apiBaseUrl}/api/app-info`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }
    
    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching app info:', error);
    return NextResponse.json(
      { error: 'Failed to fetch app information' },
      { status: 500 }
    );
  }
};
