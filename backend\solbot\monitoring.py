import asyncio
import time
import logging
import psutil
import os
import gc
from app.states import MintState

logger = logging.getLogger(__name__)

# Track processing metrics
processing_metrics = {
    'detection_to_completion_times': [],  # Time from 100th price detection to completion start
    'completion_durations': [],           # Time taken to complete processing
    'total_processing_times': [],         # Total time from detection to completion finish
    'queue_sizes': {
        'subscription_queue': [],
        'signatures_queue': [],
        'completion_queue': [],
        'unsubscribe_queue': []
    },
    'mint_address_counts': {
        'active': [],
        'stopped': []
    },
    'memory_usage': []
}

async def monitor_accumulators(app):
    logger.info("Starting accumulator monitor")
    # Time threshold for cleaning up finalized accumulators (20 minutes = 1200 seconds)
    FINALIZED_CLEANUP_THRESHOLD = 1200
    
    # Start system metrics monitoring
    asyncio.create_task(monitor_system_metrics(app))
    
    while True:
        try:
            current_time = time.time()
            
            # First, get a list of all active mint addresses
            async with app.state.mint_states_lock:
                active_addresses = {addr for addr, state in app.state.mint_states.items() 
                                   if state == MintState.ACTIVE}
                pending_addresses = {addr for addr, state in app.state.mint_states.items()
                                    if state == MintState.PENDING}
                logger.debug(f"Monitoring {len(active_addresses)} active addresses and {len(pending_addresses)} pending addresses")
            
            # First check pending addresses
            for mint_address in pending_addresses:
                # Check how long it's been pending
                async with app.state.mint_states_lock:
                    pending_time = app.state.mint_activation_times.get(mint_address, current_time)
                    time_elapsed = current_time - pending_time
                    
                logger.debug(f"Pending address: {mint_address}, pending for {int(time_elapsed)}s")
                
                # If it's been pending for too long without becoming active, time it out
                # We don't need to implement this timeout here as it's already handled by check_subscription_timeouts
                # This is just extra monitoring
                if time_elapsed >= 65:  # Same as our subscription timeout
                    logger.debug(f"Address {mint_address} has been PENDING for {int(time_elapsed)}s without becoming ACTIVE")
                    
            # Check addresses with accumulators first
            async with app.state.lock:
                mint_addresses_with_accumulators = list(app.state.accumulators.keys())
            
            # Process addresses with accumulators
            for mint_address in mint_addresses_with_accumulators:
                async with app.state.mint_lock_manager.acquire(mint_address):
                    if mint_address not in app.state.accumulators:
                        continue
                    accumulator = app.state.accumulators[mint_address]
                    
                    # Check if this accumulator is finalized
                    if accumulator.get('finalized', False):
                        # If finalized, check if it's been finalized for longer than the threshold
                        finalized_time = accumulator.get('finalized_time', 0)
                        if finalized_time == 0:  # If finalized_time not set, set it now
                            accumulator['finalized_time'] = current_time
                            logger.debug(f"Set finalized_time for {mint_address}")
                        elif current_time - finalized_time > FINALIZED_CLEANUP_THRESHOLD:
                            # This accumulator has been finalized for longer than the threshold, clean it up
                            logger.info(f"Cleaning up finalized accumulator for {mint_address} after {int(current_time - finalized_time)}s")
                            del app.state.accumulators[mint_address]
                            logger.info(f"Removed finalized accumulator for {mint_address} from memory")
                        continue
                    
                    # Process non-finalized accumulators as before
                    transactions_collected = len(accumulator.get('transactions', []))
                    desired_transactions = 100
                    time_elapsed = current_time - accumulator.get('start_time', 0)
                    logger.debug(f"Monitor - Mint: {mint_address}, Txs: {transactions_collected}, Time: {int(time_elapsed)}s")
                    
                    # Add price counter metrics if available
                    async with app.state.price_counters_lock:
                        if hasattr(app.state, 'price_counters') and mint_address in app.state.price_counters:
                            prices_counted = app.state.price_counters[mint_address]
                            logger.debug(f"Mint: {mint_address}, Prices directly counted: {prices_counted}/100")
                    
                    if time_elapsed >= 65 and transactions_collected < 10:
                        logger.warning(f"Timeout 1 for {mint_address}: {transactions_collected} txs in {int(time_elapsed)}s")
                        await skip_mint_address(mint_address, app)
                        continue
                    if time_elapsed >= 180 and transactions_collected < 20:
                        logger.warning(f"Timeout 2 for {mint_address}: {transactions_collected} txs in {int(time_elapsed)}s")
                        await skip_mint_address(mint_address, app)
                        continue
                    if time_elapsed >= 450 and transactions_collected < 50:
                        logger.warning(f"Timeout 3 for {mint_address}: {transactions_collected} txs in {int(time_elapsed)}s")
                        await skip_mint_address(mint_address, app)
                        continue
                    if time_elapsed >= 600 and transactions_collected < 100:
                        logger.warning(f"Timeout 4 for {mint_address}: {transactions_collected} txs in {int(time_elapsed)}s")
                        await skip_mint_address(mint_address, app)
                        continue
            
            # Now check active addresses without accumulators
            for mint_address in active_addresses:
                async with app.state.lock:
                    has_accumulator = mint_address in app.state.accumulators
                
                if not has_accumulator:
                    # Check when it became active
                    async with app.state.mint_states_lock:
                        activation_time = app.state.mint_activation_times.get(mint_address, current_time)
                        time_elapsed = current_time - activation_time
                        
                    logger.debug(f"Active address without accumulator: {mint_address}, active for {int(time_elapsed)}s")
                    
                    # If address has been active for too long without an accumulator, clean it up
                    if time_elapsed >= 65:  # First timeout threshold
                        logger.warning(f"Timeout for {mint_address}: No accumulator created after {int(time_elapsed)}s")
                        await skip_mint_address(mint_address, app)
            
            # Add consistency check between different tracking structures
            await verify_tracking_consistency(app)
                
        except Exception as e:
            logger.error(f"Error in monitor: {e}", exc_info=True)
        from solbot.config import SYSTEM_MONITOR_INTERVAL_SECONDS
        await asyncio.sleep(SYSTEM_MONITOR_INTERVAL_SECONDS)  # Configurable monitoring frequency

async def monitor_system_metrics(app):
    """Monitor system metrics like queue sizes, memory usage, and processing times."""
    logger.info("Starting system metrics monitor")
    
    # Use configurable monitoring interval
    from solbot.config import METRICS_MONITOR_INTERVAL_SECONDS
    monitor_interval = METRICS_MONITOR_INTERVAL_SECONDS
    
    while True:
        try:
            # Capture queue sizes with debug verification
            try:
                subscription_queue_size = app.state.subscription_queue.qsize()
                logger.debug(f"Subscription queue size: {subscription_queue_size}")
            except Exception as e:
                logger.error(f"Error getting subscription queue size: {e}")
                subscription_queue_size = 0
                
            try:
                signatures_queue_size = app.state.signatures_queue.qsize()
                logger.debug(f"Signatures queue size: {signatures_queue_size}")
            except Exception as e:
                logger.error(f"Error getting signatures queue size: {e}")
                signatures_queue_size = 0
                
            try:
                completion_queue_size = app.state.completion_queue.qsize() if hasattr(app.state, 'completion_queue') else 0
                logger.debug(f"Completion queue size: {completion_queue_size}")
            except Exception as e:
                logger.error(f"Error getting completion queue size: {e}")
                completion_queue_size = 0
                
            try:
                unsubscribe_queue_size = app.state.unsubscribe_queue.qsize()
                logger.debug(f"Unsubscribe queue size: {unsubscribe_queue_size}")
            except Exception as e:
                logger.error(f"Error getting unsubscribe queue size: {e}")
                unsubscribe_queue_size = 0
            
            # Update metrics
            processing_metrics['queue_sizes']['subscription_queue'].append(subscription_queue_size)
            processing_metrics['queue_sizes']['signatures_queue'].append(signatures_queue_size)
            processing_metrics['queue_sizes']['completion_queue'].append(completion_queue_size)
            processing_metrics['queue_sizes']['unsubscribe_queue'].append(unsubscribe_queue_size)
            
            # Capture memory usage
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_usage_mb = memory_info.rss / (1024 * 1024)  # Convert to MB
            processing_metrics['memory_usage'].append(memory_usage_mb)
            
            # Count mint addresses by state
            async with app.state.mint_states_lock:
                active_count = sum(1 for state in app.state.mint_states.values() if state == MintState.ACTIVE)
                pending_count = sum(1 for state in app.state.mint_states.values() if state == MintState.PENDING)
                stopped_count = sum(1 for state in app.state.mint_states.values() if state == MintState.STOPPED)
                
            processing_metrics['mint_address_counts']['active'].append(active_count)
            processing_metrics['mint_address_counts']['stopped'].append(stopped_count)
            
            # Count prices for active mint addresses
            prices_by_address = {}
            async with app.state.price_counters_lock:
                for mint_address, count in app.state.price_counters.items():
                    async with app.state.mint_states_lock:
                        if app.state.mint_states.get(mint_address) == MintState.ACTIVE:
                            prices_by_address[mint_address] = count
            
            # Log detailed metrics periodically
            logger.info(f"System metrics - Queues: Subscription={subscription_queue_size}, "
                      f"Signatures={signatures_queue_size}, Completion={completion_queue_size}, "
                      f"Unsubscribe={unsubscribe_queue_size}")
            logger.info(f"Memory usage: {memory_usage_mb:.2f} MB, Active mint addresses: {active_count}, Pending mint addresses: {pending_count}")
            
            # Check for ignored subscriptions and log them if present
            ignored_subscriptions_count = 0
            if hasattr(app.state, 'ignored_subscription_ids'):
                ignored_subscriptions_count = len(app.state.ignored_subscription_ids)
                if ignored_subscriptions_count > 0:
                    logger.info(f"Ignored subscriptions: {ignored_subscriptions_count} (messages from these subscriptions are dropped)")
            
            # Check for problematic subscriptions
            problematic_subscriptions_count = 0
            if hasattr(app.state, 'problematic_subscriptions'):
                problematic_subscriptions_count = len(app.state.problematic_subscriptions)
                if problematic_subscriptions_count > 0:
                    logger.warning(f"Problematic subscriptions: {problematic_subscriptions_count} (failed to unsubscribe after max retries)")
            
            # Log active mint addresses with price counts
            if prices_by_address:
                addresses_info = ", ".join([f"{addr[-6:]}:{count}" for addr, count in prices_by_address.items()])
                logger.info(f"Price counts for active addresses: {addresses_info}")
            
            # If signatures queue is getting large, that could indicate a bottleneck
            if signatures_queue_size > 100:
                logger.warning(f"Signatures queue is large ({signatures_queue_size} items). Possible processing bottleneck.")
            
            # Potentially trigger garbage collection if memory usage is high
            if memory_usage_mb > 1000:  # Over 1GB
                logger.warning(f"High memory usage detected: {memory_usage_mb:.2f} MB. Triggering garbage collection.")
                gc.collect()
                
            # Keep metrics history manageable (keep only last 100 data points)
            max_history = 100
            for queue_name in processing_metrics['queue_sizes']:
                if len(processing_metrics['queue_sizes'][queue_name]) > max_history:
                    processing_metrics['queue_sizes'][queue_name] = processing_metrics['queue_sizes'][queue_name][-max_history:]
            
            for state in processing_metrics['mint_address_counts']:
                if len(processing_metrics['mint_address_counts'][state]) > max_history:
                    processing_metrics['mint_address_counts'][state] = processing_metrics['mint_address_counts'][state][-max_history:]
            
            if len(processing_metrics['memory_usage']) > max_history:
                processing_metrics['memory_usage'] = processing_metrics['memory_usage'][-max_history:]
                
        except Exception as e:
            logger.error(f"Error in system metrics monitor: {e}", exc_info=True)
            
        await asyncio.sleep(monitor_interval)

async def log_processing_metrics(detection_time, completion_start_time, completion_end_time, mint_address, app):
    """
    Log processing metrics for a completed mint address.
    
    Args:
        detection_time: Time when 100th price was detected
        completion_start_time: Time when completion process started
        completion_end_time: Time when completion process finished
        mint_address: The mint address being processed
        app: The application state
    """
    try:
        total_time = completion_end_time - detection_time
        detection_to_start = completion_start_time - detection_time
        completion_duration = completion_end_time - completion_start_time
        
        # Log detailed metrics for this mint
        logger.debug(f"Processing metrics for {mint_address}:")
        logger.debug(f"  Detection to completion start: {detection_to_start:.2f}s")
        logger.debug(f"  Completion duration: {completion_duration:.2f}s")
        logger.debug(f"  Total processing time: {total_time:.2f}s")
        
        # Log rolling average metrics
        if hasattr(app.state, 'completion_metrics') and app.state.completion_metrics:
            logger.debug(f"Average metrics (last {len(app.state.completion_metrics)} completions):")
            logger.debug(f"  Avg detection to completion start: {avg_detection_to_start:.2f}s")
            logger.debug(f"  Avg completion duration: {avg_completion_duration:.2f}s")
            logger.debug(f"  Avg total processing time: {avg_total_time:.2f}s")
        
        processing_metrics['detection_to_completion_times'].append(detection_to_start)
        processing_metrics['completion_durations'].append(completion_duration)
        processing_metrics['total_processing_times'].append(total_time)
        
        # Keep metrics history manageable
        max_history = 100
        for metric_name in ['detection_to_completion_times', 'completion_durations', 'total_processing_times']:
            if len(processing_metrics[metric_name]) > max_history:
                processing_metrics[metric_name] = processing_metrics[metric_name][-max_history:]
        
        # Calculate averages
        avg_detection_to_start = sum(processing_metrics['detection_to_completion_times']) / len(processing_metrics['detection_to_completion_times'])
        avg_completion_duration = sum(processing_metrics['completion_durations']) / len(processing_metrics['completion_durations'])
        avg_total_time = sum(processing_metrics['total_processing_times']) / len(processing_metrics['total_processing_times'])
        
        logger.debug(f"Processing metrics for {mint_address}:")
        logger.debug(f"  Detection to completion start: {detection_to_start:.2f}s")
        logger.debug(f"  Completion duration: {completion_duration:.2f}s")
        logger.debug(f"  Total processing time: {total_time:.2f}s")
        logger.debug(f"Average metrics (last {len(processing_metrics['total_processing_times'])} completions):")
        logger.debug(f"  Avg detection to completion start: {avg_detection_to_start:.2f}s")
        logger.debug(f"  Avg completion duration: {avg_completion_duration:.2f}s")
        logger.debug(f"  Avg total processing time: {avg_total_time:.2f}s")
    except Exception as e:
        logger.error(f"Error logging processing metrics: {e}", exc_info=True)

async def skip_mint_address(mint_address, app):
    logger.debug(f"Cleaning up {mint_address}")
    try:
        # Use a timeout context to prevent indefinite blocking
        try:
            # Immediate cleanup of the accumulator with timeout
            async with asyncio.timeout(5):  # 5 second timeout
                async with app.state.lock:
                    if mint_address in app.state.accumulators:
                        accumulator = app.state.accumulators[mint_address]
                        transactions_collected = len(accumulator['transactions'])
                        time_elapsed = time.time() - accumulator.get('start_time', 0)
                        logger.debug(f"Removing accumulator for {mint_address}: {transactions_collected} txs in {int(time_elapsed)}s")
                        del app.state.accumulators[mint_address]
                        logger.debug(f"Accumulator for {mint_address} removed from memory")
        except asyncio.TimeoutError:
            logger.error(f"Timeout while accessing accumulator for {mint_address}")
        
        try:
            # Update mint state with timeout
            async with asyncio.timeout(3):  # 3 second timeout
                async with app.state.mint_states_lock:
                    app.state.mint_states[mint_address] = MintState.STOPPED
                    # Also remove from activation times
                    app.state.mint_activation_times.pop(mint_address, None)
                    logger.debug(f"Set STOPPED for {mint_address}")
        except asyncio.TimeoutError:
            logger.error(f"Timeout while updating mint state for {mint_address}")
            
        try:
            # Remove from price counters with timeout
            async with asyncio.timeout(3):  # 3 second timeout
                async with app.state.price_counters_lock:
                    app.state.price_counters.pop(mint_address, None)
                    logger.debug(f"Removed {mint_address} from price counters")
        except asyncio.TimeoutError:
            logger.error(f"Timeout while removing price counter for {mint_address}")
            
        # Remove from detection times if present (no lock required)
        if hasattr(app.state, 'detection_times'):
            app.state.detection_times.pop(mint_address, None)
            
        try:
            # Queue for unsubscription only if it's in subscribed addresses
            async with asyncio.timeout(3):  # 3 second timeout
                async with app.state.subscriptions_lock:
                    if mint_address in app.state.subscribed_addresses:
                        try:
                            # Use non-blocking put_nowait to avoid getting stuck
                            if not app.state.unsubscribe_queue.full():
                                app.state.unsubscribe_queue.put_nowait(mint_address)
                                logger.debug(f"Queued {mint_address} for unsubscription")
                            else:
                                logger.warning(f"Unsubscribe queue is full, couldn't queue {mint_address}")
                        except asyncio.QueueFull:
                            logger.warning(f"Unsubscribe queue is full, couldn't queue {mint_address}")
        except asyncio.TimeoutError:
            logger.error(f"Timeout while accessing subscription data for {mint_address}")
            
        # Cancel any ongoing tasks for this mint address
        try:
            if mint_address in app.state.mint_tasks:
                task = app.state.mint_tasks.pop(mint_address)
                task.cancel()
                logger.debug(f"Task cancelled for {mint_address}")
        except Exception as e:
            logger.error(f"Error cancelling task for {mint_address}: {e}")
            
    except Exception as e:
        logger.error(f"Error skipping {mint_address}: {e}", exc_info=True)
        try:
            # Last resort - ensure the mint is marked as STOPPED even if other cleanup fails
            async with asyncio.timeout(2):  # Short timeout
                async with app.state.mint_states_lock:
                    app.state.mint_states[mint_address] = MintState.STOPPED
        except Exception as inner_e:
            logger.error(f"Failed to set STOPPED for {mint_address}: {inner_e}")

async def log_mint_states(app):
    while True:
        async with app.state.mint_states_lock:
            states_snapshot = app.state.mint_states.copy()
        logger.debug(f"Current mint states: {states_snapshot}")
        await asyncio.sleep(60)

async def verify_tracking_consistency(app):
    """Ensures consistency between different tracking structures."""
    try:
        # Get the mint states first - we'll process each set of addresses separately with appropriate locks
        active_addresses = set()
        pending_addresses = set()
        
        try:
            # Get active and pending addresses with timeout
            async with asyncio.timeout(3):
                async with app.state.mint_states_lock:
                    for addr, state in app.state.mint_states.items():
                        if state == MintState.ACTIVE:
                            active_addresses.add(addr)
                        elif state == MintState.PENDING:
                            pending_addresses.add(addr)
        except asyncio.TimeoutError:
            logger.error("Timeout while getting mint states in verify_tracking_consistency")
            return
            
        # Process active addresses
        for mint_address in list(active_addresses):
            try:
                # Get activation time
                activation_time = 0
                try:
                    async with asyncio.timeout(2):
                        async with app.state.mint_states_lock:
                            activation_time = app.state.mint_activation_times.get(mint_address, time.time())
                except asyncio.TimeoutError:
                    logger.error(f"Timeout getting activation time for {mint_address}")
                    continue
                    
                time_elapsed = time.time() - activation_time
                
                if time_elapsed > 30:
                    # Check each tracking structure separately to avoid deadlocks
                    in_price_counter = False
                    in_subscribed = False
                    in_accumulator = False
                    
                    # Check price counters
                    try:
                        async with asyncio.timeout(2):
                            async with app.state.price_counters_lock:
                                in_price_counter = mint_address in app.state.price_counters
                                price_count = app.state.price_counters.get(mint_address, 0) if in_price_counter else 0
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout checking price counters for {mint_address}")
                    
                    # Check subscriptions
                    try:
                        async with asyncio.timeout(2):
                            async with app.state.subscriptions_lock:
                                in_subscribed = mint_address in app.state.subscribed_addresses
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout checking subscriptions for {mint_address}")
                    
                    # Check accumulators
                    try:
                        async with asyncio.timeout(2):
                            async with app.state.lock:
                                in_accumulator = mint_address in app.state.accumulators
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout checking accumulators for {mint_address}")
                    
                    # Handle the case where the address is missing from critical structures
                    if not (in_price_counter or in_accumulator or in_subscribed):
                        logger.debug(f"Consistency issue: {mint_address} is ACTIVE but not in tracking structures. Marking as STOPPED.")
                        try:
                            async with asyncio.timeout(2):
                                async with app.state.mint_states_lock:
                                    app.state.mint_states[mint_address] = MintState.STOPPED
                                    app.state.mint_activation_times.pop(mint_address, None)
                        except asyncio.TimeoutError:
                            logger.error(f"Timeout while marking {mint_address} as STOPPED")
                    
                    # Handle the case where address has prices but no accumulator
                    if in_price_counter and not in_accumulator and time_elapsed > 60:
                        logger.debug(f"Detected inconsistency: {mint_address} has {price_count} prices, active for {int(time_elapsed)}s, but no accumulator. Cleaning up.")
                        # Use an independent task to avoid blocking the monitoring thread
                        asyncio.create_task(skip_mint_address(mint_address, app))
                        continue
                        
                    # Handle the case where address has been active too long
                    if time_elapsed > 600:
                        logger.debug(f"Mint {mint_address} has been active for {int(time_elapsed)}s which exceeds the maximum timeout. Force cleaning up.")
                        # Use an independent task to avoid blocking the monitoring thread
                        asyncio.create_task(skip_mint_address(mint_address, app))
                        continue
            except Exception as e:
                logger.error(f"Error processing active address {mint_address}: {e}")
                
        # Process pending addresses
        for mint_address in list(pending_addresses):
            try:
                # Get pending time
                pending_time = 0
                try:
                    async with asyncio.timeout(2):
                        async with app.state.mint_states_lock:
                            pending_time = app.state.mint_activation_times.get(mint_address, time.time())
                except asyncio.TimeoutError:
                    logger.error(f"Timeout getting pending time for {mint_address}")
                    continue
                    
                time_elapsed = time.time() - pending_time
                
                if time_elapsed > 30:
                    # Check if it's in the subscription process
                    in_subscription_process = False
                    try:
                        async with asyncio.timeout(2):
                            async with app.state.subscriptions_lock:
                                for request_id, (addr, _) in app.state.pending_subscriptions.items():
                                    if addr == mint_address:
                                        in_subscription_process = True
                                        break
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout checking subscription process for {mint_address}")
                        continue
                    
                    if not in_subscription_process:
                        logger.debug(f"Consistency issue: {mint_address} is PENDING but not in subscription process. Marking as STOPPED.")
                        try:
                            async with asyncio.timeout(2):
                                async with app.state.mint_states_lock:
                                    app.state.mint_states[mint_address] = MintState.STOPPED
                                    app.state.mint_activation_times.pop(mint_address, None)
                        except asyncio.TimeoutError:
                            logger.error(f"Timeout while marking pending {mint_address} as STOPPED")
            except Exception as e:
                logger.error(f"Error processing pending address {mint_address}: {e}")
                
    except Exception as e:
        logger.error(f"Error in tracking consistency check: {e}", exc_info=True)

async def run_full_system_inventory(app):
    """Performs a complete inventory check of all system state related to mint addresses."""
    logger.info("Starting full system inventory check task")
    while True:
        try:
            await asyncio.sleep(600)  # Run every 10 minutes
            
            # Skip inventory run if system is overloaded
            if hasattr(app.state, 'system_load') and app.state.system_load.get('high_load', False):
                logger.debug("System under high load, skipping full inventory check")
                continue
                
            logger.debug("Running full system inventory check")
            
            # Get all mint addresses from each tracking structure separately to avoid deadlocks
            active_mints = set()
            pending_mints = set()
            accumulator_mints = set()
            subscribed_mints = set()
            subscription_mints = set()
            price_counter_mints = set()
            
            # Get active and pending mints
            try:
                async with asyncio.timeout(3):
                    async with app.state.mint_states_lock:
                        active_mints = {addr for addr, state in app.state.mint_states.items() 
                                       if state == MintState.ACTIVE}
                        pending_mints = {addr for addr, state in app.state.mint_states.items()
                                        if state == MintState.PENDING}
            except asyncio.TimeoutError:
                logger.error("Timeout getting mint states in inventory check")
                continue
            
            # Get accumulator mints
            try:
                async with asyncio.timeout(3):
                    async with app.state.lock:
                        accumulator_mints = set(app.state.accumulators.keys())
            except asyncio.TimeoutError:
                logger.error("Timeout getting accumulators in inventory check")
            
            # Get subscription data
            try:
                async with asyncio.timeout(3):
                    async with app.state.subscriptions_lock:
                        subscribed_mints = set(app.state.subscribed_addresses)
                        subscription_mints = set(app.state.subscription_ids.keys())
            except asyncio.TimeoutError:
                logger.error("Timeout getting subscription data in inventory check")
            
            # Get price counter mints
            try:
                async with asyncio.timeout(3):
                    async with app.state.price_counters_lock:
                        price_counter_mints = set(app.state.price_counters.keys())
            except asyncio.TimeoutError:
                logger.error("Timeout getting price counters in inventory check")
            
            # Track mints that need to be cleaned up to avoid repeated skip_mint_address calls
            mints_to_cleanup = set()
            
            # Check for inconsistencies with active mints
            for mint_address in active_mints:
                if mint_address in mints_to_cleanup:
                    continue
                    
                missing_from = []
                
                if mint_address not in accumulator_mints:
                    missing_from.append("accumulators")
                
                if mint_address not in subscribed_mints:
                    missing_from.append("subscribed_addresses")
                    
                if mint_address not in subscription_mints:
                    missing_from.append("subscription_ids")
                    
                if mint_address not in price_counter_mints:
                    missing_from.append("price_counters")
                
                if missing_from:
                    logger.debug(f"Mint {mint_address} is ACTIVE but missing from: {', '.join(missing_from)}")
                    mints_to_cleanup.add(mint_address)
            
            # Check pending mints - should be in pending_subscriptions but not in confirmed structures
            pending_subscription_mints = set()
            try:
                async with asyncio.timeout(3):
                    async with app.state.subscriptions_lock:
                        for request_id, (addr, _) in app.state.pending_subscriptions.items():
                            pending_subscription_mints.add(addr)
            except asyncio.TimeoutError:
                logger.error("Timeout getting pending subscriptions in inventory check")
            
            for mint_address in pending_mints:
                if mint_address in mints_to_cleanup:
                    continue
                    
                # Check if it's in the pending_subscriptions
                if mint_address not in pending_subscription_mints:
                    logger.debug(f"Mint {mint_address} is PENDING but not in pending_subscriptions")
                    mints_to_cleanup.add(mint_address)
                
                # Check if it's incorrectly in confirmed structures
                incorrect_in = []
                if mint_address in subscription_mints:
                    incorrect_in.append("subscription_ids")
                
                if incorrect_in:
                    logger.debug(f"Mint {mint_address} is PENDING but incorrectly in: {', '.join(incorrect_in)}")
                    # This is an inconsistency - the address should be ACTIVE not PENDING
                    try:
                        async with asyncio.timeout(2):
                            async with app.state.mint_states_lock:
                                app.state.mint_states[mint_address] = MintState.ACTIVE
                                logger.debug(f"Fixed state inconsistency: Changed {mint_address} from PENDING to ACTIVE")
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout fixing state inconsistency for {mint_address}")
            
            # Clean up non-active mints from all structures (belt and suspenders)
            all_tracked_mints = (accumulator_mints | subscribed_mints | 
                                 subscription_mints | price_counter_mints)
            
            valid_mint_set = active_mints | pending_mints
            
            for mint_address in all_tracked_mints:
                if mint_address in mints_to_cleanup:
                    continue
                    
                if mint_address not in valid_mint_set:
                    logger.debug(f"Found non-active/non-pending mint {mint_address} in tracking structures. Cleaning up.")
                    mints_to_cleanup.add(mint_address)
            
            # Perform cleanup for all identified problematic mints
            for mint_address in mints_to_cleanup:
                # Use an independent task for each cleanup to avoid blocking
                asyncio.create_task(skip_mint_address(mint_address, app))
                
                # Add small delay between cleanup tasks to avoid overwhelming the system
                await asyncio.sleep(0.1)
                
            logger.info(f"Inventory check complete. {len(active_mints)} active mints, {len(pending_mints)} pending mints verified, {len(mints_to_cleanup)} issues addressed.")
                
        except asyncio.CancelledError:
            logger.info("Full system inventory check task cancelled")
            break
        except Exception as e:
            logger.error(f"Error in system inventory check: {e}", exc_info=True)

async def report_system_metrics(app):
    """Report current system metrics to logs."""
    try:
        # Get queue sizes
        subscription_queue_size = app.state.subscription_queue.qsize()
        signatures_queue_size = app.state.signatures_queue.qsize() if hasattr(app.state, 'signatures_queue') else 0
        completion_queue_size = app.state.completion_queue.qsize() if hasattr(app.state, 'completion_queue') else 0
        unsubscribe_queue_size = app.state.unsubscribe_queue.qsize() if hasattr(app.state, 'unsubscribe_queue') else 0
        
        # Log queue sizes
        logger.info(f"System metrics - Queues: Subscription={subscription_queue_size}, Signatures={signatures_queue_size}, Completion={completion_queue_size}, Unsubscribe={unsubscribe_queue_size}")
        
        # Get memory usage
        memory_usage = psutil.Process().memory_info().rss / (1024 * 1024)  # Convert to MB
        
        # Count active mint addresses
        active_mint_count = 0
        pending_mint_count = 0
        async with app.state.mint_states_lock:
            for mint_address, state in app.state.mint_states.items():
                if state == MintState.ACTIVE:
                    active_mint_count += 1
                elif state == MintState.PENDING:
                    pending_mint_count += 1
        
        # Count ignored subscriptions
        ignored_subscriptions_count = 0
        if hasattr(app.state, 'ignored_subscription_ids'):
            ignored_subscriptions_count = len(app.state.ignored_subscription_ids)
        
        # Log memory and mint counts
        logger.info(f"Memory usage: {memory_usage:.2f} MB, Active mint addresses: {active_mint_count}, Pending mint addresses: {pending_mint_count}")
        
        # Log ignored subscriptions if any exist
        if ignored_subscriptions_count > 0:
            logger.info(f"Ignored subscriptions: {ignored_subscriptions_count} (messages from these subscriptions are dropped)")
        
        # Report price counters for active mints
        if hasattr(app.state, 'price_counters'):
            # Get prices for active addresses only
            active_prices = {}
            async with app.state.price_counters_lock:
                for mint, state in app.state.mint_states.items():
                    if state == MintState.ACTIVE and mint in app.state.price_counters:
                        # Use last 4 chars of mint address as the key for more concise logging
                        key = mint[-6:] if len(mint) >= 6 else mint
                        active_prices[key] = app.state.price_counters[mint]
            
            if active_prices:
                # Sort by key for consistent output
                sorted_active_prices = dict(sorted(active_prices.items()))
                # Format the output string
                prices_str = ", ".join([f"{key}:{count}" for key, count in sorted_active_prices.items()])
                logger.info(f"Price counts for active addresses: {prices_str}")
    except Exception as e:
        logger.error(f"Error in system metrics reporting: {e}", exc_info=True)