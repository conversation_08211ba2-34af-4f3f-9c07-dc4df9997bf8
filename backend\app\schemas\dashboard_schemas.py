from pydantic import BaseModel, ConfigDict
from typing import List, Optional
from datetime import datetime

from app.schemas.bot_schemas import BotStateResponse
from app.schemas.trade_schemas import TradeItem


class HoldingItem(BaseModel):
    model_config = ConfigDict(from_attributes=True)  # If using Pydantic v2+
    # class Config: orm_mode = True # If using Pydantic v1

    token_mint: str
    amount_held: float
    average_buy_price_sol: float
    current_price_sol: Optional[float] = None
    live_pnl_percent: Optional[float] = None  # Calculated field
    last_acquired_at: datetime
    updated_at: datetime
    monitoring_active: bool = True  # Default to True
    stagnant_check_count: int = 0   # Default to 0
    last_successful_price_fetch: Optional[datetime] = None


class DashboardDataResponse(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    bot_state: BotStateResponse
    holdings: List[HoldingItem] = []
    recent_trades: List[TradeItem] = []
    # Add other summary stats later if needed (e.g., total PNL) 