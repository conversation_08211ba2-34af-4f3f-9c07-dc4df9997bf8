'use client'

import React from 'react'
import { WalletContextProvider } from "@/components/wallet-provider"
import { AuthProvider } from "@/components/auth-context"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "sonner"

export default function ClientProviders({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} disableTransitionOnChange forcedTheme="dark">
      <WalletContextProvider>
        <AuthProvider>
          <div className="relative min-h-screen">
            {/* Background Elements */}
            <div className="fixed inset-0 -z-10 overflow-hidden">
              {/* Empty background without animated floating elements */}
            </div>
            
            {/* Main Content */}
            <main className="min-h-screen relative z-0">
              {children}
            </main>
          </div>
          <Toaster position="top-right" richColors closeButton />
        </AuthProvider>
      </WalletContextProvider>
    </ThemeProvider>
  )
} 