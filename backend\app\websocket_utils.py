import asyncio
import logging
import json
from fastapi import FastAPI, WebSocket

logger = logging.getLogger(__name__)

async def send_proposal_to_user(app: FastAPI, user_wallet: str, proposal_dict: dict) -> bool:
    """Sends a transaction proposal JSON to a specific user's WebSocket."""
    websocket_to_send: WebSocket | None = None
    try:
        async with app.state.connections_lock:
            websocket_to_send = app.state.active_connections.get(user_wallet)

        if websocket_to_send:
            logger.info(f"[{user_wallet}] Sending proposal ({proposal_dict.get('type')}) for {proposal_dict.get('mint_address')} via WebSocket.")
            message_payload = {
                "message_type": "transaction_proposal",
                "data": proposal_dict
            }
            await websocket_to_send.send_json(message_payload)
            logger.debug(f"[{user_wallet}] Proposal sent successfully.")
            return True
        else:
            logger.warning(f"[{user_wallet}] No active WebSocket found. Cannot send proposal: {proposal_dict}")
            return False
    except Exception as ws_err:
        logger.error(f"[{user_wallet}] Failed to send proposal via WebSocket: {ws_err}", exc_info=True)
        # Attempt to close broken socket?
        if websocket_to_send:
            try: await websocket_to_send.close()
            except: pass
        # Remove potentially broken connection
        async with app.state.connections_lock:
            app.state.active_connections.pop(user_wallet, None)
        return False 