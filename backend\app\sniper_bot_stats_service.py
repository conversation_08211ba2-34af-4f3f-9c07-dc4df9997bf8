import asyncio
import logging
import json
import os
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import FastAP<PERSON>

from app.database import async_session_factory
from app.db_models import SniperBotSimulation
from solbot.config import RISK_LADDER_POOL_SIZE, _data_dir

logger = logging.getLogger(__name__)

# Path for storing sniper bot simulation stats
SNIPER_BOT_STATS_FILE_PATH = os.path.join(_data_dir, 'sniper_bot_simulation_stats.json')

async def calculate_and_store_sniper_bot_stats_task(app: FastAPI):
    """
    Periodically calculates Sniper Bot Risk Ladder statistics from the N most recent resolved simulations
    and stores them in a JSON file, similar to the live signals risk ladder.
    """
    logger.debug("SniperBotStatsCalculatorService: Starting service.")
    
    # Ensure the directory for the stats file exists
    stats_dir = os.path.dirname(SNIPER_BOT_STATS_FILE_PATH)
    if not os.path.exists(stats_dir):
        try:
            os.makedirs(stats_dir, exist_ok=True)
            logger.debug(f"SniperBotStatsCalculatorService: Created directory for stats file: {stats_dir}")
        except Exception as e:
            logger.error(f"SniperBotStatsCalculatorService: Failed to create directory {stats_dir}: {e}")
            return

    # Initial short delay before first run
    await asyncio.sleep(20)

    while True:
        try:
            logger.debug("SniperBotStatsCalculatorService: Starting statistics calculation cycle.")
            resolved_rois: List[float] = []
            
            async with async_session_factory() as db:
                stmt = select(SniperBotSimulation.actual_max_roi).where(
                    SniperBotSimulation.is_resolved == True,
                    SniperBotSimulation.actual_max_roi.is_not(None)  # Only include if ROI was determined
                ).order_by(SniperBotSimulation.resolution_timestamp.desc()).limit(RISK_LADDER_POOL_SIZE)
                
                result = await db.execute(stmt)
                resolved_rois = result.scalars().all()

            if not resolved_rois:
                logger.debug(f"SniperBotStatsCalculatorService: No resolved simulations with ROI found in the pool (size {RISK_LADDER_POOL_SIZE}). Skipping stats update.")
                # Write default/empty stats to the file
                default_stats = {
                    "rug_pull_pct": 0.0,
                    "small_gains_pct": 0.0,
                    "good_profit_pct": 0.0,
                    "big_gains_pct": 0.0,
                    "to_the_moon_pct": 0.0,
                    "total_tokens": 0,
                    "last_updated_utc": datetime.utcnow().isoformat()
                }
                try:
                    with open(SNIPER_BOT_STATS_FILE_PATH, 'w') as f:
                        json.dump(default_stats, f, indent=4)
                    logger.debug(f"SniperBotStatsCalculatorService: Wrote default stats to {SNIPER_BOT_STATS_FILE_PATH}")
                except Exception as e:
                    logger.error(f"SniperBotStatsCalculatorService: Error writing default stats file: {e}")
                
                await asyncio.sleep(30)  # Wait before next cycle
                continue

            pool_actual_size = len(resolved_rois)
            logger.debug(f"SniperBotStatsCalculatorService: Calculating stats from a pool of {pool_actual_size} resolved simulations.")

            # Define max_roi value boundaries for each ladder rung (profit factor)
            # These match the live signals risk ladder categories exactly
            val_rug_pull_upper_bound = 0.001  # Captures actual_max_roi = 0.0 or very near for -100% profit
            val_small_gains_upper_bound = 1.0  # Up to (but not including) +100% profit
            val_good_profit_upper_bound = 2.0  # Up to (but not including) +200% profit
            val_big_gains_upper_bound = 5.0    # Up to (but not including) +500% profit
            # TO THE MOON is >= 5.0 (+500% profit or more)

            rug_pull_count = sum(1 for roi in resolved_rois if roi < val_rug_pull_upper_bound)
            small_gains_count = sum(1 for roi in resolved_rois if val_rug_pull_upper_bound <= roi < val_small_gains_upper_bound)
            good_profit_count = sum(1 for roi in resolved_rois if val_small_gains_upper_bound <= roi < val_good_profit_upper_bound)
            big_gains_count = sum(1 for roi in resolved_rois if val_good_profit_upper_bound <= roi < val_big_gains_upper_bound)
            to_the_moon_count = sum(1 for roi in resolved_rois if roi >= val_big_gains_upper_bound)

            current_stats = {
                "rug_pull_pct": (rug_pull_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "small_gains_pct": (small_gains_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "good_profit_pct": (good_profit_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "big_gains_pct": (big_gains_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "to_the_moon_pct": (to_the_moon_count / pool_actual_size) * 100 if pool_actual_size > 0 else 0.0,
                "total_tokens": pool_actual_size,
                "last_updated_utc": datetime.utcnow().isoformat()
            }

            # Store these stats to the JSON file
            try:
                with open(SNIPER_BOT_STATS_FILE_PATH, 'w') as f:
                    json.dump(current_stats, f, indent=4)
                logger.debug(f"SniperBotStatsCalculatorService: Updated sniper bot stats in {SNIPER_BOT_STATS_FILE_PATH}")
                logger.debug(f"SniperBotStatsCalculatorService: Current Stats: {current_stats}")
            except Exception as e:
                logger.error(f"SniperBotStatsCalculatorService: Error writing sniper bot stats file: {e}")

            await asyncio.sleep(30)  # Calculate and store stats every 30 seconds (same as live signals)
        except asyncio.CancelledError:
            logger.info("SniperBotStatsCalculatorService task cancelled.")
            break
        except Exception as e:
            logger.error(f"SniperBotStatsCalculatorService: Error in main loop: {e}", exc_info=True)
            await asyncio.sleep(60)  # Wait longer after an error before retrying


async def start_sniper_bot_stats_service(app: FastAPI):
    """Start the sniper bot stats service as a background task."""
    await calculate_and_store_sniper_bot_stats_task(app)
