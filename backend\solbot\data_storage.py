import os
import json
import pandas as pd
import aiofiles
import logging
from .utilities import ensure_file_exists
from .config import MERGED_TRANSACTIONS_FILE

logger = logging.getLogger(__name__)

async def save_earliest_transactions(output_file, new_transactions, app):
    ensure_file_exists(output_file)
    async with app.state.lock:
        try:
            if os.path.exists(output_file):
                async with aiofiles.open(output_file, 'r') as f:
                    try:
                        existing_data = json.loads(await f.read())
                    except json.JSONDecodeError:
                        existing_data = {}
            else:
                existing_data = {}
            existing_data.update(new_transactions)
            async with aiofiles.open(output_file, 'w') as f:
                await f.write(json.dumps(existing_data, indent=4))
            logger.debug(f"Data saved to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save earliest transactions: {e}")
            raise

async def create_earliest_transactions_df(app):
    file_path = MERGED_TRANSACTIONS_FILE
    ensure_file_exists(file_path)
    try:
        async with app.state.lock:
            if os.path.getsize(file_path) == 0:
                logger.debug(f"CSV '{file_path}' is empty. Returning empty DataFrame.")
                return pd.DataFrame(columns=['mint_address'])  # Include mint_address column in empty DataFrame
            
            df = pd.read_csv(file_path)
            logger.debug(f"Loaded transactions from {file_path}")
            
            # Verify mint_address column exists and has valid values
            if 'mint_address' not in df.columns:
                logger.warning(f"CSV '{file_path}' does not have a mint_address column. This is required.")
                df['mint_address'] = 'Missing_Mint_Address'
            
            # Check for and handle missing mint_address values
            mask_missing = df['mint_address'].isna() | (df['mint_address'] == '')
            if mask_missing.any():
                logger.warning(f"Found {mask_missing.sum()} rows with missing mint_address. Setting placeholder.")
                df.loc[mask_missing, 'mint_address'] = 'Missing_Mint_Address'
            
            # Log mint address information
            if not df.empty:
                unique_mints = df['mint_address'].nunique()
                logger.debug(f"Loaded DataFrame has {unique_mints} unique mint addresses")
                if unique_mints > 0:
                    sample_mint = df['mint_address'].iloc[0]
                    logger.debug(f"Sample mint address: {sample_mint}")
            
            return df
    except Exception as e:
        logger.error(f"Error loading DataFrame from {file_path}: {e}")
        # Return an empty DataFrame with mint_address column rather than raising an exception
        return pd.DataFrame(columns=['mint_address'])

async def save_dataframe(app, df, output_file):
    ensure_file_exists(output_file)
    async with app.state.lock:
        try:
            # Check if 'mint_address' column exists in the dataframe
            if 'mint_address' not in df.columns:
                logger.warning("DataFrame missing 'mint_address' column. Cannot save without mint_address.")
                return

            # Remove any rows with null mint_address
            rows_before = len(df)
            df = df.dropna(subset=['mint_address'])
            rows_after = len(df)
            if rows_before > rows_after:
                logger.warning(f"Dropped {rows_before - rows_after} rows with null mint_address values")
            
            # Make mint_address the first column for better visibility
            cols = df.columns.tolist()
            if 'mint_address' in cols:
                cols.remove('mint_address')
                cols = ['mint_address'] + cols
                df = df[cols]
            
            if os.path.exists(output_file):
                try:
                    existing_df = pd.read_csv(output_file)
                    logger.debug(f"Loaded existing data with {len(existing_df)} rows from {output_file}")
                except pd.errors.EmptyDataError:
                    logger.debug(f"'{output_file}' is empty or invalid. Starting fresh.")
                    existing_df = pd.DataFrame()
            else:
                existing_df = pd.DataFrame()
            
            # Log information about mint addresses
            if not df.empty:
                unique_mints_new = df['mint_address'].nunique()
                logger.debug(f"New data has {unique_mints_new} unique mint addresses")
                logger.debug(f"Example mint address in new data: {df['mint_address'].iloc[0]}")
            
            combined_df = pd.concat([existing_df, df], ignore_index=True)
            
            # Ensure mint_address column exists before drop_duplicates
            if 'mint_address' in combined_df.columns:
                combined_df.drop_duplicates(subset='mint_address', keep='last', inplace=True)
                logger.debug(f"Combined DataFrame has {combined_df['mint_address'].nunique()} unique mint addresses")
            else:
                logger.warning("Combined DataFrame missing 'mint_address' column after concatenation")
            
            combined_df.to_csv(output_file, index=False)
            logger.debug(f"DataFrame saved to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save DataFrame: {e}")
            raise

def load_merged_transactions_df(file_path):
    merged_df = pd.read_csv(file_path)
    logger.debug(f"Loaded DataFrame from '{file_path}'. Columns: {merged_df.columns.tolist()}")
    return merged_df