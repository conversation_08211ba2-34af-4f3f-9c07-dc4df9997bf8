import asyncio
import heapq
import logging
import json
import time
import configparser
import pandas as pd
from datetime import datetime
from app.database import async_session_factory
from app.db_models import LiveModelProposals
from .data_fetching import fetch_transaction_detail, fetch_chronological_transactions, fetch_signatures_for_address
from .utilities import get_mint_address_from_transaction
from .accumulator import initialize_accumulator, process_transaction_for_accumulator, build_processed_data_from_accumulator, finalize_accumulator
from .prediction import feature_engineering, predict_internal
from .data_storage import save_dataframe
from app.states import MintState
from .config import MERGED_TRANSACTIONS_FILE, FORCE_BUY_SIGNAL_MVP_BYPASS

logger = logging.getLogger(__name__)

async def start_signature_processors(app, num_workers=4):
    if not hasattr(app.state, 'api_semaphore'):
        app.state.api_semaphore = asyncio.Semaphore(39) #(num_workers * 2)
    worker_tasks = [asyncio.create_task(signature_processor_worker(app, i)) for i in range(num_workers)]
    return worker_tasks

async def signature_processor_worker(app, worker_id):
    logger.info(f"Starting signature processor worker {worker_id}")
    while True:
        try:
            signature = await app.state.signatures_queue.get()
            async with app.state.api_semaphore:
                transaction = await fetch_transaction_detail(app, signature)
                # Check if fetch failed (returned None)
                if transaction is None:
                    logger.warning(f"Worker {worker_id}: Skipping signature {signature} because fetching details failed after retries.")
                    # Crucially, make sure to signal the queue that this item is done processing, even though it failed.
                    app.state.signatures_queue.task_done()
                    continue # Continue to the next signature

                # Original checks for mint address etc. (keep these)
                if not transaction: # This condition might be redundant now, but keep for safety
                    logger.debug(f"Worker {worker_id}: Transaction object is empty for {signature}, though fetch didn't return None.")
                    app.state.signatures_queue.task_done()
                    continue
                mint_address = get_mint_address_from_transaction(transaction)
                if not mint_address:
                    logger.debug(f"Worker {worker_id}: No mint address for {signature}")
                    app.state.signatures_queue.task_done()
                    continue
                async with app.state.mint_states_lock:
                    current_state = app.state.mint_states.get(mint_address)
                    if current_state != MintState.ACTIVE:
                        logger.debug(f"Worker {worker_id}: {mint_address} not active. Skipping {signature}")
                        app.state.signatures_queue.task_done()
                        continue
                slot = transaction.get("slot", 0)
                block_time = transaction.get("blockTime", transaction.get("timestamp", 0))
                logger.debug(f"Transaction {signature} - Slot: {slot}, blockTime: {block_time}")
                if mint_address not in app.state.mint_heaps:
                    app.state.mint_heaps[mint_address] = []
                    app.state.pending_signatures[mint_address] = set()
                    task = asyncio.create_task(
                        process_mint_queue(
                            app.state.mint_heaps[mint_address],
                            app.state.pending_signatures[mint_address],
                            app,
                            mint_address
                        )
                    )
                    app.state.mint_tasks[mint_address] = task
                    logger.debug(f"Worker {worker_id}: Created heap for {mint_address}")
                app.state.pending_signatures[mint_address].add(signature)
                heapq.heappush(app.state.mint_heaps[mint_address], (slot, block_time, signature, transaction))
                logger.debug(f"Worker {worker_id}: Pushed {signature} for {mint_address} to heap.")
                app.state.signatures_queue.task_done() # Ensure task_done is called on success path too
        except asyncio.CancelledError:
            logger.info(f"Worker {worker_id}: Shutting down")
            for task in app.state.mint_tasks.values():
                task.cancel()
            break
        except Exception as e:
            logger.error(f"Worker {worker_id}: Error: {e}", exc_info=True)
            await asyncio.sleep(1)

async def process_signatures(app):
    try:
        from .config import config
        num_workers = config.getint('PROCESSING', 'SIGNATURE_PROCESSORS', fallback=4)
    except (configparser.NoSectionError, configparser.NoOptionError):
        num_workers = 4
        logger.warning("SIGNATURE_PROCESSORS not found in config, using default 4")
    worker_tasks = await start_signature_processors(app, num_workers)
    try:
        await asyncio.gather(*worker_tasks)
    except asyncio.CancelledError:
        logger.info("Signature processing system shutting down")
        for task in worker_tasks:
            task.cancel()
        await asyncio.gather(*worker_tasks, return_exceptions=True)
        raise

async def process_mint_queue(mint_heap, pending_signatures, app, mint_address):
    last_slot = -1
    last_block_time = -1
    try:
        while True:
            try:
                slot, block_time, signature, transaction = heapq.heappop(mint_heap)
                if slot < last_slot or (slot == last_slot and block_time < last_block_time):
                    logger.debug(f"Out-of-order transaction for {mint_address}: slot {slot}, blockTime {block_time}")
                    last_slot = slot
                    last_block_time = block_time
                else:
                    last_slot = slot
                    last_block_time = block_time
                if signature in pending_signatures:
                    if transaction:
                        await process_transaction(transaction, app, signature)
                        logger.debug(f"Processed {signature} for {mint_address}")
                    else:
                        logger.warning(f"Transaction data missing for {signature}")
                pending_signatures.discard(signature)
            except IndexError:
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Error in queue for {mint_address}: {e}", exc_info=True)
                await asyncio.sleep(1)
    except asyncio.CancelledError:
        logger.debug(f"Task cancelled for {mint_address}")
    except Exception as e:
        logger.error(f"Fatal error in queue for {mint_address}: {e}", exc_info=True)

async def process_transaction(transaction, app, signature):
    mint_address = get_mint_address_from_transaction(transaction)
    if not mint_address:
        logger.warning("Transaction has no mint address.")
        return
    slot = transaction.get('slot')
    block_time = transaction.get('blockTime', transaction.get('timestamp', 0))
    logger.debug(f"Processing {signature} - Slot: {slot}, blockTime: {block_time}")
    raw_data = transaction.get('raw_data', {})
    logger.debug(f"Raw Data for {signature}: {json.dumps(raw_data, indent=2)}")
    async with app.state.mint_lock_manager.acquire(mint_address):
        # Check if this mint address is already finalized
        if mint_address in app.state.accumulators and app.state.accumulators[mint_address].get('finalized', False):
            logger.debug(f"Skipping transaction for {mint_address} as accumulator is already finalized.")
            return False
            
        if mint_address not in app.state.accumulators:
            app.state.accumulators[mint_address] = initialize_accumulator()
            logger.debug(f"Initialized accumulator for {mint_address}")
        accumulator = app.state.accumulators[mint_address]
        process_result = process_transaction_for_accumulator(transaction, mint_address, accumulator)
        transactions_collected = len(accumulator.get('transactions', []))
        prices_collected = len(accumulator.get('prices', []))
        desired_transactions = 100
        time_elapsed = time.time() - accumulator.get('start_time', 0)
        logger.debug(f"Transaction {transactions_collected}, Prices {prices_collected}/{desired_transactions} for {mint_address}, Time: {int(time_elapsed)}s")
        
        # Special case: process_result == 2 means we hit exactly 100 prices - now just log, don't trigger completion
        if process_result == 2:
            logger.info(f"Exactly 100 prices detected via accumulator for {mint_address}. OLD PATH - COMPLETION NOW TRIGGERED BY WEBSOCKET.")
            return False  # Changed from True to False since we're not triggering completion here
        # Normal case: process_result is True (1) means we have more than 100 prices - now just log, don't trigger completion
        elif process_result:
            logger.info(f">100 prices detected via accumulator for {mint_address}. OLD PATH - COMPLETION NOW TRIGGERED BY WEBSOCKET.")
            return False  # Changed from True to False since we're not triggering completion here
        return False

async def complete_data_collection(mint_address, accumulator, app):
    """
    Complete data collection for a mint address by fetching historical transactions.
    
    Process flow for collecting chronological transactions:
    1. First fetch: Get the oldest signatures, reverse them, and fetch those transactions
    2. If more needed: Fetch NEWER signatures using until_signature parameter
    3. Process all transactions in order from oldest to newest
    
    This ensures we collect the earliest 100 prices in the correct chronological order,
    which is critical for proper prediction.
    
    This function can be called from:
    - The normal processing pipeline when 100 prices are detected there
    - The high-priority completion processor when 100 prices are detected directly in websocket handler
    """
    # Add coordination to prevent duplicate processing
    async with app.state.lock:
        # Initialize the collection if it doesn't exist
        if not hasattr(app.state, 'completion_in_progress'):
            app.state.completion_in_progress = set()
            
        # Check if this mint is already being processed
        if mint_address in app.state.completion_in_progress:
            logger.info(f"Completion already in progress for {mint_address}, skipping duplicate request")
            return False
            
        # Mark this mint as being processed
        app.state.completion_in_progress.add(mint_address)
    
    try:
        logger.debug(f"Starting completion for {mint_address}")
        completion_start_time = time.time()
        detection_time = None
        
        # Record start of completion for metrics
        if hasattr(app.state, 'detection_times') and mint_address in app.state.detection_times:
            detection_time = app.state.detection_times[mint_address]
            delay = completion_start_time - detection_time
            logger.debug(f"Completion for {mint_address} starting {delay:.2f}s after 100th price detection")
        
        # Fetch the actual first 100 transactions in true chronological order using the signature-based approach
        logger.debug(f"Fetching chronological transactions for {mint_address} to collect 100 prices")
        from .data_fetching import fetch_chronological_transactions, fetch_signatures_for_address
        chronological_transactions, all_processed = await fetch_chronological_transactions(app, mint_address, required_prices=100)
        
        if chronological_transactions:
            # Use the chronologically ordered transactions - NEVER use real-time transactions for feature calculation
            logger.debug(f"Processing {len(chronological_transactions)} chronological transactions for {mint_address}")
            
            # Create a new accumulator with chronological transactions
            historical_accumulator = initialize_accumulator()
            historical_accumulator['start_time'] = accumulator.get('start_time', time.time())
            
            # Process each chronological transaction through the accumulator
            for tx in chronological_transactions:
                process_transaction_for_accumulator(tx, mint_address, historical_accumulator)
            
            # Only finalize the historical accumulator once, after all transactions are processed
            # This ensures we process all transactions in order and prepare the data correctly
            # without excessive logging from repeated finalizations
            finalize_accumulator(historical_accumulator, mint_address)
            
            # Check how many prices we collected
            price_count = len(historical_accumulator.get('prices', []))
            transaction_count = len(chronological_transactions)
            required_prices = 100
            logger.debug(f"Collected {price_count}/{required_prices} prices from {transaction_count} transactions for {mint_address}.")
            
            # Process the historical data
            processed_data = build_processed_data_from_accumulator(historical_accumulator, mint_address)
            
            if processed_data:
                # Run the global prediction flow with the processed data
                logger.debug(f"Triggering run_global_prediction for {mint_address} from complete_data_collection.")
                await run_global_prediction(mint_address, app, processed_data)
            else:
                logger.warning(f"Could not build data for {mint_address} after historical fetch.")
        else:
            # NEVER fall back to real-time transactions - log error and exit
            logger.error(f"Failed to fetch chronological transactions for {mint_address}. Aborting processing without fallback.")
        
        # Log completion duration
        completion_end_time = time.time()
        completion_duration = completion_end_time - completion_start_time
        logger.debug(f"Completed {mint_address}: Duration: {int(completion_duration)}s")
        
        # Log metrics if we have detection time
        if detection_time:
            total_time = completion_end_time - detection_time
            logger.debug(f"Total time from 100th price detection to completion for {mint_address}: {total_time:.2f}s")
            
            # Log detailed metrics
            from .monitoring import log_processing_metrics
            await log_processing_metrics(
                detection_time, 
                completion_start_time, 
                completion_end_time, 
                mint_address,
                app
            )
        
        return True
    except Exception as e:
        logger.error(f"Error in complete_data_collection for {mint_address}: {e}", exc_info=True)
        return False
    finally:
        try:
            # Remove from in-progress set to allow future processing if needed
            async with app.state.lock:
                if hasattr(app.state, 'completion_in_progress'):
                    app.state.completion_in_progress.discard(mint_address)
                    logger.debug(f"Removed {mint_address} from completion in progress tracking")
            
            # Cleanup
            async with app.state.mint_states_lock:
                app.state.mint_states[mint_address] = MintState.STOPPED
                logger.debug(f"Set state to STOPPED for {mint_address}")
                
            # Immediate cleanup: Remove the accumulator from memory instead of just marking it as finalized
            async with app.state.lock:
                if mint_address in app.state.accumulators:
                    logger.debug(f"Removing accumulator for {mint_address} immediately after processing")
                    del app.state.accumulators[mint_address]
                    logger.debug(f"Accumulator for {mint_address} removed from memory")
            
            # Queue for unsubscription
            await app.state.unsubscribe_queue.put(mint_address)
            logger.debug(f"Queued {mint_address} for unsubscription")
            
            # Cancel any ongoing tasks
            if mint_address in app.state.mint_tasks:
                task = app.state.mint_tasks.pop(mint_address)
                task.cancel()
                logger.info(f"Task cancelled for {mint_address}")
        except Exception as e:
            logger.error(f"Error in cleanup for {mint_address}: {e}")

async def run_global_prediction(mint_address, app, processed_data):
    """
    Run the full global prediction sequence for a mint address.
    
    This function contains the complete sequence:
    1. Takes pre-built processed_data directly (built from historical accumulator)
    2. Fetch holder data
    3. Integrate holders into processed_data
    4. Feature engineering
    5. Save DataFrame
    6. Predict
    7. (Stubbed) Broadcast if BUY signal
    
    Args:
        mint_address: The mint address to process
        app: The FastAPI app instance
        processed_data: Pre-processed data built from the historical accumulator
    """
    logger.debug(f"Running global prediction for {mint_address} with provided processed_data.")
    
    # Check if this mint has already been processed to avoid duplicate signals
    # Use a set to track which mint addresses have been processed
    if not hasattr(app.state, 'processed_mint_addresses'):
        app.state.processed_mint_addresses = set()
        
    # If this mint was already processed, skip to avoid duplicate signals
    async with app.state.lock:
        if mint_address in app.state.processed_mint_addresses:
            logger.debug(f"[{mint_address}] Already processed. Skipping to avoid duplicate signals.")
            return None
        else:
            # Mark this mint as processed
            app.state.processed_mint_addresses.add(mint_address)
            # Keep the set size manageable (remove old entries after 1000 mints)
            if len(app.state.processed_mint_addresses) > 1000:
                # Remove oldest entries (would need a more sophisticated approach for production)
                overflow = len(app.state.processed_mint_addresses) - 1000
                for _ in range(overflow):
                    try:
                        app.state.processed_mint_addresses.pop()
                    except:
                        break
    
    # Import necessary functions/constants
    from .prediction import feature_engineering, predict_internal
    from .data_fetching import fetch_holders
    from .data_storage import save_dataframe
    from .config import HOLDERS_LIMIT, MERGED_TRANSACTIONS_FILE, FORCE_BUY_SIGNAL_MVP_BYPASS
    import numpy as np
    
    if not processed_data:
        logger.error(f"[{mint_address}] Received empty processed_data. Cannot proceed.")
        return None
        
    try:
        # Fetch holders data
        logger.debug(f"[{mint_address}] Fetching holders data globally...")
        holders_data = await fetch_holders(mint_address)
        
        # Feature engineering
        df_engineered = feature_engineering(processed_data)
        
        if df_engineered is not None and not df_engineered.empty:
            # Integrate holders data if available
            if holders_data:
                # Add holders data to the DataFrame
                holders_columns = [col for col in df_engineered.columns if col.startswith('holder_')]
                for i, col in enumerate(holders_columns):
                    if i < len(holders_data):
                        # Get the original column dtype
                        column_dtype = df_engineered[col].dtype
                        # Handle the holder data value - pd.to_numeric returns scalars for single values
                        value = holders_data[i]
                        try:
                            # Convert to the appropriate type
                            if isinstance(value, (float, int, str)):
                                # For scalar values, convert directly
                                if column_dtype == 'int64':
                                    converted_value = int(float(value))
                                else:
                                    converted_value = float(value)
                            else:
                                # For non-scalar values, use pandas
                                converted_value = pd.to_numeric(value, errors='coerce').astype(column_dtype)
                            
                            df_engineered.loc[df_engineered['mint_address'] == mint_address, col] = converted_value
                        except (ValueError, TypeError) as e:
                            logger.warning(f"[{mint_address}] Could not convert holder data value '{value}' to {column_dtype}: {e}")
                            # Use a default value or NaN
                            if column_dtype == 'int64':
                                df_engineered.loc[df_engineered['mint_address'] == mint_address, col] = 0
                            else:
                                df_engineered.loc[df_engineered['mint_address'] == mint_address, col] = float('nan')
            else:
                logger.warning(f"[{mint_address}] No holders data fetched for integration.")
            
            # Save data row
            await save_dataframe(app, df_engineered, MERGED_TRANSACTIONS_FILE)
            logger.debug(f"[{mint_address}] Saved data row globally.")
            
            # Run prediction with the engineered dataframe data instead of raw processed_data
            # Extract the data as a dictionary from the engineered dataframe
            engineered_data = df_engineered.loc[df_engineered['mint_address'] == mint_address].iloc[0].to_dict()
            
            # Log feature information for debugging
            logger.debug(f"[{mint_address}] Engineered data contains {len(engineered_data)} features")
            logger.debug(f"[{mint_address}] Key features: {', '.join(sorted(list(engineered_data.keys()))[:10])}...")
            
            if FORCE_BUY_SIGNAL_MVP_BYPASS:
                logger.warning(f"[{mint_address}] MVP BYPASS ENABLED: Hardcoding BUY signal for testing.")
                prediction_result = {
                    "buy_signal": True,
                    "probabilities": {'0': 0.1, '1': 0.9}, # Dummy probabilities for consistency
                    "mint_address": mint_address
                }
            else:
                # Call the actual prediction logic
                logger.debug(f"[{mint_address}] MVP BYPASS DISABLED: Calling actual predict_internal.")
                prediction_result = await predict_internal(engineered_data, thresholds=None, user_wallet="global_model_assessment")
            
            # Process prediction result and broadcast if buy signal
            if prediction_result['buy_signal']:
                logger.debug(f"[{mint_address}] Global prediction is BUY. Validating required data...")
                
                # Define the required fields for filtering
                required_filter_fields = ['unique_wallets_count', 'sol_volume_sum', 'holder_1_perc_of_total_supply']
                
                # First check if processor_data contains holder_1_perc_of_total_supply, if not it must have been added to engineered_data
                if 'holder_1_perc_of_total_supply' not in processed_data and 'holder_1_perc_of_total_supply' in engineered_data:
                    logger.debug(f"[{mint_address}] holder_1_perc_of_total_supply found in engineered data")
                    processed_data['holder_1_perc_of_total_supply'] = engineered_data['holder_1_perc_of_total_supply']
                
                # Strict validation - check if ANY required field is missing
                missing_fields = []
                for field in required_filter_fields:
                    if field not in processed_data or processed_data[field] is None:
                        missing_fields.append(field)
                
                if missing_fields:
                    logger.error(f"[{mint_address}] CRITICAL DATA INTEGRITY ERROR: Missing required fields: {', '.join(missing_fields)}.")
                    logger.error(f"[{mint_address}] Cannot broadcast signal with incomplete data. Trading decisions require 100% data integrity.")
                    return None
                
                # Construct prediction_details dict with verified complete data
                prediction_details = {
                    'probability': prediction_result['probabilities']['1'],
                    'mint_address': mint_address,
                    'unique_wallets_count': processed_data['unique_wallets_count'],
                    'sol_volume_sum': processed_data['sol_volume_sum'],
                    'holder_1_perc_of_total_supply': processed_data['holder_1_perc_of_total_supply']
                }
                
                logger.debug(f"[{mint_address}] All required data validated. Broadcasting with filter values: " +
                             f"unique_wallets={prediction_details['unique_wallets_count']}, " +
                             f"sol_volume_sum={prediction_details['sol_volume_sum']}, " +
                             f"holder_1_perc={prediction_details['holder_1_perc_of_total_supply']}")
                
                # Create LiveModelProposals record for this BUY signal
                try:
                    logger.info(f"[{mint_address}] BUY signal is TRUE. Preparing to record to LiveModelProposals.")
                    async with async_session_factory() as db_session:
                        logger.info(f"[{mint_address}] Successfully created db_session for LiveModelProposals.")
                        model_version_str = "xgboost_v1" + ("_bypass_test" if FORCE_BUY_SIGNAL_MVP_BYPASS else "")
                        
                        logger.info(f"[{mint_address}] Creating LiveModelProposals record with mint: {mint_address}, version: {model_version_str}")

                        new_proposal_record = LiveModelProposals(
                            mint_address=mint_address,
                            proposal_timestamp=datetime.utcnow(),
                            model_version=model_version_str,
                            is_resolved=False,
                            actual_max_roi=None,
                            resolution_timestamp=None
                        )
                        db_session.add(new_proposal_record)
                        logger.info(f"[{mint_address}] Added LiveModelProposals record to session. Attempting commit...")
                        await db_session.commit()
                        await db_session.refresh(new_proposal_record) # Refresh to get ID
                        logger.info(f"[{mint_address}] Successfully committed and recorded new BUY proposal to LiveModelProposals table. ID: {new_proposal_record.id}")
                except Exception as e:
                    logger.error(f"[{mint_address}] CRITICAL DB ERROR recording proposal to LiveModelProposals: {e}", exc_info=True)
                
                # Continue with signal broadcasting
                await app.state.bot_manager.broadcast_buy_signal(mint_address, prediction_details)
            else:
                logger.info(f"[{mint_address}] Global prediction is NOT BUY.")
            
            return prediction_result
        else:
            logger.error(f"[{mint_address}] Feature engineering produced empty or None DataFrame.")
    except Exception as data_err:
        logger.error(f"[{mint_address}] Error during global data processing/saving: {data_err}", exc_info=True)
    
    return None