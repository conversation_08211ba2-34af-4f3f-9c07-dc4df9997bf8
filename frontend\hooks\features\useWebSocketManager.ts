import { useState, useRef, useEffect, useMemo } from "react";
import { useAuth } from "@/components/auth-context";
import { TransactionProposalData, WebSocketMessage } from "@/types/api";

export function useWebSocketManager(
  fetchData: () => Promise<void>,
  onTransactionProposal: (proposalData: TransactionProposalData) => void,
  onServerInfo: (serverInfo: { server_version_id: string }) => void
) {
  const { isAuthenticated, authUserWallet, handleLogout } = useAuth();
  
  // WebSocket states and refs
  const [wsStatus, setWsStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error' | 'unauthenticated'>('disconnected');
  const websocket = useRef<WebSocket | null>(null);
  const connectionAttempts = useRef(0);
  
  // Server down modal state and refs (moved from page.tsx)
  const [showServerDownModal, setShowServerDownModal] = useState<boolean>(false);
  const serverDownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const serverVersionIdRef = useRef<string | null>(null);
  const [isCheckingServer, setIsCheckingServer] = useState<boolean>(false);
  const serverCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to check server and reconnect if possible
  const checkServerAndReconnect = () => {
    // Immediately return if not authenticated
    if (!isAuthenticated) {
      console.log("[Server Check] Skipping server check - user not authenticated");
      return;
    }
    
    setIsCheckingServer(true);
    console.log("[Server Check] Checking if server is back online...");
    
    const wsBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
    const checkUrl = wsBaseUrl + '/api/health';
    
    fetch(checkUrl)
      .then(response => {
        if (response.ok) {
          console.log("[Server Check] Server is back online! Attempting to reconnect...");
          // The WebSocket reconnection logic will handle reconnection internally
          return true;
        } else {
          console.log(`[Server Check] Server check failed with status: ${response.status}`);
          return false;
        }
      })
      .catch(err => {
        console.error("[Server Check] Server still unreachable:", err.message);
        return false;
      })
      .finally(() => {
        setIsCheckingServer(false);
      });
  };

  useEffect(() => {
    // Helper function to check if the WebSocket endpoint is responding
    const checkWebSocketEndpoint = () => {
      const wsBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
      // Convert to HTTP for a regular fetch request to check server availability
      const checkUrl = wsBaseUrl + '/api/health';
      
      console.log("[WS] Checking server availability at:", checkUrl);
      
      // Try a regular fetch to see if the server is responding
      fetch(checkUrl)
        .then(response => {
          console.log("[WS] Server health check response:", response.status);
          return response.ok;
        })
        .catch(err => {
          console.error("[WS] Server unreachable:", err.message);
          return false;
        });
    };

    const connectWebSocket = () => {
      if (websocket.current || wsStatus === 'connecting') {
        console.log("[WS] Connection attempt skipped: Already connected or connecting.");
        return; // Don't reconnect if already connected or trying
      }

      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error("[WS] Cannot connect: Auth token not found.");
        setWsStatus('disconnected');
        return;
      }

      setWsStatus('connecting');
      console.log("[WS] Attempting to connect...");
      connectionAttempts.current += 1;

      // Construct WebSocket URL
      const wsBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
      // Replace http/https with ws/wss
      const wsUrl = wsBaseUrl.replace(/^http/, 'ws') + `/ws?token=${encodeURIComponent(token)}`;
      console.log("[WS] Connecting to:", wsUrl.split('?')[0], "(token redacted)");
      
      // Validate WebSocket URL format
      if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
        console.error("[WS] Invalid WebSocket URL format:", wsUrl.split('?')[0]);
        setWsStatus('error');
        return;
      }

      try {
        const ws = new WebSocket(wsUrl);
        websocket.current = ws; // Store the instance

        ws.onopen = () => {
          console.log("[WS] Connection established successfully!");
          
          // Check if this is a reconnection before resetting the counter
          const isReconnection = connectionAttempts.current > 0;
          
          // Now reset the counter
          connectionAttempts.current = 0;
          
          // Update connection status
          setWsStatus('connected');
          
          // If this is a reconnection, fetch data
          if (isReconnection) {
            console.log("[WS] Reconnection successful, fetching fresh data...");
            fetchData();
          }
        };

        ws.onclose = (event) => {
          const previousStatus = wsStatus;
          websocket.current = null; // Clear the ref
          setWsStatus('disconnected'); // Immediately set status to disconnected
          console.log(`[WS] Connection closed (previous status: ${previousStatus}): Code=${event.code}, Reason=${event.reason || 'No reason provided'}`);
          
          // Check if this was an authentication failure (code 1008 often indicates policy violation like bad token)
          if (event.code === 1008 || event.reason?.toLowerCase().includes('auth') || event.reason?.toLowerCase().includes('token')) {
            console.log("[WS] Connection closed due to authentication issue, logging out:", event.reason);
            handleLogout();
            return;
          }
          
          // Only attempt reconnect if the closure was not intentionally triggered by client (code 1000)
          // and the user is still authenticated
          if (event.code !== 1000 && isAuthenticated) {
            console.log(`[WS] Unexpected closure detected (Code: ${event.code}). Starting reconnection process...`);
            
            // Reconnect Logic - no need to handle the modal here, the wsStatus effect will do that
            // Calculate a delay with exponential backoff, but cap it at 30 seconds
            const delay = Math.min(1000 * Math.pow(2, connectionAttempts.current), 30000);
            connectionAttempts.current += 1;
            console.log(`[WS] Attempting reconnect in ${delay / 1000}s (Attempt ${connectionAttempts.current})`);
            
            // Check server before reconnecting if we've had multiple failures
            if (connectionAttempts.current >= 3) {
              console.log("[WS] Multiple connection failures, checking server availability...");
              checkWebSocketEndpoint();
            }
            
            // Always attempt to reconnect, regardless of how many attempts we've made
            setTimeout(connectWebSocket, delay);
          } else {
            // Handle intentional closure (code 1000) or unauthenticated state
            if (event.code === 1000) {
              console.log("[WS] Clean closure (Code 1000). No automatic reconnection needed.");
            } else if (!isAuthenticated) {
              console.log("[WS] Not attempting to reconnect - user is not authenticated.");
            }
            connectionAttempts.current = 0; // Reset attempts
          }
        };
        
        ws.onmessage = (event) => {
          console.log("[WS] Message received:", event.data);
          try {
            const message: WebSocketMessage = JSON.parse(event.data as string);

            // Handle authentication error messages
            if (message.type === 'error' && (
                message.detail?.toLowerCase().includes('auth') || 
                message.detail?.toLowerCase().includes('token') ||
                message.detail?.toLowerCase().includes('credential')
            )) {
              console.log("[WS] Authentication error received:", message.detail);
              // Close the WebSocket and log out
              if (websocket.current) {
                websocket.current.close();
                websocket.current = null;
              }
              handleLogout();
              return;
            }

            // Handle server_info messages (related to server version)
            if (message.type === 'server_info' && message.server_version_id) {
              console.log("[WS] Server version ID received:", message.server_version_id);
              // Store server version ID in our internal ref
              serverVersionIdRef.current = message.server_version_id;
              // Call onServerInfo callback
              onServerInfo({ server_version_id: message.server_version_id });
            }
            // Handle transaction proposals - check both message_type and type fields
            else if ((message.message_type === 'transaction_proposal' || message.type === 'transaction_proposal') && message.data) {
              console.log("[WS] Transaction proposal received:", message.data);
              onTransactionProposal(message.data as TransactionProposalData);
            }
            // Handle other potential message types if any are expected
            else {
              console.log("[WS] Received unhandled message type or structure:", message);
            }
          } catch (error) {
            console.error("[WS] Error parsing message or handling callback:", error);
          }
        };

        ws.onerror = (error) => {
          console.error("[WS] WebSocket error:", error);
          
          // After an error, if we're still authenticated, check if the token is valid
          if (isAuthenticated) {
            const wsBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
            const checkTokenUrl = wsBaseUrl + '/api/auth/check';
            
            fetch(checkTokenUrl, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            }).then(response => {
              if (response.status === 401) {
                console.log("[WS] Token validation failed, logging out");
                handleLogout();
              }
            }).catch(() => {
              // Network error, server might be down, don't log out
            });
          }
        };
        
      } catch (err) {
        console.error("[WS] Failed to create WebSocket:", err);
        setWsStatus('error');
        websocket.current = null;
        
        // Check server availability on connection creation error
        checkWebSocketEndpoint();
      }
    };

    const disconnectWebSocket = () => {
      if (websocket.current) {
        console.log("[WS] Manually disconnecting...");
        // Set status based on authentication state
        setWsStatus(isAuthenticated ? 'disconnected' : 'unauthenticated');
        
        // Try to send a disconnect message to tell the server to stop the bot
        try {
          if (websocket.current.readyState === WebSocket.OPEN) {
            websocket.current.send(JSON.stringify({
              type: 'user_disconnect',
              message: 'User is closing tab or logging out'
            }));
            console.log("[WS] Sent disconnect message to server");
          }
        } catch (err) {
          console.error("[WS] Error sending disconnect message:", err);
        }
        
        // Close the connection with clean closure code
        websocket.current.close(1000, "User logged out or component unmounted");
        websocket.current = null;
      } else if (!isAuthenticated) {
        // If no websocket but we're logged out, still set the unauthenticated status
        setWsStatus('unauthenticated');
      }
    };

    if (isAuthenticated) {
      console.log("[WS] Auth detected, initiating WebSocket connection.");
      connectWebSocket();
      
      // Add event listener to handle page unload (tab close or refresh)
      const handleBeforeUnload = () => {
        console.log("[WS] Page unloading, sending disconnect to server");
        disconnectWebSocket();
      };
      
      window.addEventListener('beforeunload', handleBeforeUnload);
      
      // Return cleanup function
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        console.log("[WS] Cleanup: Disconnecting WebSocket.");
        disconnectWebSocket();
      };
    } else {
      console.log("[WS] No auth detected, ensuring WebSocket is disconnected.");
      disconnectWebSocket();
      
      // Return empty cleanup function
      return () => {};
    }
  }, [isAuthenticated, authUserWallet, fetchData, handleLogout]); // Added handleLogout to dependencies

  // Effect to handle showing/hiding server down modal based on WebSocket status
  useEffect(() => {
    // When connected or unauthenticated, always hide the modal
    if (wsStatus === 'connected' || wsStatus === 'unauthenticated') {
      if (showServerDownModal) {
        setShowServerDownModal(false);
      }
      if (serverDownTimerRef.current) {
        clearTimeout(serverDownTimerRef.current);
        serverDownTimerRef.current = null;
      }
      return;
    }
    
    // Only show the server down modal if the user is authenticated
    if (!isAuthenticated) {
      if (showServerDownModal) {
        setShowServerDownModal(false);
      }
      if (serverDownTimerRef.current) {
        clearTimeout(serverDownTimerRef.current);
        serverDownTimerRef.current = null;
      }
      return;
    }
    
    // When disconnected, start a timer to show the modal (to avoid flashing for brief disconnections)
    if (wsStatus === 'disconnected' && !showServerDownModal && serverDownTimerRef.current === null) {
      serverDownTimerRef.current = setTimeout(() => {
        // Only show if still disconnected after the delay and user is still authenticated
        if (wsStatus === 'disconnected' && isAuthenticated) {
          setShowServerDownModal(true);
        }
        serverDownTimerRef.current = null;
      }, 2000); // Wait 2 seconds before showing the modal
    }
    
    // Cleanup function
    return () => {
      if (serverDownTimerRef.current) {
        clearTimeout(serverDownTimerRef.current);
        serverDownTimerRef.current = null;
      }
    };
  }, [wsStatus, showServerDownModal, isAuthenticated]);

  // Effect to periodically check server when in disconnected state for a while
  useEffect(() => {
    // Start periodic checks if disconnected state lasts for some time and authenticated
    if (wsStatus === 'disconnected' && isAuthenticated && connectionAttempts.current >= 3) {
      console.log("[Server Check] Starting periodic server availability checks for long disconnection");
      
      // Only start the interval if it's not already running
      if (!serverCheckIntervalRef.current) {
        // Check immediately once
        checkServerAndReconnect();
        
        // Then set up interval - check less frequently after multiple attempts to reduce load
        serverCheckIntervalRef.current = setInterval(() => {
          checkServerAndReconnect();
        }, 15000); // Check every 15 seconds
      }
    } else {
      // Clear interval if connected or not authenticated
      if (serverCheckIntervalRef.current) {
        console.log("[Server Check] Stopping periodic server checks");
        clearInterval(serverCheckIntervalRef.current);
        serverCheckIntervalRef.current = null;
      }
    }

    // Cleanup
    return () => {
      if (serverCheckIntervalRef.current) {
        clearInterval(serverCheckIntervalRef.current);
        serverCheckIntervalRef.current = null;
      }
    };
  }, [wsStatus, isAuthenticated, connectionAttempts.current]);

  // Return the websocket status and server modal state for components to use
  return useMemo(() => ({
    wsStatus,
    showServerDownModal,
    connectionAttemptsCurrent: connectionAttempts.current,
    isCheckingServer
  }), [wsStatus, showServerDownModal, connectionAttempts.current, isCheckingServer]);
} 