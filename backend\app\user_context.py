import asyncio
import time
from asyncio import Queue
import logging

logger = logging.getLogger(__name__)

class UserBotContext:
    def __init__(self, user_wallet: str):
        self.user_wallet: str = user_wallet
        self.incoming_buy_signals: asyncio.Queue = asyncio.Queue()
        self.incoming_sell_signals: asyncio.Queue = asyncio.Queue()
        self.session_predicted_signals: int = 0
        self.session_start_time = time.time()
        
    def reset_session_stats(self):
        self.session_start_time = time.time()
        self.session_predicted_signals = 0  # Ensure this is reset
        logger.info(f"[{self.user_wallet}] Session stats reset for context ID: {id(self)} (Predicted Signals: 0, Start Time: {self.session_start_time}).")
        
    def add_buy_signal(self, signal_payload):
        """Add a buy signal to the incoming buy signals queue"""
        try:
            self.incoming_buy_signals.put_nowait(signal_payload)
            logger.info(f"[{self.user_wallet}] Added buy signal for {signal_payload[0]} to queue")
            return True
        except asyncio.QueueFull:
            logger.warning(f"[{self.user_wallet}] Buy signal queue full, dropping signal for {signal_payload[0]}")
            return False
            
    def add_sell_signal(self, mint_address):
        """Add a sell signal to the incoming sell signals queue"""
        try:
            self.incoming_sell_signals.put_nowait(mint_address)
            logger.info(f"[{self.user_wallet}] Added sell signal for {mint_address} to queue")
            return True
        except asyncio.QueueFull:
            logger.warning(f"[{self.user_wallet}] Sell signal queue full, dropping signal for {mint_address}")
            return False 