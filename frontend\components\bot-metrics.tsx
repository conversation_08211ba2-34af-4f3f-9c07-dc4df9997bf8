"use client";

import React from 'react';

// StatusItem component definition
interface StatusItemProps {
  label: string;
  value: number;
  color: string;
}

function StatusItem({ label, value, color }: StatusItemProps) {
  const getColor = () => {
    switch (color) {
      case "cyan": return "from-cyan-500 to-blue-500";
      case "green": return "from-green-500 to-emerald-500";
      case "blue": return "from-blue-500 to-indigo-500";
      case "purple": return "from-purple-500 to-pink-500";
      default: return "from-cyan-500 to-blue-500";
    }
  }
  return (
    <div>
      <div className="flex items-center justify-between mb-1">
        <div className="text-xs text-slate-400">{label}</div>
        <div className="text-xs text-slate-400">{value}%</div>
      </div>
      <div className="h-1.5 bg-slate-800 rounded-full overflow-hidden">
        <div className={`h-full bg-gradient-to-r ${getColor()} rounded-full`} style={{ width: `${value}%` }}></div>
      </div>
    </div>
  );
}

interface BotMetricsProps {
  profitMetric: number;
  tradeMetric: number;
  volumeMetric: number;
}

const BotMetrics: React.FC<BotMetricsProps> = ({
  profitMetric,
  tradeMetric,
  volumeMetric
}) => {
  return (
    <div className="mt-8 pt-6 border-t border-slate-700/50 space-y-3">
      <div className="text-xs text-slate-500 mb-2 font-mono">BOT METRICS</div>
      <StatusItem label="Profit Ratio" value={profitMetric} color="green" />
      <StatusItem label="Trade Success" value={tradeMetric} color="cyan" />
      <StatusItem label="Volume" value={volumeMetric} color="blue" />
    </div>
  );
};

export default BotMetrics; 